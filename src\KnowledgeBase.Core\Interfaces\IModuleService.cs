using KnowledgeBase.Core.Entities;

namespace KnowledgeBase.Core.Interfaces;

/// <summary>
/// Service interface for module management operations.
/// </summary>
public interface IModuleService
{
    Task<List<Module>> GetAllAsync(bool includeInactive = false, CancellationToken cancellationToken = default);
    Task<Module?> GetByIdAsync(Guid moduleId, CancellationToken cancellationToken = default);
    Task<Module> CreateAsync(Module module, CancellationToken cancellationToken = default);
    Task<Module> UpdateAsync(Module module, CancellationToken cancellationToken = default);
    Task<bool> SetActiveStatusAsync(Guid moduleId, bool isActive, CancellationToken cancellationToken = default);
    Task<int> GetArticleCountAsync(Guid moduleId, CancellationToken cancellationToken = default);
}

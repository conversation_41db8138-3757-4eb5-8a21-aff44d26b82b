using KnowledgeBase.Core.Interfaces;
using KnowledgeBase.Infrastructure.Data;
using KnowledgeBase.Infrastructure.Services;
using KnowledgeBase.Infrastructure.Services.Agents;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace KnowledgeBase.Infrastructure;

/// <summary>
/// Extension methods for registering infrastructure services.
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Add infrastructure services to the DI container.
    /// </summary>
    public static IServiceCollection AddInfrastructure(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Database
        services.AddDbContext<KnowledgeBaseContext>(options =>
            options.UseSqlServer(
                configuration.GetConnectionString("DefaultConnection"),
                sqlOptions =>
                {
                    sqlOptions.EnableRetryOnFailure(maxRetryCount: 3);
                    sqlOptions.CommandTimeout(30);
                }));

        // Core services
        services.AddScoped<IArticleService, ArticleService>();
        services.AddScoped<IModuleService, ModuleService>();
        services.AddScoped<IUserService, UserService>();

        // AI services
        var azureOpenAIEndpoint = configuration["AzureOpenAI:Endpoint"];
        if (!string.IsNullOrEmpty(azureOpenAIEndpoint))
        {
            services.AddSingleton<IAzureOpenAIClient, AzureOpenAIClientWrapper>();
            services.AddScoped<IRouterAgentService, RouterAgentService>();
            services.AddScoped<IEvaluationAgentService, EvaluationAgentService>();
            services.AddScoped<AgentFactory>();
            services.AddScoped<ArticleOrchestrator>();
        }
        else
        {
            services.AddSingleton<IAzureOpenAIClient, MockAzureOpenAIClient>();
        }

        // Search service (use Azure AI Search if configured, otherwise mock)
        var azureSearchEndpoint = configuration["AzureSearch:Endpoint"];
        if (!string.IsNullOrEmpty(azureSearchEndpoint))
        {
            services.AddSingleton<ISearchService, AzureSearchService>();
        }
        else
        {
            services.AddSingleton<ISearchService, MockSearchService>();
        }

        return services;
    }

    /// <summary>
    /// Add infrastructure services with mock AI for development/testing.
    /// </summary>
    public static IServiceCollection AddInfrastructureWithMockAI(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Database
        services.AddDbContext<KnowledgeBaseContext>(options =>
            options.UseSqlServer(
                configuration.GetConnectionString("DefaultConnection"),
                sqlOptions =>
                {
                    sqlOptions.EnableRetryOnFailure(maxRetryCount: 3);
                    sqlOptions.CommandTimeout(30);
                }));

        // Core services
        services.AddScoped<IArticleService, ArticleService>();
        services.AddScoped<IModuleService, ModuleService>();
        services.AddScoped<IUserService, UserService>();

        // Mock AI services for development without Azure OpenAI
        services.AddSingleton<IAzureOpenAIClient, MockAzureOpenAIClient>();
        services.AddScoped<IRouterAgentService, RouterAgentService>();
        services.AddScoped<IEvaluationAgentService, EvaluationAgentService>();
        services.AddScoped<AgentFactory>();
        services.AddScoped<ArticleOrchestrator>();

        // Mock search service for development
        services.AddSingleton<ISearchService, MockSearchService>();

        return services;
    }
}

/// <summary>
/// Mock Azure OpenAI client for development without actual API.
/// </summary>
public class MockAzureOpenAIClient : IAzureOpenAIClient
{
    public Task<ChatCompletionResult> GetChatCompletionAsync(
        string deploymentName,
        string systemPrompt,
        string userMessage,
        float temperature = 0.7f,
        int maxTokens = 2000,
        CancellationToken cancellationToken = default)
    {
        // Return mock classification result
        if (systemPrompt.Contains("content classification"))
        {
            return Task.FromResult(new ChatCompletionResult
            {
                Success = true,
                Content = @"{
                    ""article_type"": ""how_to"",
                    ""confidence"": 0.85,
                    ""reasoning"": ""The content appears to contain step-by-step instructions""
                }",
                PromptTokens = 100,
                CompletionTokens = 50,
                TotalTokens = 150
            });
        }

        // Return mock evaluation result
        if (systemPrompt.Contains("quality assurance"))
        {
            return Task.FromResult(new ChatCompletionResult
            {
                Success = true,
                Content = @"{
                    ""overall_score"": 7.5,
                    ""scores"": {
                        ""completeness"": 8,
                        ""clarity"": 7,
                        ""accuracy"": 8,
                        ""actionability"": 7,
                        ""structure"": 8,
                        ""seo"": 7
                    },
                    ""strengths"": [""Clear structure"", ""Good examples""],
                    ""issues"": [],
                    ""recommended_changes"": [""Add more screenshots""],
                    ""readability_level"": ""intermediate"",
                    ""estimated_read_time_minutes"": 5
                }",
                PromptTokens = 200,
                CompletionTokens = 100,
                TotalTokens = 300
            });
        }

        // Return mock article generation result
        return Task.FromResult(new ChatCompletionResult
        {
            Success = true,
            Content = @"{
                ""title"": ""How to Complete This Task"",
                ""summary"": ""A step-by-step guide to completing the task described in the input."",
                ""content"": {
                    ""prerequisites"": [{""item"": ""Access to the system"", ""type"": ""access""}],
                    ""steps"": [
                        {""step_number"": 1, ""title"": ""Step 1"", ""action"": ""First action"", ""expected_outcome"": ""Expected result"", ""screenshot_recommended"": false}
                    ],
                    ""validation"": {""title"": ""Verify"", ""steps"": [""Check the result""]},
                    ""next_steps"": [""Continue learning""]
                },
                ""tags"": [""tutorial"", ""guide""],
                ""category"": ""configuration"",
                ""estimated_time_minutes"": 10,
                ""difficulty"": ""beginner""
            }",
            PromptTokens = 300,
            CompletionTokens = 200,
            TotalTokens = 500
        });
    }

    public Task<float[]> GetEmbeddingsAsync(string text, CancellationToken cancellationToken = default)
    {
        // Return mock embeddings (1536 dimensions for ada-002)
        var embeddings = new float[1536];
        var random = new Random(text.GetHashCode());
        for (int i = 0; i < embeddings.Length; i++)
        {
            embeddings[i] = (float)(random.NextDouble() * 2 - 1);
        }
        return Task.FromResult(embeddings);
    }
}

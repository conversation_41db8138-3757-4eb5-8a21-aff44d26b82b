@page "/articles/{ArticleId:guid}"
@rendermode InteractiveServer
@using KnowledgeBase.Web.Services
@using KnowledgeBase.Web.Services.Models
@inject KnowledgeBaseApiService ApiService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>@(_article?.Title ?? "Article") - Knowledge Base</PageTitle>

@if (_loading)
{
    <MudProgressLinear Indeterminate="true" Color="Color.Primary" />
}
else if (_article == null)
{
    <MudAlert Severity="Severity.Error">Article not found</MudAlert>
}
else
{
    @* Header *@
    <MudPaper Class="pa-4 mb-4" Elevation="0">
        <div class="d-flex align-start justify-space-between flex-wrap gap-4">
            <div class="flex-grow-1">
                <div class="d-flex align-center gap-2 mb-2">
                    <MudChip T="string" Color="@GetArticleTypeColor(_article.ArticleType)" Size="Size.Small" Icon="@GetArticleTypeIcon(_article.ArticleType)">
                        @_article.ArticleType
                    </MudChip>
                    <MudChip T="string" Color="@GetStatusColor(_article.Status)" Size="Size.Small">
                        @_article.Status
                    </MudChip>
                    @if (!string.IsNullOrEmpty(_article.Difficulty))
                    {
                        <MudChip T="string" Size="Size.Small" Variant="Variant.Outlined">@_article.Difficulty</MudChip>
                    }
                </div>
                <MudText Typo="Typo.h4">@_article.Title</MudText>
                <MudText Typo="Typo.body1" Color="Color.Secondary" Class="mt-2">@_article.Summary</MudText>

                <div class="d-flex align-center gap-4 mt-3">
                    <MudText Typo="Typo.caption">
                        <MudIcon Icon="@Icons.Material.Filled.Visibility" Size="Size.Small" Class="mr-1" />
                        @_article.ViewCount views
                    </MudText>
                    <MudText Typo="Typo.caption">
                        <MudIcon Icon="@Icons.Material.Filled.ThumbUp" Size="Size.Small" Class="mr-1" />
                        @_article.HelpfulCount helpful
                    </MudText>
                    @if (_article.EstimatedTimeMinutes.HasValue)
                    {
                        <MudText Typo="Typo.caption">
                            <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Small" Class="mr-1" />
                            @_article.EstimatedTimeMinutes min read
                        </MudText>
                    }
                    <MudText Typo="Typo.caption">
                        <MudIcon Icon="@Icons.Material.Filled.Update" Size="Size.Small" Class="mr-1" />
                        Updated @_article.LastModifiedDate.ToLocalTime().ToString("MMM dd, yyyy")
                    </MudText>
                </div>
            </div>

            <MudStack Row="true" Spacing="2">
                <MudButton Variant="Variant.Outlined" StartIcon="@Icons.Material.Filled.Edit"
                           Href="@($"/articles/{ArticleId}/edit")">Edit</MudButton>
                @if (_article.Status == "Draft")
                {
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Send"
                               OnClick="@SubmitForReview">Submit for Review</MudButton>
                }
                @if (_article.Status == "InReview")
                {
                    <MudButton Variant="Variant.Filled" Color="Color.Success" StartIcon="@Icons.Material.Filled.Publish"
                               OnClick="@PublishArticle">Publish</MudButton>
                }
                <MudMenu Icon="@Icons.Material.Filled.MoreVert" AnchorOrigin="Origin.BottomLeft">
                    <MudMenuItem Icon="@Icons.Material.Filled.History" OnClick="@ViewHistory">Version History</MudMenuItem>
                    <MudMenuItem Icon="@Icons.Material.Filled.ContentCopy" OnClick="@DuplicateArticle">Duplicate</MudMenuItem>
                    <MudDivider />
                    <MudMenuItem Icon="@Icons.Material.Filled.Archive" OnClick="@ArchiveArticle">Archive</MudMenuItem>
                    <MudMenuItem Icon="@Icons.Material.Filled.Delete" IconColor="Color.Error" OnClick="@DeleteArticle">Delete</MudMenuItem>
                </MudMenu>
            </MudStack>
        </div>
    </MudPaper>

    <MudGrid>
        @* Main Content *@
        <MudItem xs="12" md="8">
            <MudCard>
                <MudCardContent>
                    @RenderArticleContent()
                </MudCardContent>
            </MudCard>

            @* Feedback Section *@
            <MudCard Class="mt-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">Was this article helpful?</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Row="true" Spacing="2" Justify="Justify.Center">
                        <MudButton Variant="@(_feedbackGiven == true ? Variant.Filled : Variant.Outlined)"
                                   Color="Color.Success" StartIcon="@Icons.Material.Filled.ThumbUp"
                                   OnClick="@(() => SubmitFeedback(true))">
                            Yes (@_article.HelpfulCount)
                        </MudButton>
                        <MudButton Variant="@(_feedbackGiven == false ? Variant.Filled : Variant.Outlined)"
                                   Color="Color.Error" StartIcon="@Icons.Material.Filled.ThumbDown"
                                   OnClick="@(() => SubmitFeedback(false))">
                            No (@_article.NotHelpfulCount)
                        </MudButton>
                    </MudStack>
                    @if (_feedbackGiven.HasValue)
                    {
                        <MudTextField @bind-Value="_feedbackComment" Label="Additional feedback (optional)"
                                      Lines="2" Class="mt-4" Variant="Variant.Outlined" />
                        <MudButton Variant="Variant.Text" Color="Color.Primary" Class="mt-2"
                                   OnClick="@SubmitFeedbackComment">Submit Feedback</MudButton>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>

        @* Sidebar *@
        <MudItem xs="12" md="4">
            @* Tags *@
            @if (GetTags().Any())
            {
                <MudCard Class="mb-4">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.subtitle1">Tags</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudChipSet T="string" ReadOnly="true">
                            @foreach (var tag in GetTags())
                            {
                                <MudChip T="string" Size="Size.Small" Variant="Variant.Outlined"
                                         OnClick="@(() => SearchByTag(tag))">@tag</MudChip>
                            }
                        </MudChipSet>
                    </MudCardContent>
                </MudCard>
            }

            @* Article Info *@
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.subtitle1">Article Information</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudList T="string" Dense="true">
                        <MudListItem T="string">
                            <div class="d-flex justify-space-between">
                                <MudText Typo="Typo.body2" Color="Color.Secondary">Category</MudText>
                                <MudText Typo="Typo.body2">@(_article.Category ?? "-")</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem T="string">
                            <div class="d-flex justify-space-between">
                                <MudText Typo="Typo.body2" Color="Color.Secondary">Version</MudText>
                                <MudText Typo="Typo.body2">@_article.Version</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem T="string">
                            <div class="d-flex justify-space-between">
                                <MudText Typo="Typo.body2" Color="Color.Secondary">Created</MudText>
                                <MudText Typo="Typo.body2">@_article.CreatedDate.ToLocalTime().ToString("MMM dd, yyyy")</MudText>
                            </div>
                        </MudListItem>
                        @if (_article.LastReviewedDate.HasValue)
                        {
                            <MudListItem T="string">
                                <div class="d-flex justify-space-between">
                                    <MudText Typo="Typo.body2" Color="Color.Secondary">Last Reviewed</MudText>
                                    <MudText Typo="Typo.body2">@_article.LastReviewedDate.Value.ToLocalTime().ToString("MMM dd, yyyy")</MudText>
                                </div>
                            </MudListItem>
                        }
                    </MudList>
                </MudCardContent>
            </MudCard>

            @* Related Articles *@
            @if (_relatedArticles.Any())
            {
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.subtitle1">Related Articles</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudList T="ArticleSummaryDto" Dense="true">
                            @foreach (var related in _relatedArticles)
                            {
                                <MudListItem T="ArticleSummaryDto" OnClick="@(() => Navigation.NavigateTo($"/articles/{related.ArticleId}"))">
                                    <div class="d-flex align-center gap-2">
                                        <MudIcon Icon="@GetArticleTypeIcon(related.ArticleType)" Size="Size.Small" Color="@GetArticleTypeColor(related.ArticleType)" />
                                        <MudText Typo="Typo.body2">@related.Title</MudText>
                                    </div>
                                </MudListItem>
                            }
                        </MudList>
                    </MudCardContent>
                </MudCard>
            }
        </MudItem>
    </MudGrid>
}

@code {
    [Parameter]
    public Guid ArticleId { get; set; }

    private bool _loading = true;
    private ArticleDto? _article;
    private List<ArticleSummaryDto> _relatedArticles = new();
    private bool? _feedbackGiven;
    private string _feedbackComment = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadArticle();
    }

    private async Task LoadArticle()
    {
        _loading = true;
        StateHasChanged();

        try
        {
            _article = await ApiService.GetArticleAsync(ArticleId);
            // TODO: Load related articles from API when endpoint is available
            _relatedArticles = new List<ArticleSummaryDto>();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading article: {ex.Message}", Severity.Error);
        }

        _loading = false;
        StateHasChanged();
    }

    private RenderFragment RenderArticleContent() => builder =>
    {
        if (_article?.Content == null)
            return;

        try
        {
            var contentJson = System.Text.Json.JsonSerializer.Serialize(_article.Content);
            using var doc = System.Text.Json.JsonDocument.Parse(contentJson);
            var root = doc.RootElement;

            var seq = 0;

            // Prerequisites
            if (root.TryGetProperty("prerequisites", out var prereqs))
            {
                builder.OpenElement(seq++, "div");
                builder.AddAttribute(seq++, "class", "mb-4");

                builder.OpenComponent<MudText>(seq++);
                builder.AddAttribute(seq++, "Typo", Typo.h6);
                builder.AddAttribute(seq++, "Class", "mb-2");
                builder.AddAttribute(seq++, "ChildContent", (RenderFragment)(b => b.AddContent(0, "Prerequisites")));
                builder.CloseComponent();

                builder.OpenElement(seq++, "ul");
                builder.AddAttribute(seq++, "class", "mb-0");
                foreach (var prereq in prereqs.EnumerateArray())
                {
                    var item = prereq.GetProperty("item").GetString();
                    builder.OpenElement(seq++, "li");
                    builder.AddContent(seq++, item);
                    builder.CloseElement();
                }
                builder.CloseElement();
                builder.CloseElement();
            }

            // Steps
            if (root.TryGetProperty("steps", out var steps))
            {
                builder.OpenElement(seq++, "div");
                builder.AddAttribute(seq++, "class", "mb-4");

                builder.OpenComponent<MudText>(seq++);
                builder.AddAttribute(seq++, "Typo", Typo.h6);
                builder.AddAttribute(seq++, "Class", "mb-2");
                builder.AddAttribute(seq++, "ChildContent", (RenderFragment)(b => b.AddContent(0, "Steps")));
                builder.CloseComponent();

                foreach (var step in steps.EnumerateArray())
                {
                    var stepNum = step.GetProperty("step_number").GetInt32();
                    var title = step.GetProperty("title").GetString();
                    var action = step.GetProperty("action").GetString();
                    var outcome = step.TryGetProperty("expected_outcome", out var o) ? o.GetString() : null;

                    builder.OpenComponent<MudCard>(seq++);
                    builder.AddAttribute(seq++, "Elevation", 1);
                    builder.AddAttribute(seq++, "Class", "mb-3 pa-3");
                    builder.AddAttribute(seq++, "ChildContent", (RenderFragment)(cardBuilder =>
                    {
                        cardBuilder.OpenComponent<MudText>(0);
                        cardBuilder.AddAttribute(1, "Typo", Typo.subtitle1);
                        cardBuilder.AddAttribute(2, "ChildContent", (RenderFragment)(b => b.AddContent(0, $"Step {stepNum}: {title}")));
                        cardBuilder.CloseComponent();

                        cardBuilder.OpenComponent<MudText>(3);
                        cardBuilder.AddAttribute(4, "Class", "mt-2");
                        cardBuilder.AddAttribute(5, "ChildContent", (RenderFragment)(b => b.AddContent(0, action)));
                        cardBuilder.CloseComponent();

                        if (!string.IsNullOrEmpty(outcome))
                        {
                            cardBuilder.OpenComponent<MudText>(6);
                            cardBuilder.AddAttribute(7, "Typo", Typo.body2);
                            cardBuilder.AddAttribute(8, "Color", Color.Success);
                            cardBuilder.AddAttribute(9, "Class", "mt-2");
                            cardBuilder.AddAttribute(10, "ChildContent", (RenderFragment)(b => b.AddContent(0, $"Expected: {outcome}")));
                            cardBuilder.CloseComponent();
                        }
                    }));
                    builder.CloseComponent();
                }
                builder.CloseElement();
            }

            // Validation
            if (root.TryGetProperty("validation", out var validation))
            {
                builder.OpenComponent<MudAlert>(seq++);
                builder.AddAttribute(seq++, "Severity", Severity.Success);
                builder.AddAttribute(seq++, "Class", "mb-4");
                builder.AddAttribute(seq++, "ChildContent", (RenderFragment)(alertBuilder =>
                {
                    var title = validation.GetProperty("title").GetString();
                    alertBuilder.OpenComponent<MudText>(0);
                    alertBuilder.AddAttribute(1, "Typo", Typo.subtitle2);
                    alertBuilder.AddAttribute(2, "ChildContent", (RenderFragment)(b => b.AddContent(0, title)));
                    alertBuilder.CloseComponent();

                    if (validation.TryGetProperty("steps", out var valSteps))
                    {
                        alertBuilder.OpenElement(3, "ul");
                        alertBuilder.AddAttribute(4, "class", "mt-2 mb-0");
                        foreach (var valStep in valSteps.EnumerateArray())
                        {
                            alertBuilder.OpenElement(5, "li");
                            alertBuilder.AddContent(6, valStep.GetString());
                            alertBuilder.CloseElement();
                        }
                        alertBuilder.CloseElement();
                    }
                }));
                builder.CloseComponent();
            }

            // Next Steps
            if (root.TryGetProperty("next_steps", out var nextSteps))
            {
                builder.OpenElement(seq++, "div");

                builder.OpenComponent<MudText>(seq++);
                builder.AddAttribute(seq++, "Typo", Typo.h6);
                builder.AddAttribute(seq++, "Class", "mb-2");
                builder.AddAttribute(seq++, "ChildContent", (RenderFragment)(b => b.AddContent(0, "Next Steps")));
                builder.CloseComponent();

                builder.OpenElement(seq++, "ul");
                builder.AddAttribute(seq++, "class", "mb-0");
                foreach (var next in nextSteps.EnumerateArray())
                {
                    builder.OpenElement(seq++, "li");
                    builder.AddContent(seq++, next.GetString());
                    builder.CloseElement();
                }
                builder.CloseElement();
                builder.CloseElement();
            }
        }
        catch
        {
            builder.OpenComponent<MudText>(0);
            builder.AddAttribute(1, "ChildContent", (RenderFragment)(b => b.AddContent(0, _article.Content)));
            builder.CloseComponent();
        }
    };

    private List<string> GetTags()
    {
        return _article?.Tags ?? new List<string>();
    }

    private void SearchByTag(string tag)
    {
        Navigation.NavigateTo($"/articles?search={Uri.EscapeDataString(tag)}");
    }

    private async Task SubmitForReview()
    {
        var success = await ApiService.UpdateArticleStatusAsync(ArticleId, "InReview");
        if (success)
        {
            Snackbar.Add("Article submitted for review", Severity.Success);
            await LoadArticle();
        }
        else
        {
            Snackbar.Add("Failed to submit article for review", Severity.Error);
        }
    }

    private async Task PublishArticle()
    {
        var success = await ApiService.UpdateArticleStatusAsync(ArticleId, "Published");
        if (success)
        {
            Snackbar.Add("Article published successfully", Severity.Success);
            await LoadArticle();
        }
        else
        {
            Snackbar.Add("Failed to publish article", Severity.Error);
        }
    }

    private void ViewHistory()
    {
        Navigation.NavigateTo($"/articles/{ArticleId}/history");
    }

    private async Task DuplicateArticle()
    {
        Snackbar.Add("Duplicate feature coming soon", Severity.Info);
        await Task.CompletedTask;
    }

    private async Task ArchiveArticle()
    {
        var success = await ApiService.UpdateArticleStatusAsync(ArticleId, "Archived");
        if (success)
        {
            Snackbar.Add("Article archived", Severity.Info);
            Navigation.NavigateTo("/articles");
        }
        else
        {
            Snackbar.Add("Failed to archive article", Severity.Error);
        }
    }

    private async Task DeleteArticle()
    {
        var result = await DialogService.ShowMessageBox(
            "Delete Article",
            "Are you sure you want to delete this article? This action cannot be undone.",
            yesText: "Delete", cancelText: "Cancel");

        if (result == true)
        {
            var success = await ApiService.DeleteArticleAsync(ArticleId);
            if (success)
            {
                Snackbar.Add("Article deleted", Severity.Warning);
                Navigation.NavigateTo("/articles");
            }
            else
            {
                Snackbar.Add("Failed to delete article", Severity.Error);
            }
        }
    }

    private async Task SubmitFeedback(bool helpful)
    {
        _feedbackGiven = helpful;
        var success = await ApiService.RecordFeedbackAsync(ArticleId, helpful);
        if (success)
        {
            if (helpful)
                _article!.HelpfulCount++;
            else
                _article!.NotHelpfulCount++;
        }
    }

    private async Task SubmitFeedbackComment()
    {
        if (!string.IsNullOrWhiteSpace(_feedbackComment) && _feedbackGiven.HasValue)
        {
            await ApiService.RecordFeedbackAsync(ArticleId, _feedbackGiven.Value, _feedbackComment);
        }
        Snackbar.Add("Thank you for your feedback!", Severity.Success);
        _feedbackComment = "";
    }

    private string GetArticleTypeIcon(string type) => type switch
    {
        "HowTo" => Icons.Material.Filled.MenuBook,
        "Troubleshooting" => Icons.Material.Filled.BugReport,
        "ReleaseNote" => Icons.Material.Filled.NewReleases,
        "Faq" or "FAQ" => Icons.Material.Filled.QuestionAnswer,
        "ProductOverview" => Icons.Material.Filled.Inventory,
        "BestPractice" => Icons.Material.Filled.Star,
        _ => Icons.Material.Filled.Article
    };

    private Color GetArticleTypeColor(string type) => type switch
    {
        "HowTo" => Color.Primary,
        "Troubleshooting" => Color.Error,
        "ReleaseNote" => Color.Info,
        "Faq" or "FAQ" => Color.Secondary,
        "ProductOverview" => Color.Tertiary,
        "BestPractice" => Color.Warning,
        _ => Color.Default
    };

    private Color GetStatusColor(string status) => status switch
    {
        "Draft" => Color.Default,
        "InReview" => Color.Warning,
        "Published" => Color.Success,
        "Archived" => Color.Dark,
        _ => Color.Default
    };
}

@page "/articles/{ArticleId:guid}/history"
@rendermode InteractiveServer
@using KnowledgeBase.Web.Services
@using KnowledgeBase.Web.Services.Models
@inject KnowledgeBaseApiService ApiService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar

<PageTitle>Version History - Knowledge Base</PageTitle>

<MudStack Row="true" AlignItems="AlignItems.Center" Class="mb-4">
    <MudIconButton Icon="@Icons.Material.Filled.ArrowBack" Href="@($"/articles/{ArticleId}")" />
    <MudText Typo="Typo.h4">Version History</MudText>
</MudStack>

@if (_loading)
{
    <MudProgressLinear Indeterminate="true" Color="Color.Primary" Class="mb-4" />
}
else if (!_versions.Any())
{
    <MudAlert Severity="Severity.Info" Class="mb-4">
        No version history available for this article.
    </MudAlert>
}
else
{
    <MudTimeline TimelineOrientation="TimelineOrientation.Vertical" TimelinePosition="TimelinePosition.Start">
        @foreach (var version in _versions)
        {
            <MudTimelineItem Color="@(version == _versions.First() ? Color.Primary : Color.Default)"
                             Size="@(version == _versions.First() ? Size.Medium : Size.Small)">
                <ItemContent>
                    <MudCard Elevation="1" Class="mb-2">
                        <MudCardContent>
                            <MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Start">
                                <div>
                                    <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2">
                                        <MudText Typo="Typo.subtitle1">Version @version.VersionNumber</MudText>
                                        @if (version == _versions.First())
                                        {
                                            <MudChip T="string" Size="Size.Small" Color="Color.Primary">Current</MudChip>
                                        }
                                    </MudStack>
                                    <MudText Typo="Typo.body2" Class="mt-1">@version.Title</MudText>
                                    @if (!string.IsNullOrEmpty(version.ChangeDescription))
                                    {
                                        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mt-1">
                                            @version.ChangeDescription
                                        </MudText>
                                    }
                                </div>
                                <MudStack AlignItems="AlignItems.End">
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">
                                        @version.ModifiedDate.ToLocalTime().ToString("MMM dd, yyyy HH:mm")
                                    </MudText>
                                    <MudText Typo="Typo.caption">
                                        @version.ModifiedByName
                                    </MudText>
                                </MudStack>
                            </MudStack>
                        </MudCardContent>
                    </MudCard>
                </ItemContent>
            </MudTimelineItem>
        }
    </MudTimeline>
}

<MudStack Row="true" Justify="Justify.Center" Spacing="2" Class="mt-4">
    <MudButton Variant="Variant.Outlined" Color="Color.Default"
               Href="@($"/articles/{ArticleId}")" StartIcon="@Icons.Material.Filled.Visibility">
        View Article
    </MudButton>
    <MudButton Variant="Variant.Filled" Color="Color.Primary"
               Href="@($"/articles/{ArticleId}/edit")" StartIcon="@Icons.Material.Filled.Edit">
        Edit Article
    </MudButton>
</MudStack>

@code {
    [Parameter] public Guid ArticleId { get; set; }

    private bool _loading = true;
    private List<ArticleVersionDto> _versions = new();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _versions = await ApiService.GetArticleVersionsAsync(ArticleId);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading version history: {ex.Message}", Severity.Error);
        }

        _loading = false;
    }
}

using System.ComponentModel.DataAnnotations;

namespace KnowledgeBase.Api.Models.Requests;

/// <summary>
/// Request model for creating an article via AI processing.
/// </summary>
public class CreateArticleRequest
{
    /// <summary>
    /// Raw user input (pasted content, conversation, etc.)
    /// </summary>
    [Required]
    public string UserInput { get; set; } = string.Empty;

    /// <summary>
    /// Optional: Override AI-detected article type.
    /// </summary>
    public string? ArticleType { get; set; }

    /// <summary>
    /// Optional: Pre-assign to a module.
    /// </summary>
    public Guid? ModuleId { get; set; }
}

/// <summary>
/// Request model for manual article creation/update.
/// </summary>
public class SaveArticleRequest
{
    [Required]
    public string ArticleType { get; set; } = string.Empty;

    [Required]
    [MaxLength(200)]
    public string Title { get; set; } = string.Empty;

    [Required]
    [MaxLength(500)]
    public string Summary { get; set; } = string.Empty;

    [Required]
    public object Content { get; set; } = new { };

    [Required]
    public Guid ModuleId { get; set; }

    [Required]
    [MaxLength(50)]
    public string Category { get; set; } = string.Empty;

    public List<string>? Tags { get; set; }

    public int? EstimatedTimeMinutes { get; set; }

    public string? Difficulty { get; set; }

    public List<string>? AppliesTo { get; set; }

    public List<TicketReferenceRequest>? TicketReferences { get; set; }

    public List<Guid>? RelatedArticleIds { get; set; }
}

public class TicketReferenceRequest
{
    [Required]
    public string TicketId { get; set; } = string.Empty;

    [Required]
    public string TicketSystem { get; set; } = string.Empty;

    public string? TicketUrl { get; set; }
}

/// <summary>
/// Request model for AI article generation.
/// </summary>
public class GenerateArticleApiRequest
{
    /// <summary>
    /// Raw user input (pasted content, conversation, etc.)
    /// </summary>
    [Required]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Optional: Override AI-detected article type.
    /// </summary>
    public string? ArticleType { get; set; }

    /// <summary>
    /// Optional: Pre-assign to a module.
    /// </summary>
    public Guid? ModuleId { get; set; }
}

/// <summary>
/// Request model for updating article status.
/// </summary>
public class UpdateStatusRequest
{
    [Required]
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// Request model for publishing an article.
/// </summary>
public class PublishArticleRequest
{
    public string? ChangeDescription { get; set; }
}

/// <summary>
/// Request model for submitting article feedback.
/// </summary>
public class ArticleFeedbackRequest
{
    [Required]
    public string FeedbackType { get; set; } = string.Empty;

    public string? Comments { get; set; }
}

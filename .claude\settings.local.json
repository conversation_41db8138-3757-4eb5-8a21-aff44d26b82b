{"permissions": {"allow": ["WebFetch(domain:claude.ai)", "Bash(dotnet new:*)", "Bash(dotnet add:*)", "Bash(dotnet build:*)", "Bash(dotnet ef migrations add:*)", "Bash(dotnet tool list:*)", "<PERSON><PERSON>(findstr:*)", "Bash(dotnet dotnet-ef:*)", "Bash(find:*)", "Bash(del \"d:\\\\Development\\\\MeshworksKB\\\\src\\\\KnowledgeBase.Web\\\\Components\\\\Pages\\\\Counter.razor\")", "Bash(del \"d:\\\\Development\\\\MeshworksKB\\\\src\\\\KnowledgeBase.Web\\\\Components\\\\Pages\\\\Weather.razor\")"]}}
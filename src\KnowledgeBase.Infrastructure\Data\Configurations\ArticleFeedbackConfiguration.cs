using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KnowledgeBase.Infrastructure.Data.Configurations;

public class ArticleFeedbackConfiguration : IEntityTypeConfiguration<ArticleFeedback>
{
    public void Configure(EntityTypeBuilder<ArticleFeedback> builder)
    {
        builder.ToTable("ArticleFeedback");

        builder.HasKey(f => f.FeedbackId);

        builder.Property(f => f.FeedbackType)
            .HasConversion<string>()
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(f => f.Comments)
            .HasMaxLength(1000);

        builder.Property(f => f.FeedbackDate)
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(f => f.SessionId)
            .HasMaxLength(100);

        builder.HasOne(f => f.Article)
            .WithMany(a => a.Feedback)
            .HasForeignKey(f => f.ArticleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(f => f.User)
            .WithMany(u => u.Feedback)
            .HasForeignKey(f => f.UserId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasIndex(f => f.ArticleId);
    }
}

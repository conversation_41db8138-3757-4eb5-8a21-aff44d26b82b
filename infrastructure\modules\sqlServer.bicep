// SQL Server and Database module

@description('Name of the SQL Server')
param serverName string

@description('Name of the SQL Database')
param databaseName string

@description('Location for the resources')
param location string

@description('SQL Server administrator login')
param adminLogin string

@description('SQL Server administrator password')
@secure()
param adminPassword string

@description('Database SKU')
@allowed(['Basic', 'S0', 'S1', 'S2', 'P1', 'P2'])
param databaseSku string = 'S0'

var skuMapping = {
  Basic: {
    name: 'Basic'
    tier: 'Basic'
    capacity: 5
  }
  S0: {
    name: 'Standard'
    tier: 'Standard'
    capacity: 10
  }
  S1: {
    name: 'Standard'
    tier: 'Standard'
    capacity: 20
  }
  S2: {
    name: 'Standard'
    tier: 'Standard'
    capacity: 50
  }
  P1: {
    name: 'Premium'
    tier: 'Premium'
    capacity: 125
  }
  P2: {
    name: 'Premium'
    tier: 'Premium'
    capacity: 250
  }
}

resource sqlServer 'Microsoft.Sql/servers@2023-05-01-preview' = {
  name: serverName
  location: location
  properties: {
    administratorLogin: adminLogin
    administratorLoginPassword: adminPassword
    version: '12.0'
    minimalTlsVersion: '1.2'
    publicNetworkAccess: 'Enabled'
  }
}

resource sqlDatabase 'Microsoft.Sql/servers/databases@2023-05-01-preview' = {
  parent: sqlServer
  name: databaseName
  location: location
  sku: {
    name: skuMapping[databaseSku].name
    tier: skuMapping[databaseSku].tier
    capacity: skuMapping[databaseSku].capacity
  }
  properties: {
    collation: 'SQL_Latin1_General_CP1_CI_AS'
    maxSizeBytes: 268435456000 // 250 GB
    zoneRedundant: false
    readScale: 'Disabled'
  }
}

// Allow Azure services to access the server
resource firewallRuleAzure 'Microsoft.Sql/servers/firewallRules@2023-05-01-preview' = {
  parent: sqlServer
  name: 'AllowAllAzureIps'
  properties: {
    startIpAddress: '0.0.0.0'
    endIpAddress: '0.0.0.0'
  }
}

output id string = sqlServer.id
output name string = sqlServer.name
output fullyQualifiedDomainName string = sqlServer.properties.fullyQualifiedDomainName
output databaseName string = sqlDatabase.name
output connectionString string = 'Server=tcp:${sqlServer.properties.fullyQualifiedDomainName},1433;Initial Catalog=${databaseName};Persist Security Info=False;User ID=${adminLogin};Password=${adminPassword};MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;'

using System.Text.Json;
using KnowledgeBase.Core.Enums;
using KnowledgeBase.Core.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace KnowledgeBase.Infrastructure.Services.Agents;

/// <summary>
/// Base content generation agent that can generate any article type.
/// </summary>
public class ContentAgentService : IAgentService
{
    private readonly IAzureOpenAIClient _openAIClient;
    private readonly ILogger<ContentAgentService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ArticleType _articleType;

    public ArticleType ArticleType => _articleType;

    public ContentAgentService(
        IAzureOpenAIClient openAIClient,
        IConfiguration configuration,
        ILogger<ContentAgentService> logger,
        ArticleType articleType)
    {
        _openAIClient = openAIClient;
        _configuration = configuration;
        _logger = logger;
        _articleType = articleType;
    }

    public async Task<AgentArticleResult> GenerateAsync(string userInput, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating {ArticleType} article", _articleType);

        try
        {
            var deploymentKey = _articleType switch
            {
                ArticleType.HowTo => "HowTo",
                ArticleType.Troubleshooting => "Troubleshooting",
                ArticleType.ReleaseNote => "ReleaseNote",
                ArticleType.Faq => "Faq",
                ArticleType.ProductOverview => "ProductOverview",
                ArticleType.BestPractice => "BestPractice",
                _ => "HowTo"
            };

            var deploymentName = _configuration[$"AzureOpenAI:DeploymentNames:{deploymentKey}"]
                ?? _configuration["AzureOpenAI:DeploymentNames:Default"]
                ?? "gpt-4";

            _logger.LogInformation("Using deployment: {DeploymentName} for article type: {ArticleType}", deploymentName, _articleType);

            var articleTypeString = ConvertArticleTypeToString(_articleType);
            var systemPrompt = AgentPrompts.GetPromptForType(articleTypeString);

            _logger.LogInformation("Using prompt for article type: {ArticleTypeString}", articleTypeString);

            var response = await _openAIClient.GetChatCompletionAsync(
                deploymentName,
                systemPrompt,
                userInput,
                temperature: 0.7f,
                maxTokens: 4000,
                cancellationToken);

            if (!response.Success)
            {
                _logger.LogError("Content agent failed: {Error}", response.ErrorMessage);
                return new AgentArticleResult
                {
                    Success = false,
                    ErrorMessage = response.ErrorMessage
                };
            }

        try
        {
            var json = ExtractJson(response.Content);
            using var doc = JsonDocument.Parse(json);
            var root = doc.RootElement;

            var title = root.GetProperty("title").GetString() ?? "Untitled";
            var summary = root.GetProperty("summary").GetString() ?? "";
            var content = root.GetProperty("content");
            var tags = ParseStringArray(root, "tags");
            var category = root.TryGetProperty("category", out var cat) ? cat.GetString() : null;
            var estimatedTime = root.TryGetProperty("estimated_time_minutes", out var time) ? time.GetInt32() : (int?)null;
            var difficulty = root.TryGetProperty("difficulty", out var diff) ? ParseDifficulty(diff.GetString()) : (Difficulty?)null;

            _logger.LogInformation("Generated article: {Title}", title);

            return new AgentArticleResult
            {
                Success = true,
                Title = title,
                Summary = summary,
                ContentJson = content.GetRawText(),
                Tags = tags,
                SuggestedCategory = category,
                EstimatedTimeMinutes = estimatedTime,
                Difficulty = difficulty
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to parse agent response: {Response}", response.Content);
            return new AgentArticleResult
            {
                Success = false,
                ErrorMessage = $"Failed to parse response: {ex.Message}"
            };
        }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in GenerateAsync for {ArticleType}", _articleType);
            return new AgentArticleResult
            {
                Success = false,
                ErrorMessage = $"Unexpected error: {ex.Message}"
            };
        }
    }

    private static List<string> ParseStringArray(JsonElement root, string propertyName)
    {
        if (!root.TryGetProperty(propertyName, out var prop))
            return new List<string>();

        var list = new List<string>();
        foreach (var item in prop.EnumerateArray())
        {
            var value = item.GetString();
            if (!string.IsNullOrEmpty(value))
                list.Add(value);
        }
        return list;
    }

    private static Difficulty? ParseDifficulty(string? value)
    {
        return value?.ToLower() switch
        {
            "beginner" => Difficulty.Beginner,
            "intermediate" => Difficulty.Intermediate,
            "advanced" => Difficulty.Advanced,
            _ => null
        };
    }

    private static string ExtractJson(string content)
    {
        content = content.Trim();
        if (content.StartsWith("```json"))
            content = content[7..];
        else if (content.StartsWith("```"))
            content = content[3..];
        if (content.EndsWith("```"))
            content = content[..^3];
        return content.Trim();
    }

    private static string ConvertArticleTypeToString(ArticleType articleType)
    {
        return articleType switch
        {
            ArticleType.HowTo => "how_to",
            ArticleType.Troubleshooting => "troubleshooting",
            ArticleType.ReleaseNote => "release_note",
            ArticleType.Faq => "faq",
            ArticleType.ProductOverview => "product_overview",
            ArticleType.BestPractice => "best_practice",
            _ => "how_to"
        };
    }
}

/// <summary>
/// Factory for creating content agent services.
/// </summary>
public class AgentFactory
{
    private readonly IAzureOpenAIClient _openAIClient;
    private readonly IConfiguration _configuration;
    private readonly ILoggerFactory _loggerFactory;

    public AgentFactory(
        IAzureOpenAIClient openAIClient,
        IConfiguration configuration,
        ILoggerFactory loggerFactory)
    {
        _openAIClient = openAIClient;
        _configuration = configuration;
        _loggerFactory = loggerFactory;
    }

    public IAgentService GetAgent(ArticleType articleType)
    {
        return new ContentAgentService(
            _openAIClient,
            _configuration,
            _loggerFactory.CreateLogger<ContentAgentService>(),
            articleType);
    }
}

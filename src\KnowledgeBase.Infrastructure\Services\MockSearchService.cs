using System.Text.Json;
using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace KnowledgeBase.Infrastructure.Services;

/// <summary>
/// Mock search service for development without Azure AI Search.
/// Uses in-memory search with basic text matching.
/// </summary>
public class MockSearchService : ISearchService
{
    private readonly ILogger<MockSearchService> _logger;
    private readonly List<ArticleIndex> _index = new();
    private readonly object _lock = new();

    public MockSearchService(ILogger<MockSearchService> logger)
    {
        _logger = logger;
        _logger.LogInformation("Using MockSearchService for development");
    }

    public Task<(List<ArticleSearchResult> Items, int TotalCount)> SearchAsync(
        SearchQuery query,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Mock search for: {Query}", query.Query);

        lock (_lock)
        {
            var results = _index.AsEnumerable();

            // Apply text search
            if (!string.IsNullOrWhiteSpace(query.Query))
            {
                var searchTerms = query.Query.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);
                results = results.Where(a =>
                    searchTerms.Any(term =>
                        a.Title.ToLower().Contains(term) ||
                        a.Summary.ToLower().Contains(term) ||
                        a.Content.ToLower().Contains(term) ||
                        a.Tags.Any(t => t.ToLower().Contains(term))));
            }

            // Apply filters
            if (!string.IsNullOrEmpty(query.ArticleType))
            {
                results = results.Where(a => a.ArticleType.Equals(query.ArticleType, StringComparison.OrdinalIgnoreCase));
            }

            if (query.ModuleId.HasValue)
            {
                results = results.Where(a => a.ModuleId == query.ModuleId.Value);
            }

            if (!string.IsNullOrEmpty(query.Category))
            {
                results = results.Where(a => a.Category.Equals(query.Category, StringComparison.OrdinalIgnoreCase));
            }

            if (!string.IsNullOrEmpty(query.Status))
            {
                results = results.Where(a => a.Status.Equals(query.Status, StringComparison.OrdinalIgnoreCase));
            }

            if (query.Tags?.Any() == true)
            {
                results = results.Where(a => query.Tags.Any(t => a.Tags.Contains(t, StringComparer.OrdinalIgnoreCase)));
            }

            // Get total count before pagination
            var totalCount = results.Count();

            // Apply sorting
            results = query.SortBy?.ToLower() switch
            {
                "date" or "lastmodifieddate" => query.SortDescending
                    ? results.OrderByDescending(a => a.LastModifiedDate)
                    : results.OrderBy(a => a.LastModifiedDate),
                "title" => query.SortDescending
                    ? results.OrderByDescending(a => a.Title)
                    : results.OrderBy(a => a.Title),
                "views" or "viewcount" => query.SortDescending
                    ? results.OrderByDescending(a => a.ViewCount)
                    : results.OrderBy(a => a.ViewCount),
                "rating" => query.SortDescending
                    ? results.OrderByDescending(a => a.Rating)
                    : results.OrderBy(a => a.Rating),
                _ => results.OrderByDescending(a => CalculateRelevanceScore(a, query.Query))
            };

            // Apply pagination
            var skip = (query.Page - 1) * query.PageSize;
            var pagedResults = results.Skip(skip).Take(query.PageSize).ToList();

            var searchResults = pagedResults.Select((a, index) => new ArticleSearchResult
            {
                ArticleId = a.ArticleId,
                ArticleType = a.ArticleType,
                Title = a.Title,
                Summary = a.Summary,
                ModuleName = a.ModuleName,
                Category = a.Category,
                Tags = a.Tags,
                LastModifiedDate = a.LastModifiedDate,
                ViewCount = a.ViewCount,
                Rating = a.Rating,
                SearchRank = skip + index + 1
            }).ToList();

            _logger.LogInformation("Mock search returned {Count} of {Total} results", searchResults.Count, totalCount);

            return Task.FromResult((searchResults, totalCount));
        }
    }

    public Task<List<string>> GetSuggestionsAsync(
        string query,
        int maxResults = 10,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(query))
            return Task.FromResult(new List<string>());

        lock (_lock)
        {
            var suggestions = _index
                .SelectMany(a => new[] { a.Title }.Concat(a.Tags))
                .Where(s => s.StartsWith(query, StringComparison.OrdinalIgnoreCase))
                .Distinct()
                .Take(maxResults)
                .ToList();

            return Task.FromResult(suggestions);
        }
    }

    public Task IndexArticleAsync(Article article, CancellationToken cancellationToken = default)
    {
        return IndexArticlesAsync(new[] { article }, cancellationToken);
    }

    public Task IndexArticlesAsync(IEnumerable<Article> articles, CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            foreach (var article in articles)
            {
                // Remove existing entry
                _index.RemoveAll(a => a.ArticleId == article.ArticleId);

                // Add new entry
                _index.Add(new ArticleIndex
                {
                    ArticleId = article.ArticleId,
                    ArticleType = article.ArticleType.ToString(),
                    Title = article.Title,
                    Summary = article.Summary,
                    Content = ExtractSearchableContent(article.Content),
                    ModuleId = article.ModuleId,
                    ModuleName = article.Module?.Name ?? "",
                    Category = article.Category,
                    Tags = ParseTags(article.Tags),
                    Status = article.Status.ToString().ToLower(),
                    LastModifiedDate = article.LastModifiedDate,
                    ViewCount = article.ViewCount,
                    Rating = article.Rating
                });
            }

            _logger.LogInformation("Indexed {Count} articles in mock search", articles.Count());
        }

        return Task.CompletedTask;
    }

    public Task RemoveFromIndexAsync(Guid articleId, CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            _index.RemoveAll(a => a.ArticleId == articleId);
            _logger.LogInformation("Removed article {ArticleId} from mock search index", articleId);
        }

        return Task.CompletedTask;
    }

    public Task EnsureIndexExistsAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Mock search index ready");
        return Task.CompletedTask;
    }

    private static double CalculateRelevanceScore(ArticleIndex article, string? query)
    {
        if (string.IsNullOrWhiteSpace(query))
            return 0;

        var terms = query.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);
        double score = 0;

        foreach (var term in terms)
        {
            // Title matches worth more
            if (article.Title.ToLower().Contains(term))
                score += 10;

            // Summary matches
            if (article.Summary.ToLower().Contains(term))
                score += 5;

            // Content matches
            if (article.Content.ToLower().Contains(term))
                score += 2;

            // Tag exact matches
            if (article.Tags.Any(t => t.Equals(term, StringComparison.OrdinalIgnoreCase)))
                score += 8;
        }

        // Boost by view count and rating
        score += Math.Log10(article.ViewCount + 1);
        score += (double)(article.Rating ?? 0) * 2;

        return score;
    }

    private static string ExtractSearchableContent(string jsonContent)
    {
        if (string.IsNullOrEmpty(jsonContent))
            return string.Empty;

        try
        {
            using var doc = JsonDocument.Parse(jsonContent);
            var texts = new List<string>();
            ExtractText(doc.RootElement, texts);
            return string.Join(" ", texts);
        }
        catch
        {
            return jsonContent;
        }
    }

    private static void ExtractText(JsonElement element, List<string> texts)
    {
        switch (element.ValueKind)
        {
            case JsonValueKind.String:
                var text = element.GetString();
                if (!string.IsNullOrWhiteSpace(text))
                    texts.Add(text);
                break;

            case JsonValueKind.Object:
                foreach (var property in element.EnumerateObject())
                {
                    ExtractText(property.Value, texts);
                }
                break;

            case JsonValueKind.Array:
                foreach (var item in element.EnumerateArray())
                {
                    ExtractText(item, texts);
                }
                break;
        }
    }

    private static List<string> ParseTags(string? tagsJson)
    {
        if (string.IsNullOrEmpty(tagsJson))
            return new List<string>();

        try
        {
            return JsonSerializer.Deserialize<List<string>>(tagsJson) ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }

    private class ArticleIndex
    {
        public Guid ArticleId { get; set; }
        public string ArticleType { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Summary { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public Guid ModuleId { get; set; }
        public string ModuleName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public List<string> Tags { get; set; } = new();
        public string Status { get; set; } = string.Empty;
        public DateTime LastModifiedDate { get; set; }
        public int ViewCount { get; set; }
        public decimal? Rating { get; set; }
    }
}

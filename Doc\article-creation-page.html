<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Base - Create Article</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="logo">
                <div class="logo-icon">K</div>
                <span>Knowledge Base</span>
            </div>
            <div class="header-actions">
                <div class="user-avatar">JD</div>
            </div>
        </header>

        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-section">
                <div class="sidebar-section-title">Main</div>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">🏠</span>
                    <span>Dashboard</span>
                </a>
                <a href="#" class="nav-item active">
                    <span class="nav-item-icon">✨</span>
                    <span>Create Article</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">📚</span>
                    <span>All Articles</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">🔍</span>
                    <span>Search</span>
                </a>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-title">Management</div>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">📋</span>
                    <span>Review Queue</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">📊</span>
                    <span>Analytics</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">🗂️</span>
                    <span>Modules</span>
                </a>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-title">Settings</div>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">👥</span>
                    <span>Users</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">⚙️</span>
                    <span>Settings</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-wrapper">
                <!-- Page Header -->
                <div class="page-header">
                    <h1 class="page-title">Create New Article</h1>
                    <p class="page-subtitle">AI-powered article creation from any source - paste text, upload documents, or enter manually</p>
                </div>

                <!-- Info Banner -->
                <div class="info-banner">
                    <div class="info-icon">💡</div>
                    <div class="info-content">
                        <div class="info-title">How it works</div>
                        <div class="info-text">Paste your content below and our AI will analyze it to determine the best article type, extract key information, and structure it properly. You can review and edit before publishing.</div>
                    </div>
                </div>

                <!-- Workflow Steps -->
                <div class="workflow-steps">
                    <div class="workflow-step active">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <div class="step-title">Input Content</div>
                            <div class="step-description">Paste or upload your content</div>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <div class="step-title">AI Processing</div>
                            <div class="step-description">AI extracts and structures</div>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <div class="step-title">Review & Edit</div>
                            <div class="step-description">Refine the generated article</div>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <div class="step-title">Publish</div>
                            <div class="step-description">Save draft or submit for review</div>
                        </div>
                    </div>
                </div>

                <!-- Content Input Section -->
                <div class="content-section">
                    <div class="section-header">
                        <div class="section-title">
                            <div class="section-icon">📝</div>
                            <span>Input Your Content</span>
                        </div>
                    </div>

                    <div class="input-area">
                        <div class="input-tabs">
                            <button class="input-tab active" onclick="switchTab('paste')">✍️ Paste Text</button>
                            <button class="input-tab" onclick="switchTab('upload')">📄 Upload File</button>
                            <button class="input-tab" onclick="switchTab('manual')">⌨️ Manual Entry</button>
                        </div>

                        <!-- Paste Text Area -->
                        <div id="paste-tab" class="textarea-wrapper">
                            <textarea 
                                class="content-textarea" 
                                id="content-input"
                                placeholder="Paste your content here... 

You can paste:
• Text from documents (instructions, troubleshooting steps, release notes)
• Conversations from Slack or Teams
• Meeting notes or documentation
• Technical specifications

Our AI will analyze the content and suggest the best article type."
                            >To set up single sign-on in the admin portal, first you need admin credentials. Log into the admin portal at admin.example.com, then go to Settings > Security. Click on "SSO Configuration" and choose your identity provider (we support Okta, Azure AD, and Google Workspace). For Azure AD, you'll need your tenant ID and client secret. Enter those values, then click "Test Connection" to verify. Once verified, enable SSO for your organization.</textarea>
                            <div class="textarea-footer">
                                <span class="char-count"><span id="char-count">508</span> characters</span>
                                <div class="quick-actions">
                                    <button class="quick-action-btn" onclick="clearContent()">Clear</button>
                                    <button class="quick-action-btn" onclick="pasteFromClipboard()">📋 Paste from Clipboard</button>
                                </div>
                            </div>
                        </div>

                        <!-- File Upload Area -->
                        <div id="upload-tab" class="file-upload-area">
                            <div class="file-upload-icon">📄</div>
                            <div class="file-upload-text">Drop files here or click to browse</div>
                            <div class="file-upload-subtext">Supports Word (.docx), PDF (.pdf), and text files • Max 10MB</div>
                        </div>
                    </div>
                </div>

                <!-- AI Processing Section (Hidden initially) -->
                <div class="ai-section" id="ai-processing">
                    <div class="ai-header">
                        <div class="ai-icon">🤖</div>
                        <div class="ai-status">
                            <div class="ai-status-title">AI is analyzing your content...</div>
                            <div class="ai-status-text">Extracting key information and determining article type</div>
                        </div>
                    </div>
                    <div class="ai-progress">
                        <div class="ai-progress-bar" style="width: 65%; background-size: 200% 100%;"></div>
                    </div>
                </div>

                <!-- Article Type Selection (Hidden initially) -->
                <div class="type-selection" id="type-selection">
                    <div class="content-section">
                        <div class="section-header">
                            <div class="section-title">
                                <div class="section-icon">🎯</div>
                                <span>Select Article Type</span>
                            </div>
                        </div>

                        <div class="type-grid">
                            <div class="type-card selected" onclick="selectType(this)">
                                <div class="type-icon">📖</div>
                                <div class="type-title">How-To / Tutorial</div>
                                <div class="type-description">Step-by-step instructions for completing a specific task or process</div>
                                <span class="type-confidence">95% confidence match</span>
                            </div>

                            <div class="type-card" onclick="selectType(this)">
                                <div class="type-icon">🔧</div>
                                <div class="type-title">Troubleshooting</div>
                                <div class="type-description">Problem diagnosis and solutions for fixing issues</div>
                            </div>

                            <div class="type-card" onclick="selectType(this)">
                                <div class="type-icon">📢</div>
                                <div class="type-title">Release Note</div>
                                <div class="type-description">Product changes, updates, new features, and bug fixes</div>
                            </div>

                            <div class="type-card" onclick="selectType(this)">
                                <div class="type-icon">❓</div>
                                <div class="type-title">FAQ</div>
                                <div class="type-description">Frequently asked questions with concise answers</div>
                            </div>

                            <div class="type-card" onclick="selectType(this
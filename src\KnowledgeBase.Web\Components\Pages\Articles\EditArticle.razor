@page "/articles/{ArticleId:guid}/edit"
@rendermode InteractiveServer
@using KnowledgeBase.Web.Services
@using KnowledgeBase.Web.Services.Models
@using Microsoft.AspNetCore.Components.Rendering
@using System.Text.Json
@inject KnowledgeBaseApiService ApiService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>Edit Article - Knowledge Base</PageTitle>

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="mb-4" />
    <MudSkeleton SkeletonType="SkeletonType.Rectangle" Height="40px" Class="mb-4" />
    <MudSkeleton SkeletonType="SkeletonType.Rectangle" Height="400px" />
}
else if (_article == null)
{
    <MudAlert Severity="Severity.Error" Class="mb-4">
        Article not found. <MudLink Href="/articles">Return to articles</MudLink>
    </MudAlert>
}
else
{
    <MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-4">
        <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2">
            <MudIconButton Icon="@Icons.Material.Filled.ArrowBack" Href="@($"/articles/{ArticleId}")" />
            <MudText Typo="Typo.h4">Edit Article</MudText>
            <MudChip T="string" Size="Size.Small" Color="@GetStatusColor(_article.Status)">@_article.Status</MudChip>
        </MudStack>
        <MudStack Row="true" Spacing="2">
            <MudButton Variant="Variant.Outlined" Color="Color.Default" OnClick="@Cancel">
                Cancel
            </MudButton>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@SaveChanges"
                       StartIcon="@Icons.Material.Filled.Save" Disabled="@_saving">
                @if (_saving)
                {
                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                }
                Save Changes
            </MudButton>
        </MudStack>
    </MudStack>

    <MudGrid>
        @* Main Editor *@
        <MudItem xs="12" md="8">
            <MudCard>
                <MudCardContent>
                    <MudForm @ref="_form" @bind-IsValid="_formValid">
                        <MudStack Spacing="3">
                            <MudTextField @bind-Value="_article.Title" Label="Title" Required="true"
                                          RequiredError="Title is required" MaxLength="300" Counter="300"
                                          Variant="Variant.Outlined" />

                            <MudTextField @bind-Value="_article.Summary" Label="Summary"
                                          Lines="3" MaxLength="1000" Counter="1000"
                                          HelperText="Brief description of the article content"
                                          Variant="Variant.Outlined" />

                            <MudTabs Elevation="0" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-3">
                                <MudTabPanel Text="Structured Editor">
                                    @RenderStructuredEditor()
                                </MudTabPanel>
                                <MudTabPanel Text="JSON Editor">
                                    <MudStack Spacing="2">
                                        <MudAlert Severity="Severity.Info" Dense="true">
                                            <MudText Typo="Typo.body2">
                                                Edit the article content in JSON format. The content structure varies by article type.
                                            </MudText>
                                        </MudAlert>
                                        <MudTextField @bind-Value="_contentText" Label="Content (JSON)"
                                                      Lines="20" Variant="Variant.Outlined"
                                                      HelperText="Edit the article content in JSON format"
                                                      Style="font-family: 'Consolas', monospace; font-size: 14px;" />
                                        <MudStack Row="true" Spacing="2" Justify="Justify.FlexEnd">
                                            <MudButton Size="Size.Small" Variant="Variant.Outlined"
                                                       OnClick="@FormatJsonContent"
                                                       StartIcon="@Icons.Material.Filled.FormatAlignLeft">
                                                Format JSON
                                            </MudButton>
                                            <MudButton Size="Size.Small" Variant="Variant.Outlined"
                                                       OnClick="@ValidateJsonContent"
                                                       StartIcon="@Icons.Material.Filled.CheckCircle">
                                                Validate JSON
                                            </MudButton>
                                        </MudStack>
                                    </MudStack>
                                </MudTabPanel>
                                <MudTabPanel Text="JSON View">
                                    <MudPaper Class="pa-3" Elevation="0" Style="background-color: var(--mud-palette-background-grey);">
                                        <pre style="white-space: pre-wrap; font-family: 'Consolas', monospace; margin: 0; max-height: 400px; overflow-y: auto;">@FormatJson(_article.Content)</pre>
                                    </MudPaper>
                                </MudTabPanel>
                            </MudTabs>
                        </MudStack>
                    </MudForm>
                </MudCardContent>
            </MudCard>

            @* Version History Link *@
            <MudPaper Class="pa-3 mt-4" Elevation="1">
                <MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        <MudIcon Icon="@Icons.Material.Filled.History" Size="Size.Small" Class="mr-1" />
                        Version @_article.Version - Last modified @_article.LastModifiedDate.ToLocalTime().ToString("MMM dd, yyyy HH:mm")
                    </MudText>
                    <MudButton Variant="Variant.Text" Color="Color.Primary" Size="Size.Small"
                               Href="@($"/articles/{ArticleId}/history")">
                        View History
                    </MudButton>
                </MudStack>
            </MudPaper>
        </MudItem>

        @* Sidebar *@
        <MudItem xs="12" md="4">
            @* Metadata Card *@
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">Article Properties</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Spacing="3">
                        <MudSelect T="Guid" @bind-Value="_article.ModuleId" Label="Module" Variant="Variant.Outlined">
                            @foreach (var module in _modules)
                            {
                                <MudSelectItem T="Guid" Value="@module.ModuleId">@module.Name</MudSelectItem>
                            }
                        </MudSelect>

                        <MudSelect T="string" @bind-Value="_article.ArticleType" Label="Article Type" Variant="Variant.Outlined">
                            @foreach (var type in ArticleTypes)
                            {
                                <MudSelectItem T="string" Value="@type">@type</MudSelectItem>
                            }
                        </MudSelect>

                        <MudTextField @bind-Value="_article.Category" Label="Category" Variant="Variant.Outlined"
                                      HelperText="e.g., configuration, troubleshooting, setup" />

                        @if (_article.ArticleType == "ReleaseNote")
                        {
                            <MudTextField @bind-Value="_releaseVersion" Label="Version" Placeholder="e.g., 2.1.0"
                                          Variant="Variant.Outlined"
                                          HelperText="Semantic version number for this release" />
                        }

                        <div>
                            <MudText Typo="Typo.caption" Class="mb-1">Tags</MudText>
                            <MudChipSet T="string" AllClosable="true" OnClose="@RemoveTag">
                                @foreach (var tag in _tags)
                                {
                                    <MudChip T="string" Value="@tag" Size="Size.Small" Variant="Variant.Outlined"
                                             Color="Color.Primary">@tag</MudChip>
                                }
                            </MudChipSet>
                            <MudTextField @bind-Value="_tagInput" Label="Add Tag" Variant="Variant.Outlined"
                                          Margin="Margin.Dense" Class="mt-1"
                                          HelperText="Press Enter to add a tag"
                                          OnKeyDown="@OnTagKeyDown"
                                          Adornment="Adornment.End" AdornmentIcon="@Icons.Material.Filled.Add"
                                          OnAdornmentClick="@AddTag" />
                        </div>

                        <MudSelect T="string" @bind-Value="_difficulty" Label="Difficulty" Variant="Variant.Outlined">
                            @foreach (var diff in DifficultyLevels)
                            {
                                <MudSelectItem T="string" Value="@diff">@diff</MudSelectItem>
                            }
                        </MudSelect>

                        <MudNumericField T="int?" @bind-Value="_article.EstimatedTimeMinutes" Label="Est. Read Time (min)"
                                         Variant="Variant.Outlined" Min="1" Max="120" />
                    </MudStack>
                </MudCardContent>
            </MudCard>

            @* Status Actions *@
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">Status Actions</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Spacing="2">
                        @if (_article.Status == "Draft")
                        {
                            <MudButton Variant="Variant.Filled" Color="Color.Primary" FullWidth="true"
                                       StartIcon="@Icons.Material.Filled.Send" OnClick="@SubmitForReview">
                                Submit for Review
                            </MudButton>
                        }
                        @if (_article.Status == "InReview")
                        {
                            <MudButton Variant="Variant.Filled" Color="Color.Success" FullWidth="true"
                                       StartIcon="@Icons.Material.Filled.Publish" OnClick="@Publish">
                                Publish Article
                            </MudButton>
                            <MudButton Variant="Variant.Outlined" Color="Color.Warning" FullWidth="true"
                                       StartIcon="@Icons.Material.Filled.Undo" OnClick="@ReturnToDraft">
                                Return to Draft
                            </MudButton>
                        }
                        @if (_article.Status == "Published")
                        {
                            <MudButton Variant="Variant.Outlined" Color="Color.Warning" FullWidth="true"
                                       StartIcon="@Icons.Material.Filled.Archive" OnClick="@Archive">
                                Archive Article
                            </MudButton>
                            <MudButton Variant="Variant.Outlined" Color="Color.Default" FullWidth="true"
                                       StartIcon="@Icons.Material.Filled.Edit" OnClick="@CreateNewVersion">
                                Create New Version
                            </MudButton>
                        }
                        @if (_article.Status == "Archived")
                        {
                            <MudButton Variant="Variant.Filled" Color="Color.Primary" FullWidth="true"
                                       StartIcon="@Icons.Material.Filled.Unarchive" OnClick="@Republish">
                                Republish Article
                            </MudButton>
                        }
                    </MudStack>
                </MudCardContent>
            </MudCard>

        </MudItem>
    </MudGrid>
}

@code {
    [Parameter] public Guid ArticleId { get; set; }

    private bool _loading = true;
    private bool _saving = false;
    private bool _formValid = false;
    private MudForm? _form;

    private ArticleDto? _article;
    private string _contentText = "";
    private List<string> _tags = new();
    private string _tagInput = "";
    private string _releaseVersion = "";
    private string _difficulty = "Beginner";
    private List<ModuleDto> _modules = new();

    private static readonly string[] ArticleTypes = { "HowTo", "Troubleshooting", "ReleaseNote", "Faq", "ProductOverview", "BestPractice" };
    private static readonly string[] DifficultyLevels = { "Beginner", "Intermediate", "Advanced" };

    protected override async Task OnInitializedAsync()
    {
        await LoadArticle();
    }

    private async Task LoadArticle()
    {
        _loading = true;
        StateHasChanged();

        try
        {
            // Load modules and article in parallel
            var modulesTask = ApiService.GetModulesAsync();
            var articleTask = ApiService.GetArticleAsync(ArticleId);

            await Task.WhenAll(modulesTask, articleTask);

            _modules = await modulesTask;
            _article = await articleTask;

            if (_article != null)
            {
                _contentText = FormatContentForEditing(_article.Content);
                _tags = ParseTags(_article.Tags);
                _difficulty = _article.Difficulty ?? "Beginner";

                // Extract version from tags for release notes
                if (_article.ArticleType == "ReleaseNote")
                {
                    var versionTag = _tags.FirstOrDefault(t => t.StartsWith("v", StringComparison.OrdinalIgnoreCase) && t.Length > 1 && char.IsDigit(t[1]));
                    if (versionTag != null)
                    {
                        _releaseVersion = versionTag.TrimStart('v', 'V');
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading article: {ex.Message}", Severity.Error);
        }

        _loading = false;
        StateHasChanged();
    }

    private async Task SaveChanges()
    {
        if (_form == null || _article == null) return;

        await _form.Validate();
        if (!_formValid) return;

        _saving = true;
        StateHasChanged();

        try
        {
            // Build final tags list with version tag for release notes
            var saveTags = new List<string>(_tags);
            if (_article.ArticleType == "ReleaseNote" && !string.IsNullOrWhiteSpace(_releaseVersion))
            {
                saveTags.RemoveAll(t => t.StartsWith("v", StringComparison.OrdinalIgnoreCase) && t.Length > 1 && char.IsDigit(t[1]));
                saveTags.Add($"v{_releaseVersion}");
            }

            var request = new UpdateArticleRequest
            {
                Title = _article.Title,
                Summary = _article.Summary,
                Content = _contentText,
                ModuleId = _article.ModuleId,
                Category = _article.Category,
                Tags = saveTags,
                EstimatedTimeMinutes = _article.EstimatedTimeMinutes,
                Difficulty = _difficulty
            };

            var updated = await ApiService.UpdateArticleAsync(ArticleId, request);
            if (updated != null)
            {
                _article = updated;
                Snackbar.Add("Article saved successfully", Severity.Success);
            }
            else
            {
                Snackbar.Add("Failed to save article", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error saving article: {ex.Message}", Severity.Error);
        }

        _saving = false;
        StateHasChanged();
    }

    private void Cancel()
    {
        Navigation.NavigateTo($"/articles/{ArticleId}");
    }

    private async Task SubmitForReview()
    {
        if (_article == null) return;

        await SaveChanges();
        var success = await ApiService.UpdateArticleStatusAsync(ArticleId, "InReview");
        if (success)
        {
            _article.Status = "InReview";
            Snackbar.Add("Article submitted for review", Severity.Success);
        }
        else
        {
            Snackbar.Add("Failed to submit for review", Severity.Error);
        }
        StateHasChanged();
    }

    private async Task Publish()
    {
        if (_article == null) return;

        await SaveChanges();
        var success = await ApiService.UpdateArticleStatusAsync(ArticleId, "Published");
        if (success)
        {
            Snackbar.Add("Article published successfully", Severity.Success);
            Navigation.NavigateTo($"/articles/{ArticleId}");
        }
        else
        {
            Snackbar.Add("Failed to publish article", Severity.Error);
        }
    }

    private async Task ReturnToDraft()
    {
        if (_article == null) return;

        var success = await ApiService.UpdateArticleStatusAsync(ArticleId, "Draft");
        if (success)
        {
            _article.Status = "Draft";
            Snackbar.Add("Article returned to draft status", Severity.Info);
        }
        else
        {
            Snackbar.Add("Failed to return to draft", Severity.Error);
        }
        StateHasChanged();
    }

    private async Task Archive()
    {
        if (_article == null) return;

        var result = await DialogService.ShowMessageBox(
            "Archive Article",
            "Are you sure you want to archive this article? It will no longer be visible to users.",
            yesText: "Archive", cancelText: "Cancel");

        if (result == true)
        {
            var success = await ApiService.UpdateArticleStatusAsync(ArticleId, "Archived");
            if (success)
            {
                _article.Status = "Archived";
                Snackbar.Add("Article archived", Severity.Info);
            }
            else
            {
                Snackbar.Add("Failed to archive article", Severity.Error);
            }
            StateHasChanged();
        }
    }

    private async Task Republish()
    {
        if (_article == null) return;

        var success = await ApiService.UpdateArticleStatusAsync(ArticleId, "Published");
        if (success)
        {
            _article.Status = "Published";
            Snackbar.Add("Article republished", Severity.Success);
        }
        else
        {
            Snackbar.Add("Failed to republish article", Severity.Error);
        }
        StateHasChanged();
    }

    private async Task CreateNewVersion()
    {
        if (_article == null) return;

        // TODO: Implement create new version API endpoint
        Snackbar.Add("Create new version feature coming soon", Severity.Info);
        StateHasChanged();
    }

    private void AddTag()
    {
        var tag = _tagInput.Trim();
        if (!string.IsNullOrEmpty(tag) && !_tags.Contains(tag, StringComparer.OrdinalIgnoreCase))
        {
            _tags.Add(tag);
        }
        _tagInput = "";
    }

    private void RemoveTag(MudChip<string> chip)
    {
        if (chip.Value != null)
        {
            _tags.Remove(chip.Value);
        }
    }

    private void OnTagKeyDown(KeyboardEventArgs args)
    {
        if (args.Key == "Enter")
        {
            AddTag();
        }
    }

    private string FormatJson(object? content)
    {
        if (content == null) return "";

        try
        {
            var options = new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
            return System.Text.Json.JsonSerializer.Serialize(content, options);
        }
        catch
        {
            return content.ToString() ?? "";
        }
    }

    private List<string> ParseTags(List<string>? tags)
    {
        return tags ?? new List<string>();
    }

    private string FormatContentForEditing(object? content)
    {
        if (content == null) return "";

        try
        {
            // If content is already a formatted string, return it as-is
            if (content is string str)
            {
                // Try to parse and reformat if it's JSON
                try
                {
                    using var doc = System.Text.Json.JsonDocument.Parse(str);
                    var options = new System.Text.Json.JsonSerializerOptions
                    {
                        WriteIndented = true,
                        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                    };
                    return System.Text.Json.JsonSerializer.Serialize(doc, options);
                }
                catch
                {
                    // If not valid JSON, return the string as-is
                    return str;
                }
            }

            // If content is an object, serialize it with proper formatting
            var jsonOptions = new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
            return System.Text.Json.JsonSerializer.Serialize(content, jsonOptions);
        }
        catch
        {
            return content.ToString() ?? "";
        }
    }

    private RenderFragment RenderStructuredEditor() => builder =>
    {
        if (_article?.Content == null)
        {
            builder.OpenComponent<MudAlert>(0);
            builder.AddAttribute(1, "Severity", Severity.Info);
            builder.AddAttribute(2, "ChildContent", (RenderFragment)(b => b.AddContent(0, "No content available")));
            builder.CloseComponent();
            return;
        }

        try
        {
            var contentJson = System.Text.Json.JsonSerializer.Serialize(_article.Content);
            using var doc = System.Text.Json.JsonDocument.Parse(contentJson);
            var root = doc.RootElement;

            var seq = 0;

            // Render based on article type
            switch (_article.ArticleType?.ToLower())
            {
                case "howto":
                    RenderHowToContent(builder, root, ref seq);
                    break;
                case "troubleshooting":
                    RenderTroubleshootingContent(builder, root, ref seq);
                    break;
                case "releasenote":
                    RenderReleaseNoteContent(builder, root, ref seq);
                    break;
                default:
                    RenderGenericContent(builder, root, ref seq);
                    break;
            }
        }
        catch (Exception ex)
        {
            builder.OpenComponent<MudAlert>(0);
            builder.AddAttribute(1, "Severity", Severity.Warning);
            builder.AddAttribute(2, "ChildContent", (RenderFragment)(b =>
            {
                b.AddContent(0, "Unable to parse content structure. ");
                b.OpenComponent<MudButton>(1);
                b.AddAttribute(2, "Size", Size.Small);
                b.AddAttribute(3, "Variant", Variant.Text);
                b.AddAttribute(4, "ChildContent", (RenderFragment)(btn => btn.AddContent(0, "Switch to JSON Editor")));
                b.CloseComponent();
            }));
            builder.CloseComponent();
        }
    };

    private void RenderHowToContent(RenderTreeBuilder builder, JsonElement root, ref int seq)
    {
        builder.OpenComponent<MudStack>(seq++);
        builder.AddAttribute(seq++, "Spacing", 3);
        builder.AddAttribute(seq++, "ChildContent", (RenderFragment)(stackBuilder =>
        {
            var stackSeq = 0;

            // Prerequisites
            if (root.TryGetProperty("prerequisites", out var prereqs) && prereqs.ValueKind == JsonValueKind.Array)
            {
                RenderSection(stackBuilder, "Prerequisites", prereqs, ref stackSeq);
            }

            // Steps
            if (root.TryGetProperty("steps", out var steps) && steps.ValueKind == JsonValueKind.Array)
            {
                RenderStepsSection(stackBuilder, "Steps", steps, ref stackSeq);
            }

            // Validation
            if (root.TryGetProperty("validation", out var validation))
            {
                RenderObjectSection(stackBuilder, "Validation", validation, ref stackSeq);
            }

            // Troubleshooting
            if (root.TryGetProperty("troubleshooting", out var troubleshooting) && troubleshooting.ValueKind == JsonValueKind.Array)
            {
                RenderSection(stackBuilder, "Troubleshooting", troubleshooting, ref stackSeq);
            }

            // Next Steps
            if (root.TryGetProperty("next_steps", out var nextSteps) && nextSteps.ValueKind == JsonValueKind.Array)
            {
                RenderSection(stackBuilder, "Next Steps", nextSteps, ref stackSeq);
            }
        }));
        builder.CloseComponent();
    }

    private void RenderTroubleshootingContent(RenderTreeBuilder builder, JsonElement root, ref int seq)
    {
        builder.OpenComponent<MudStack>(seq++);
        builder.AddAttribute(seq++, "Spacing", 3);
        builder.AddAttribute(seq++, "ChildContent", (RenderFragment)(stackBuilder =>
        {
            var stackSeq = 0;

            // Symptoms
            if (root.TryGetProperty("symptoms", out var symptoms) && symptoms.ValueKind == JsonValueKind.Array)
            {
                RenderSection(stackBuilder, "Symptoms", symptoms, ref stackSeq);
            }

            // Root Causes
            if (root.TryGetProperty("root_causes", out var causes) && causes.ValueKind == JsonValueKind.Array)
            {
                RenderSection(stackBuilder, "Root Causes", causes, ref stackSeq);
            }

            // Workarounds
            if (root.TryGetProperty("workarounds", out var workarounds) && workarounds.ValueKind == JsonValueKind.Array)
            {
                RenderSection(stackBuilder, "Workarounds", workarounds, ref stackSeq);
            }

            // Prevention
            if (root.TryGetProperty("prevention", out var prevention) && prevention.ValueKind == JsonValueKind.Array)
            {
                RenderSection(stackBuilder, "Prevention", prevention, ref stackSeq);
            }
        }));
        builder.CloseComponent();
    }

    private void RenderReleaseNoteContent(RenderTreeBuilder builder, JsonElement root, ref int seq)
    {
        builder.OpenComponent<MudStack>(seq++);
        builder.AddAttribute(seq++, "Spacing", 3);
        builder.AddAttribute(seq++, "ChildContent", (RenderFragment)(stackBuilder =>
        {
            var stackSeq = 0;

            // Version and Release Info
            if (root.TryGetProperty("version", out var version))
            {
                RenderPropertyCard(stackBuilder, "Version", version.GetString() ?? "", ref stackSeq);
            }

            if (root.TryGetProperty("release_date", out var releaseDate))
            {
                RenderPropertyCard(stackBuilder, "Release Date", releaseDate.GetString() ?? "", ref stackSeq);
            }

            // Highlights
            if (root.TryGetProperty("highlights", out var highlights) && highlights.ValueKind == JsonValueKind.Array)
            {
                RenderSection(stackBuilder, "Highlights", highlights, ref stackSeq);
            }

            // Sections
            if (root.TryGetProperty("sections", out var sections) && sections.ValueKind == JsonValueKind.Array)
            {
                RenderSection(stackBuilder, "Release Sections", sections, ref stackSeq);
            }

            // Known Issues
            if (root.TryGetProperty("known_issues", out var knownIssues) && knownIssues.ValueKind == JsonValueKind.Array)
            {
                RenderSection(stackBuilder, "Known Issues", knownIssues, ref stackSeq);
            }
        }));
        builder.CloseComponent();
    }

    private void RenderGenericContent(RenderTreeBuilder builder, JsonElement root, ref int seq)
    {
        builder.OpenComponent<MudStack>(seq++);
        builder.AddAttribute(seq++, "Spacing", 3);
        builder.AddAttribute(seq++, "ChildContent", (RenderFragment)(stackBuilder =>
        {
            var stackSeq = 0;

            foreach (var property in root.EnumerateObject())
            {
                if (property.Value.ValueKind == JsonValueKind.Array)
                {
                    RenderSection(stackBuilder, property.Name, property.Value, ref stackSeq);
                }
                else if (property.Value.ValueKind == JsonValueKind.Object)
                {
                    RenderObjectSection(stackBuilder, property.Name, property.Value, ref stackSeq);
                }
                else
                {
                    var value = property.Value.ValueKind == JsonValueKind.String
                        ? property.Value.GetString() ?? ""
                        : property.Value.ToString();
                    RenderPropertyCard(stackBuilder, property.Name, value, ref stackSeq);
                }
            }
        }));
        builder.CloseComponent();
    }

    private void RenderSection(RenderTreeBuilder builder, string title, JsonElement arrayElement, ref int seq)
    {
        builder.OpenComponent<MudCard>(seq++);
        builder.AddAttribute(seq++, "Class", "mb-3");
        builder.AddAttribute(seq++, "ChildContent", (RenderFragment)(cardBuilder =>
        {
            cardBuilder.OpenComponent<MudCardHeader>(0);
            cardBuilder.AddAttribute(1, "ChildContent", (RenderFragment)(headerBuilder =>
            {
                headerBuilder.OpenComponent<MudText>(0);
                headerBuilder.AddAttribute(1, "Typo", Typo.h6);
                headerBuilder.AddAttribute(2, "ChildContent", (RenderFragment)(b => b.AddContent(0, title)));
                headerBuilder.CloseComponent();
            }));
            cardBuilder.CloseComponent();

            cardBuilder.OpenComponent<MudCardContent>(2);
            cardBuilder.AddAttribute(3, "ChildContent", (RenderFragment)(contentBuilder =>
            {
                var itemSeq = 0;
                foreach (var item in arrayElement.EnumerateArray())
                {
                    contentBuilder.OpenComponent<MudText>(itemSeq++);
                    contentBuilder.AddAttribute(itemSeq++, "Class", "mb-2");
                    var itemText = item.ValueKind == JsonValueKind.String
                        ? item.GetString() ?? ""
                        : item.ToString();
                    contentBuilder.AddAttribute(itemSeq++, "ChildContent", (RenderFragment)(b => b.AddContent(0, $"• {itemText}")));
                    contentBuilder.CloseComponent();
                }
            }));
            cardBuilder.CloseComponent();
        }));
        builder.CloseComponent();
    }

    private void RenderStepsSection(RenderTreeBuilder builder, string title, JsonElement arrayElement, ref int seq)
    {
        builder.OpenComponent<MudCard>(seq++);
        builder.AddAttribute(seq++, "Class", "mb-3");
        builder.AddAttribute(seq++, "ChildContent", (RenderFragment)(cardBuilder =>
        {
            cardBuilder.OpenComponent<MudCardHeader>(0);
            cardBuilder.AddAttribute(1, "ChildContent", (RenderFragment)(headerBuilder =>
            {
                headerBuilder.OpenComponent<MudText>(0);
                headerBuilder.AddAttribute(1, "Typo", Typo.h6);
                headerBuilder.AddAttribute(2, "ChildContent", (RenderFragment)(b => b.AddContent(0, title)));
                headerBuilder.CloseComponent();
            }));
            cardBuilder.CloseComponent();

            cardBuilder.OpenComponent<MudCardContent>(2);
            cardBuilder.AddAttribute(3, "ChildContent", (RenderFragment)(contentBuilder =>
            {
                var stepNum = 1;
                var itemSeq = 0;
                foreach (var step in arrayElement.EnumerateArray())
                {
                    contentBuilder.OpenComponent<MudPaper>(itemSeq++);
                    contentBuilder.AddAttribute(itemSeq++, "Class", "pa-3 mb-2");
                    contentBuilder.AddAttribute(itemSeq++, "Elevation", 1);
                    contentBuilder.AddAttribute(itemSeq++, "ChildContent", (RenderFragment)(stepBuilder =>
                    {
                        stepBuilder.OpenComponent<MudText>(0);
                        stepBuilder.AddAttribute(1, "Typo", Typo.subtitle2);
                        stepBuilder.AddAttribute(2, "Color", Color.Primary);
                        stepBuilder.AddAttribute(3, "ChildContent", (RenderFragment)(b => b.AddContent(0, $"Step {stepNum++}")));
                        stepBuilder.CloseComponent();

                        stepBuilder.OpenComponent<MudText>(4);
                        stepBuilder.AddAttribute(5, "Class", "mt-1");
                        var stepText = step.ValueKind == JsonValueKind.String
                            ? step.GetString() ?? ""
                            : step.ToString();
                        stepBuilder.AddAttribute(6, "ChildContent", (RenderFragment)(b => b.AddContent(0, stepText)));
                        stepBuilder.CloseComponent();
                    }));
                    contentBuilder.CloseComponent();
                }
            }));
            cardBuilder.CloseComponent();
        }));
        builder.CloseComponent();
    }

    private void RenderObjectSection(RenderTreeBuilder builder, string title, JsonElement objectElement, ref int seq)
    {
        builder.OpenComponent<MudCard>(seq++);
        builder.AddAttribute(seq++, "Class", "mb-3");
        builder.AddAttribute(seq++, "ChildContent", (RenderFragment)(cardBuilder =>
        {
            cardBuilder.OpenComponent<MudCardHeader>(0);
            cardBuilder.AddAttribute(1, "ChildContent", (RenderFragment)(headerBuilder =>
            {
                headerBuilder.OpenComponent<MudText>(0);
                headerBuilder.AddAttribute(1, "Typo", Typo.h6);
                headerBuilder.AddAttribute(2, "ChildContent", (RenderFragment)(b => b.AddContent(0, title)));
                headerBuilder.CloseComponent();
            }));
            cardBuilder.CloseComponent();

            cardBuilder.OpenComponent<MudCardContent>(2);
            cardBuilder.AddAttribute(3, "ChildContent", (RenderFragment)(contentBuilder =>
            {
                var propSeq = 0;
                foreach (var property in objectElement.EnumerateObject())
                {
                    contentBuilder.OpenComponent<MudText>(propSeq++);
                    contentBuilder.AddAttribute(propSeq++, "Class", "mb-1");
                    var propValue = property.Value.ValueKind == JsonValueKind.String
                        ? property.Value.GetString() ?? ""
                        : property.Value.ToString();
                    contentBuilder.AddAttribute(propSeq++, "ChildContent", (RenderFragment)(b =>
                        b.AddContent(0, $"{property.Name}: {propValue}")));
                    contentBuilder.CloseComponent();
                }
            }));
            cardBuilder.CloseComponent();
        }));
        builder.CloseComponent();
    }

    private void RenderPropertyCard(RenderTreeBuilder builder, string title, string value, ref int seq)
    {
        builder.OpenComponent<MudPaper>(seq++);
        builder.AddAttribute(seq++, "Class", "pa-3 mb-2");
        builder.AddAttribute(seq++, "Elevation", 1);
        builder.AddAttribute(seq++, "ChildContent", (RenderFragment)(paperBuilder =>
        {
            paperBuilder.OpenComponent<MudText>(0);
            paperBuilder.AddAttribute(1, "Typo", Typo.subtitle2);
            paperBuilder.AddAttribute(2, "ChildContent", (RenderFragment)(b => b.AddContent(0, title)));
            paperBuilder.CloseComponent();

            paperBuilder.OpenComponent<MudText>(3);
            paperBuilder.AddAttribute(4, "Class", "mt-1");
            paperBuilder.AddAttribute(5, "ChildContent", (RenderFragment)(b => b.AddContent(0, value)));
            paperBuilder.CloseComponent();
        }));
        builder.CloseComponent();
    }

    private Color GetStatusColor(string status) => status switch
    {
        "Draft" => Color.Default,
        "InReview" => Color.Warning,
        "Published" => Color.Success,
        "Archived" => Color.Dark,
        _ => Color.Default
    };

    private void FormatJsonContent()
    {
        if (string.IsNullOrWhiteSpace(_contentText))
            return;

        try
        {
            using var doc = System.Text.Json.JsonDocument.Parse(_contentText);
            var options = new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
            _contentText = System.Text.Json.JsonSerializer.Serialize(doc, options);
            Snackbar.Add("JSON formatted successfully", Severity.Success);
        }
        catch (JsonException ex)
        {
            Snackbar.Add($"Invalid JSON: {ex.Message}", Severity.Error);
        }
    }

    private void ValidateJsonContent()
    {
        if (string.IsNullOrWhiteSpace(_contentText))
        {
            Snackbar.Add("No content to validate", Severity.Warning);
            return;
        }

        try
        {
            using var doc = System.Text.Json.JsonDocument.Parse(_contentText);
            Snackbar.Add("JSON is valid", Severity.Success);
        }
        catch (JsonException ex)
        {
            Snackbar.Add($"Invalid JSON: {ex.Message}", Severity.Error);
        }
    }
}

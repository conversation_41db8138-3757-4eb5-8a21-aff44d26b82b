// Azure OpenAI module

@description('Name of the Azure OpenAI resource')
param name string

@description('Location for the resource')
param location string

@description('SKU for the Azure OpenAI resource')
@allowed(['S0'])
param sku string = 'S0'

@description('GPT model name to deploy')
param gptModelName string = 'gpt-4o'

@description('GPT model version')
param gptModelVersion string = '2024-11-20'

@description('Embedding model name to deploy')
param embeddingModelName string = 'text-embedding-ada-002'

@description('Embedding model version')
param embeddingModelVersion string = '2'

@description('Capacity for GPT deployments (1000s of tokens per minute)')
param gptCapacity int = 10

@description('Capacity for embedding deployment (1000s of tokens per minute)')
param embeddingCapacity int = 10

// Azure OpenAI resource
resource openAI 'Microsoft.CognitiveServices/accounts@2024-10-01' = {
  name: name
  location: location
  kind: 'OpenAI'
  sku: {
    name: sku
  }
  properties: {
    customSubDomainName: name
    publicNetworkAccess: 'Enabled'
    networkAcls: {
      defaultAction: 'Allow'
    }
  }
}

// Router deployment - for content classification
resource routerDeployment 'Microsoft.CognitiveServices/accounts/deployments@2024-10-01' = {
  parent: openAI
  name: 'gpt-4-router'
  sku: {
    name: 'Standard'
    capacity: gptCapacity
  }
  properties: {
    model: {
      format: 'OpenAI'
      name: gptModelName
      version: gptModelVersion
    }
    raiPolicyName: 'Microsoft.DefaultV2'
  }
}

// HowTo deployment - for how-to article generation
resource howToDeployment 'Microsoft.CognitiveServices/accounts/deployments@2024-10-01' = {
  parent: openAI
  name: 'gpt-4-howto'
  sku: {
    name: 'Standard'
    capacity: gptCapacity
  }
  properties: {
    model: {
      format: 'OpenAI'
      name: gptModelName
      version: gptModelVersion
    }
    raiPolicyName: 'Microsoft.DefaultV2'
  }
  dependsOn: [routerDeployment]
}

// Troubleshooting deployment - for troubleshooting article generation
resource troubleshootingDeployment 'Microsoft.CognitiveServices/accounts/deployments@2024-10-01' = {
  parent: openAI
  name: 'gpt-4-troubleshooting'
  sku: {
    name: 'Standard'
    capacity: gptCapacity
  }
  properties: {
    model: {
      format: 'OpenAI'
      name: gptModelName
      version: gptModelVersion
    }
    raiPolicyName: 'Microsoft.DefaultV2'
  }
  dependsOn: [howToDeployment]
}

// Evaluator deployment - for quality assessment
resource evaluatorDeployment 'Microsoft.CognitiveServices/accounts/deployments@2024-10-01' = {
  parent: openAI
  name: 'gpt-4-evaluator'
  sku: {
    name: 'Standard'
    capacity: gptCapacity
  }
  properties: {
    model: {
      format: 'OpenAI'
      name: gptModelName
      version: gptModelVersion
    }
    raiPolicyName: 'Microsoft.DefaultV2'
  }
  dependsOn: [troubleshootingDeployment]
}

// Embedding deployment - for vector embeddings
resource embeddingDeployment 'Microsoft.CognitiveServices/accounts/deployments@2024-10-01' = {
  parent: openAI
  name: 'text-embedding-ada-002'
  sku: {
    name: 'Standard'
    capacity: embeddingCapacity
  }
  properties: {
    model: {
      format: 'OpenAI'
      name: embeddingModelName
      version: embeddingModelVersion
    }
    raiPolicyName: 'Microsoft.DefaultV2'
  }
  dependsOn: [evaluatorDeployment]
}

// Outputs
output id string = openAI.id
output name string = openAI.name
output endpoint string = openAI.properties.endpoint
output apiKey string = openAI.listKeys().key1

// Deployment names for app configuration
output routerDeploymentName string = routerDeployment.name
output howToDeploymentName string = howToDeployment.name
output troubleshootingDeploymentName string = troubleshootingDeployment.name
output evaluatorDeploymentName string = evaluatorDeployment.name
output embeddingDeploymentName string = embeddingDeployment.name

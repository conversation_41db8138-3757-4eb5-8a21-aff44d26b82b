using System.ComponentModel.DataAnnotations;

namespace KnowledgeBase.Core.Entities;

/// <summary>
/// Represents a module/subject area in the knowledge base.
/// </summary>
public class Module
{
    [Key]
    public Guid ModuleId { get; set; }

    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    [MaxLength(500)]
    public string? Description { get; set; }

    public Guid? SMEUserId { get; set; }

    public Guid? ParentModuleId { get; set; }

    public bool IsActive { get; set; } = true;

    public int DisplayOrder { get; set; }

    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual User? SME { get; set; }
    public virtual Module? ParentModule { get; set; }
    public virtual ICollection<Module> ChildModules { get; set; } = new List<Module>();
    public virtual ICollection<Article> Articles { get; set; } = new List<Article>();
}

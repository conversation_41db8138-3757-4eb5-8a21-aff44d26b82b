# Azure DevOps Setup Guide

This guide explains how to set up CI/CD pipelines for MeshworksKB in Azure DevOps.

## Prerequisites

1. **Azure Subscription** with permissions to create resources
2. **Azure DevOps Organization** and Project
3. **Service Connection** to Azure (Service Principal)

## Quick Start

### 1. Create Azure DevOps Project

1. Go to [Azure DevOps](https://dev.azure.com)
2. Create a new project: `MeshworksKB`
3. Initialize with Git repository

### 2. Push Code to Azure DevOps

```bash
# Add Azure DevOps as remote
git remote add azure https://dev.azure.com/{org}/{project}/_git/MeshworksKB

# Push code
git push azure main
```

### 3. Create Service Connection

1. Project Settings → Service Connections → New
2. Select **Azure Resource Manager**
3. Select **Service Principal (automatic)**
4. Name: `azure-subscription` (or your preferred name)
5. Grant access to all pipelines

### 4. Create Variable Groups

Create two variable groups in Library:

#### `meshworks-kb-dev`
| Variable | Value | Secret |
|----------|-------|--------|
| azureSubscription | azure-subscription | No |
| apiAppServiceName | meshworkskb-dev-api | No |
| webAppServiceName | meshworkskb-dev-web | No |
| sqlAdminPassword | (your password) | Yes |
| azureOpenAIEndpoint | (optional) | No |
| azureOpenAIKey | (optional) | Yes |

#### `meshworks-kb-prod`
| Variable | Value | Secret |
|----------|-------|--------|
| azureSubscription | azure-subscription | No |
| apiAppServiceName | meshworkskb-prod-api | No |
| webAppServiceName | meshworkskb-prod-web | No |
| sqlAdminPassword | (your password) | Yes |
| azureOpenAIEndpoint | (optional) | No |
| azureOpenAIKey | (optional) | Yes |

### 5. Create Environments

1. Pipelines → Environments → New Environment
2. Create: `meshworks-kb-dev`, `meshworks-kb-prod`
3. Add approvals for production environment

### 6. Create Pipelines

#### Main CI/CD Pipeline
1. Pipelines → New Pipeline
2. Select Azure Repos Git
3. Select your repository
4. Select "Existing Azure Pipelines YAML file"
5. Path: `/azure-pipelines.yml`

#### Infrastructure Pipeline
1. Pipelines → New Pipeline
2. Path: `/.azure-devops/infrastructure-pipeline.yml`

## Pipeline Flow

```
┌─────────────────────────────────────────────────────────────────┐
│                        MAIN PIPELINE                            │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐      │
│  │   Build &   │────▶│  Deploy to  │────▶│  Deploy to  │      │
│  │    Test     │     │     Dev     │     │    Prod     │      │
│  └─────────────┘     └─────────────┘     └─────────────┘      │
│        │                   │                   │               │
│   All branches        develop only         main only           │
│                                          (with approval)       │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                   INFRASTRUCTURE PIPELINE                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐      │
│  │  Validate   │────▶│   Deploy    │────▶│   Deploy    │      │
│  │   Bicep     │     │   Azure     │     │  Database   │      │
│  └─────────────┘     │  Resources  │     │   Schema    │      │
│                      └─────────────┘     └─────────────┘      │
│                                                                 │
│  Manual trigger with environment parameter (dev/staging/prod)  │
└─────────────────────────────────────────────────────────────────┘
```

## Infrastructure Deployed

The infrastructure pipeline deploys:

- **App Service Plan** (Basic for dev, Premium for prod)
- **API App Service** (.NET 8)
- **Web App Service** (.NET 8 Blazor)
- **SQL Server** and Database
- **Azure AI Search** (Free tier for dev)
- **Application Insights** and Log Analytics

## First Time Setup

1. Run **Infrastructure Pipeline** first to create Azure resources
2. Wait for resources to be created
3. Main pipeline will automatically deploy code on push

## Troubleshooting

### Common Issues

**Service Connection Error**
- Ensure the service principal has Contributor access to the subscription/resource group

**Database Deployment Fails**
- Check SQL Server firewall allows Azure services
- Verify SQL credentials in variable group

**App Service Deployment Fails**
- Ensure App Service exists (run infrastructure pipeline first)
- Check deployment logs in Azure Portal

### Useful Commands

```bash
# View pipeline runs
az pipelines runs list --project MeshworksKB

# Trigger infrastructure deployment
az pipelines run --name "Infrastructure Pipeline" --parameters environment=dev

# View deployment status
az webapp deployment list-publishing-profiles --name meshworkskb-dev-api --resource-group rg-meshworkskb-dev
```

using KnowledgeBase.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KnowledgeBase.Infrastructure.Data.Configurations;

public class ArticleVersionConfiguration : IEntityTypeConfiguration<ArticleVersion>
{
    public void Configure(EntityTypeBuilder<ArticleVersion> builder)
    {
        builder.ToTable("ArticleVersions");

        builder.HasKey(v => v.VersionId);

        builder.Property(v => v.Title)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(v => v.Summary)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(v => v.Content)
            .IsRequired();

        builder.Property(v => v.ChangeDescription)
            .HasMaxLength(1000);

        builder.Property(v => v.ModifiedDate)
            .HasDefaultValueSql("GETUTCDATE()");

        builder.HasOne(v => v.Article)
            .WithMany(a => a.Versions)
            .HasForeignKey(v => v.ArticleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(v => v.ModifiedBy)
            .WithMany()
            .HasForeignKey(v => v.ModifiedByUserId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasIndex(v => new { v.ArticleId, v.VersionNumber })
            .IsUnique();
    }
}

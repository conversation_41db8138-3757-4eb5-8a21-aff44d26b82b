# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Oo]ut/
[Ll]og/
[Ll]ogs/

# Visual Studio cache/options directory
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates

# Rider
.idea/

# VS Code
.vscode/

# NuGet Packages
*.nupkg
*.snupkg
**/[Pp]ackages/*
!**/[Pp]ackages/build/

# Build outputs
*.dll
*.exe
*.pdb
*.ilk
*.meta
*.obj

# Test results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*
*.trx
TestResult.xml

# NCrunch
*.ncrunchproject
*.ncrunchsolution
_NCrunch_*

# Coverlet coverage output
coverage*.json
coverage*.xml
coverage*.info

# User-specific files
*.rsuser
*.sln.ide/

# Files built by Visual Studio
*_i.c
*_p.c
*_h.h
*.tlb
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.tlog
*.vspscc
*.vssscc

# Windows OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# macOS generated files
.DS_Store
.AppleDouble
.LSOverride

# Application-specific
appsettings.local.json
appsettings.*.local.json
secrets.json

# Azure Functions
local.settings.json

# Entity Framework migrations
*.sql.backup
/nul

using System.ComponentModel.DataAnnotations;

namespace KnowledgeBase.Core.Entities;

/// <summary>
/// Represents a reference to an external ticket system (Jira, Azure DevOps, etc.)
/// </summary>
public class ArticleTicketReference
{
    [Key]
    public Guid Id { get; set; }

    public Guid ArticleId { get; set; }

    [Required]
    [MaxLength(50)]
    public string TicketId { get; set; } = string.Empty;

    [Required]
    [MaxLength(50)]
    public string TicketSystem { get; set; } = string.Empty;

    [MaxLength(500)]
    public string? TicketUrl { get; set; }

    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    // Navigation property
    public virtual Article Article { get; set; } = null!;
}

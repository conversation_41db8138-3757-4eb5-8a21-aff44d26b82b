// Main Bicep template for MeshworksKB infrastructure
// Deploys: App Service Plan, App Services (API + Web), SQL Server, SQL Database,
// Azure AI Search, Azure OpenAI, Key Vault, Application Insights

targetScope = 'resourceGroup'

// Parameters
@description('Environment name (dev, staging, prod)')
@allowed(['dev', 'staging', 'prod'])
param environment string = 'dev'

@description('Azure region for resources')
param location string = 'australiaeast'

@description('Base name for all resources')
param baseName string = 'meshworkskb'

@description('SQL Server administrator login')
param sqlAdminLogin string = 'sqladmin'

@description('SQL Server administrator password')
@secure()
param sqlAdminPassword string

@description('Azure AI Search SKU')
@allowed(['free', 'basic', 'standard', 'standard2', 'standard3'])
param searchSku string = 'basic'

@description('App Service Plan SKU')
@allowed(['F1', 'B1', 'B2', 'S1', 'S2', 'P1v2', 'P2v2', 'P3v2'])
param appServiceSku string = 'B1'

@description('SQL Database SKU')
@allowed(['Basic', 'S0', 'S1', 'S2', 'P1', 'P2'])
param sqlDatabaseSku string = 'S0'

@description('Deploy a new Azure OpenAI resource (false = use existing AI Foundry endpoint)')
param deployOpenAI bool = false

@description('Existing Azure AI Foundry endpoint (used when deployOpenAI = false)')
param aiFoundryEndpoint string = 'https://aifoundry-meshworkskb-d-resource.services.ai.azure.com'

@description('GPT model/deployment name in AI Foundry')
param gptDeploymentName string = 'gpt-4.1'

@description('Embedding model/deployment name in AI Foundry')
param embeddingDeploymentName string = 'text-embedding-3-small'

@description('GPT model to deploy (only used when deployOpenAI = true)')
param gptModelName string = 'gpt-4o'

@description('GPT model version (only used when deployOpenAI = true)')
param gptModelVersion string = '2024-11-20'

// Variables
var resourceSuffix = '${baseName}-${environment}'
var appServicePlanName = 'asp-${resourceSuffix}'
var apiAppName = 'api-${resourceSuffix}'
var webAppName = 'web-${resourceSuffix}'
var sqlServerName = 'sql-${resourceSuffix}'
var sqlDatabaseName = 'sqldb-${resourceSuffix}'
var searchServiceName = 'search-${replace(resourceSuffix, '-', '')}'
var keyVaultName = 'kv-${replace(resourceSuffix, '-', '')}'
var appInsightsName = 'ai-${resourceSuffix}'
var logAnalyticsName = 'log-${resourceSuffix}'
var openAIName = 'oai-${replace(resourceSuffix, '-', '')}'

// Base app settings for API
var baseApiAppSettings = [
  {
    name: 'ASPNETCORE_ENVIRONMENT'
    value: environment == 'prod' ? 'Production' : 'Development'
  }
  {
    name: 'KeyVault__VaultUri'
    value: keyVault.outputs.vaultUri
  }
  {
    name: 'AzureSearch__ServiceEndpoint'
    value: searchService.outputs.endpoint
  }
]

// AI Foundry app settings (when using existing AI Foundry endpoint)
var aiFoundryAppSettings = [
  {
    name: 'AzureOpenAI__Endpoint'
    value: aiFoundryEndpoint
  }
  {
    name: 'AzureOpenAI__DeploymentNames__Router'
    value: gptDeploymentName
  }
  {
    name: 'AzureOpenAI__DeploymentNames__HowTo'
    value: gptDeploymentName
  }
  {
    name: 'AzureOpenAI__DeploymentNames__Troubleshooting'
    value: gptDeploymentName
  }
  {
    name: 'AzureOpenAI__DeploymentNames__Evaluator'
    value: gptDeploymentName
  }
  {
    name: 'AzureOpenAI__DeploymentNames__Embedding'
    value: embeddingDeploymentName
  }
]

// Log Analytics Workspace
module logAnalytics 'modules/logAnalytics.bicep' = {
  name: 'logAnalytics'
  params: {
    name: logAnalyticsName
    location: location
  }
}

// Application Insights
module appInsights 'modules/appInsights.bicep' = {
  name: 'appInsights'
  params: {
    name: appInsightsName
    location: location
    logAnalyticsWorkspaceId: logAnalytics.outputs.id
  }
}

// Key Vault
module keyVault 'modules/keyVault.bicep' = {
  name: 'keyVault'
  params: {
    name: keyVaultName
    location: location
  }
}

// SQL Server and Database
module sqlServer 'modules/sqlServer.bicep' = {
  name: 'sqlServer'
  params: {
    serverName: sqlServerName
    databaseName: sqlDatabaseName
    location: location
    adminLogin: sqlAdminLogin
    adminPassword: sqlAdminPassword
    databaseSku: sqlDatabaseSku
  }
}

// Azure AI Search
module searchService 'modules/searchService.bicep' = {
  name: 'searchService'
  params: {
    name: searchServiceName
    location: location
    sku: searchSku
  }
}

// Azure OpenAI
module openAI 'modules/openAI.bicep' = if (deployOpenAI) {
  name: 'openAI'
  params: {
    name: openAIName
    location: location
    gptModelName: gptModelName
    gptModelVersion: gptModelVersion
  }
}

// App Service Plan
module appServicePlan 'modules/appServicePlan.bicep' = {
  name: 'appServicePlan'
  params: {
    name: appServicePlanName
    location: location
    sku: appServiceSku
  }
}

// API App Service
module apiApp 'modules/appService.bicep' = {
  name: 'apiApp'
  params: {
    name: apiAppName
    location: location
    appServicePlanId: appServicePlan.outputs.id
    appInsightsConnectionString: appInsights.outputs.connectionString
    appInsightsInstrumentationKey: appInsights.outputs.instrumentationKey
    appSettings: deployOpenAI ? concat(baseApiAppSettings, [
      {
        name: 'AzureOpenAI__Endpoint'
        value: openAI!.outputs.endpoint
      }
      {
        name: 'AzureOpenAI__DeploymentNames__Router'
        value: openAI!.outputs.routerDeploymentName
      }
      {
        name: 'AzureOpenAI__DeploymentNames__HowTo'
        value: openAI!.outputs.howToDeploymentName
      }
      {
        name: 'AzureOpenAI__DeploymentNames__Troubleshooting'
        value: openAI!.outputs.troubleshootingDeploymentName
      }
      {
        name: 'AzureOpenAI__DeploymentNames__Evaluator'
        value: openAI!.outputs.evaluatorDeploymentName
      }
      {
        name: 'AzureOpenAI__DeploymentNames__Embedding'
        value: openAI!.outputs.embeddingDeploymentName
      }
    ]) : concat(baseApiAppSettings, aiFoundryAppSettings)
  }
}

// Web App Service
module webApp 'modules/appService.bicep' = {
  name: 'webApp'
  params: {
    name: webAppName
    location: location
    appServicePlanId: appServicePlan.outputs.id
    appInsightsConnectionString: appInsights.outputs.connectionString
    appInsightsInstrumentationKey: appInsights.outputs.instrumentationKey
    appSettings: [
      {
        name: 'ASPNETCORE_ENVIRONMENT'
        value: environment == 'prod' ? 'Production' : 'Development'
      }
      {
        name: 'ApiBaseUrl'
        value: 'https://${apiAppName}.azurewebsites.net'
      }
    ]
  }
}

// Store secrets in Key Vault
module keyVaultSecrets 'modules/keyVaultSecrets.bicep' = {
  name: 'keyVaultSecrets'
  params: {
    keyVaultName: keyVault.outputs.name
    secrets: concat([
      {
        name: 'SqlConnectionString'
        value: sqlServer.outputs.connectionString
      }
      {
        name: 'SearchServiceApiKey'
        value: searchService.outputs.adminKey
      }
    ], deployOpenAI ? [
      {
        name: 'AzureOpenAIApiKey'
        value: openAI!.outputs.apiKey
      }
    ] : [])
  }
}

// Grant Key Vault access to App Services
module apiKeyVaultAccess 'modules/keyVaultAccess.bicep' = {
  name: 'apiKeyVaultAccess'
  params: {
    keyVaultName: keyVault.outputs.name
    principalId: apiApp.outputs.principalId
  }
}

module webKeyVaultAccess 'modules/keyVaultAccess.bicep' = {
  name: 'webKeyVaultAccess'
  params: {
    keyVaultName: keyVault.outputs.name
    principalId: webApp.outputs.principalId
  }
}

// Outputs
output apiAppUrl string = 'https://${apiApp.outputs.defaultHostName}'
output webAppUrl string = 'https://${webApp.outputs.defaultHostName}'
output sqlServerFqdn string = sqlServer.outputs.fullyQualifiedDomainName
output searchServiceEndpoint string = searchService.outputs.endpoint
output keyVaultUri string = keyVault.outputs.vaultUri
output appInsightsConnectionString string = appInsights.outputs.connectionString
output openAIEndpoint string = deployOpenAI ? openAI!.outputs.endpoint : aiFoundryEndpoint

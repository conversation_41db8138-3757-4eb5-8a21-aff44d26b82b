@page "/search"
@rendermode InteractiveServer
@using KnowledgeBase.Web.Services
@using KnowledgeBase.Web.Services.Models
@inject KnowledgeBaseApiService ApiService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar

<PageTitle>Search Results - Knowledge Base</PageTitle>

<MudText Typo="Typo.h4" Class="mb-4">Search Results</MudText>

@* Search Bar *@
<MudPaper Class="pa-4 mb-4">
    <MudGrid>
        <MudItem xs="12" md="8">
            <MudTextField @bind-Value="_searchQuery" Label="Search" Placeholder="Search articles..."
                          Adornment="Adornment.Start" AdornmentIcon="@Icons.Material.Filled.Search"
                          Variant="Variant.Outlined" OnKeyDown="@OnSearchKeyDown" Immediate="true" />
        </MudItem>
        <MudItem xs="12" md="4" Class="d-flex align-end">
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@PerformSearch"
                       StartIcon="@Icons.Material.Filled.Search" FullWidth="true" Disabled="@_searching">
                @if (_searching)
                {
                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                }
                Search
            </MudButton>
        </MudItem>
    </MudGrid>
</MudPaper>

@* Filters *@
<MudExpansionPanels Class="mb-4">
    <MudExpansionPanel Text="Filters" Expanded="@_hasActiveFilters">
        <MudGrid>
            <MudItem xs="12" sm="6" md="3">
                <MudSelect T="string" Value="_filterType" ValueChanged="OnTypeFilterChanged" Label="Article Type" Clearable="true" Variant="Variant.Outlined">
                    @foreach (var type in ArticleTypes)
                    {
                        <MudSelectItem T="string" Value="@type">@type</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudSelect T="Guid?" Value="_filterModuleId" ValueChanged="OnModuleFilterChanged" Label="Module" Clearable="true" Variant="Variant.Outlined">
                    @foreach (var module in _modules)
                    {
                        <MudSelectItem T="Guid?" Value="@((Guid?)module.ModuleId)">@module.Name</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudSelect T="string" Value="_filterCategory" ValueChanged="OnCategoryFilterChanged" Label="Category" Clearable="true" Variant="Variant.Outlined">
                    @foreach (var category in _categories)
                    {
                        <MudSelectItem T="string" Value="@category">@category</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudSelect T="string" Value="_sortBy" ValueChanged="OnSortChanged" Label="Sort By" Variant="Variant.Outlined">
                    <MudSelectItem T="string" Value="@("relevance")">Relevance</MudSelectItem>
                    <MudSelectItem T="string" Value="@("date_desc")">Newest First</MudSelectItem>
                    <MudSelectItem T="string" Value="@("date_asc")">Oldest First</MudSelectItem>
                    <MudSelectItem T="string" Value="@("views")">Most Viewed</MudSelectItem>
                    <MudSelectItem T="string" Value="@("rating")">Highest Rated</MudSelectItem>
                </MudSelect>
            </MudItem>
        </MudGrid>
        @if (_hasActiveFilters)
        {
            <MudStack Row="true" Class="mt-3" Spacing="1">
                <MudText Typo="Typo.body2" Class="mr-2">Active filters:</MudText>
                @if (!string.IsNullOrEmpty(_filterType))
                {
                    <MudChip T="string" Size="Size.Small" Color="Color.Primary" OnClose="@(() => OnTypeFilterChanged(null))">
                        Type: @_filterType
                    </MudChip>
                }
                @if (_filterModuleId.HasValue)
                {
                    <MudChip T="string" Size="Size.Small" Color="Color.Primary" OnClose="@(() => OnModuleFilterChanged(null))">
                        Module: @(_modules.FirstOrDefault(m => m.ModuleId == _filterModuleId)?.Name ?? "Unknown")
                    </MudChip>
                }
                @if (!string.IsNullOrEmpty(_filterCategory))
                {
                    <MudChip T="string" Size="Size.Small" Color="Color.Primary" OnClose="@(() => OnCategoryFilterChanged(null))">
                        Category: @_filterCategory
                    </MudChip>
                }
                <MudButton Variant="Variant.Text" Color="Color.Secondary" Size="Size.Small" OnClick="@ClearFilters">
                    Clear All
                </MudButton>
            </MudStack>
        }
    </MudExpansionPanel>
</MudExpansionPanels>

@* Results Summary *@
@if (_hasSearched)
{
    <MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-3">
        <MudText Typo="Typo.body1">
            @if (_searching)
            {
                <span>Searching...</span>
            }
            else if (_results.Count == 0)
            {
                <span>No results found for "<strong>@_lastSearchQuery</strong>"</span>
            }
            else
            {
                <span>Found <strong>@_totalResults</strong> results for "<strong>@_lastSearchQuery</strong>" (@_searchTime.ToString("F2")s)</span>
            }
        </MudText>
        <MudToggleIconButton @bind-Toggled="_listView"
                             Icon="@Icons.Material.Filled.ViewList" ToggledIcon="@Icons.Material.Filled.GridView"
                             Color="Color.Default" ToggledColor="Color.Primary" />
    </MudStack>
}

@* Search Results *@
@if (_searching)
{
    <MudStack Spacing="2">
        @for (int i = 0; i < 5; i++)
        {
            <MudPaper Class="pa-4">
                <MudSkeleton SkeletonType="SkeletonType.Text" Width="60%" Height="24px" />
                <MudSkeleton SkeletonType="SkeletonType.Text" Width="100%" Class="mt-2" />
                <MudSkeleton SkeletonType="SkeletonType.Text" Width="80%" />
            </MudPaper>
        }
    </MudStack>
}
else if (_hasSearched && _results.Count == 0)
{
    <MudPaper Class="pa-8 text-center">
        <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Secondary" Class="mb-4" />
        <MudText Typo="Typo.h6" Color="Color.Secondary">No results found</MudText>
        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mt-2">
            Try adjusting your search terms or filters
        </MudText>
        <MudStack Row="true" Justify="Justify.Center" Class="mt-4" Spacing="2">
            <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="@ClearFilters">
                Clear Filters
            </MudButton>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/articles">
                Browse All Articles
            </MudButton>
        </MudStack>
    </MudPaper>
}
else if (_results.Count > 0)
{
    @if (_listView)
    {
        @* List View *@
        <MudStack Spacing="2">
            @foreach (var result in _results)
            {
                <MudPaper Class="pa-4 cursor-pointer" Elevation="1" @onclick="@(() => ViewArticle(result))">
                    <MudStack Spacing="2">
                        <MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Start">
                            <div>
                                <MudText Typo="Typo.h6" Color="Color.Primary">
                                    @((MarkupString)HighlightText(result.Title))
                                </MudText>
                                <MudStack Row="true" Spacing="1" Class="mt-1">
                                    <MudChip T="string" Size="Size.Small" Color="@GetArticleTypeColor(result.ArticleType)" Variant="Variant.Outlined">
                                        @result.ArticleType
                                    </MudChip>
                                    @if (!string.IsNullOrEmpty(result.ModuleName))
                                    {
                                        <MudChip T="string" Size="Size.Small" Variant="Variant.Text">@result.ModuleName</MudChip>
                                    }
                                    @if (!string.IsNullOrEmpty(result.Category))
                                    {
                                        <MudChip T="string" Size="Size.Small" Variant="Variant.Text">@result.Category</MudChip>
                                    }
                                </MudStack>
                            </div>
                            <MudStack AlignItems="AlignItems.End">
                                @if (result.Score > 0)
                                {
                                    <MudTooltip Text="Relevance Score">
                                        <MudChip T="string" Size="Size.Small" Color="Color.Info">@result.Score.ToString("P0")</MudChip>
                                    </MudTooltip>
                                }
                            </MudStack>
                        </MudStack>

                        <MudText Typo="Typo.body2">
                            @((MarkupString)HighlightText(result.Summary ?? ""))
                        </MudText>

                        <MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                            <MudStack Row="true" Spacing="3">
                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                    <MudIcon Icon="@Icons.Material.Filled.Visibility" Size="Size.Small" Class="mr-1" />
                                    @result.ViewCount views
                                </MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                    Updated @result.LastModifiedDate.ToLocalTime().ToString("MMM dd, yyyy")
                                </MudText>
                            </MudStack>
                            @if (result.Tags != null && result.Tags.Any())
                            {
                                <MudStack Row="true" Spacing="1">
                                    @foreach (var tag in result.Tags.Take(3))
                                    {
                                        <MudChip T="string" Size="Size.Small" Variant="Variant.Outlined" Color="Color.Default">@tag</MudChip>
                                    }
                                    @if (result.Tags.Count > 3)
                                    {
                                        <MudChip T="string" Size="Size.Small" Variant="Variant.Outlined" Color="Color.Default">+@(result.Tags.Count - 3)</MudChip>
                                    }
                                </MudStack>
                            }
                        </MudStack>
                    </MudStack>
                </MudPaper>
            }
        </MudStack>
    }
    else
    {
        @* Grid View *@
        <MudGrid>
            @foreach (var result in _results)
            {
                <MudItem xs="12" sm="6" md="4">
                    <MudCard Class="cursor-pointer" Style="height: 100%;" @onclick="@(() => ViewArticle(result))">
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.subtitle1" Class="text-truncate-2">
                                    @result.Title
                                </MudText>
                            </CardHeaderContent>
                            <CardHeaderActions>
                                <MudChip T="string" Size="Size.Small" Color="@GetArticleTypeColor(result.ArticleType)">
                                    @result.ArticleType
                                </MudChip>
                            </CardHeaderActions>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudText Typo="Typo.body2" Class="text-truncate-3" Color="Color.Secondary">
                                @result.Summary
                            </MudText>
                        </MudCardContent>
                        <MudCardActions>
                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                @result.ViewCount views | @result.LastModifiedDate.ToLocalTime().ToString("MMM dd")
                            </MudText>
                        </MudCardActions>
                    </MudCard>
                </MudItem>
            }
        </MudGrid>
    }

    @* Pagination *@
    @if (_totalPages > 1)
    {
        <MudStack Row="true" Justify="Justify.Center" Class="mt-4">
            <MudPagination Count="@_totalPages" Selected="@_currentPage" SelectedChanged="@OnPageChanged"
                           Color="Color.Primary" Variant="Variant.Filled" ShowFirstButton="true" ShowLastButton="true" />
        </MudStack>
    }
}
else if (!_hasSearched)
{
    @* Initial State *@
    <MudPaper Class="pa-8 text-center">
        <MudIcon Icon="@Icons.Material.Filled.Search" Size="Size.Large" Color="Color.Primary" Class="mb-4" />
        <MudText Typo="Typo.h6">Search the Knowledge Base</MudText>
        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mt-2">
            Enter keywords above to find articles, how-to guides, troubleshooting tips, and more
        </MudText>

        <MudDivider Class="my-4" />

        <MudText Typo="Typo.subtitle2" Class="mb-3">Popular Searches</MudText>
        <MudStack Row="true" Justify="Justify.Center" Spacing="2">
            @foreach (var term in _popularSearches)
            {
                <MudChip T="string" Color="Color.Primary" Variant="Variant.Outlined" OnClick="@(() => QuickSearch(term))">
                    @term
                </MudChip>
            }
        </MudStack>
    </MudPaper>
}

<style>
    .text-truncate-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    .text-truncate-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    .cursor-pointer {
        cursor: pointer;
    }
    .cursor-pointer:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
</style>

@code {
    private string _searchQuery = "";
    private string _lastSearchQuery = "";
    private bool _searching = false;
    private bool _hasSearched = false;
    private bool _listView = true;
    private double _searchTime = 0;

    // Filters
    private string? _filterType;
    private Guid? _filterModuleId;
    private string? _filterCategory;
    private string _sortBy = "relevance";

    // Pagination
    private int _currentPage = 1;
    private int _pageSize = 10;
    private int _totalResults = 0;
    private int _totalPages => (int)Math.Ceiling((double)_totalResults / _pageSize);

    // Data
    private List<SearchResultDto> _results = new();
    private List<ModuleDto> _modules = new();
    private List<string> _categories = new() { "configuration", "troubleshooting", "setup", "api", "security" };
    private List<string> _popularSearches = new() { "dashboard", "login", "API", "configuration", "export" };
    private static readonly string[] ArticleTypes = { "HowTo", "Troubleshooting", "ReleaseNote", "Faq", "ProductOverview", "BestPractice" };

    private bool _hasActiveFilters => !string.IsNullOrEmpty(_filterType) || _filterModuleId.HasValue || !string.IsNullOrEmpty(_filterCategory);

    protected override async Task OnInitializedAsync()
    {
        // Load modules for filter dropdown
        try
        {
            _modules = await ApiService.GetModulesAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading modules: {ex.Message}", Severity.Error);
        }

        // Check for query parameter
        var uri = new Uri(Navigation.Uri);
        var query = System.Web.HttpUtility.ParseQueryString(uri.Query);
        var q = query["q"];

        if (!string.IsNullOrEmpty(q))
        {
            _searchQuery = q;
            await PerformSearch();
        }
    }

    private async Task OnSearchKeyDown(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await PerformSearch();
        }
    }

    private async Task PerformSearch()
    {
        if (string.IsNullOrWhiteSpace(_searchQuery)) return;

        _searching = true;
        _hasSearched = true;
        _lastSearchQuery = _searchQuery;
        _currentPage = 1;
        StateHasChanged();

        var startTime = DateTime.Now;

        try
        {
            var response = await ApiService.SearchAsync(
                query: _searchQuery,
                page: _currentPage,
                pageSize: _pageSize,
                articleType: _filterType,
                moduleId: _filterModuleId,
                category: _filterCategory,
                sortBy: _sortBy);

            _results = response.Items;
            _totalResults = response.TotalCount;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Search error: {ex.Message}", Severity.Error);
            _results = new();
        }

        _searchTime = (DateTime.Now - startTime).TotalSeconds;
        _searching = false;
        StateHasChanged();
    }

    private async Task OnTypeFilterChanged(string? value)
    {
        _filterType = value;
        _currentPage = 1;
        await PerformSearch();
    }

    private async Task OnModuleFilterChanged(Guid? value)
    {
        _filterModuleId = value;
        _currentPage = 1;
        await PerformSearch();
    }

    private async Task OnCategoryFilterChanged(string? value)
    {
        _filterCategory = value;
        _currentPage = 1;
        await PerformSearch();
    }

    private async Task OnSortChanged(string value)
    {
        _sortBy = value;
        await PerformSearch();
    }

    private async Task ClearFilters()
    {
        _filterType = null;
        _filterModuleId = null;
        _filterCategory = null;
        _sortBy = "relevance";
        if (_hasSearched)
        {
            await PerformSearch();
        }
    }

    private async Task OnPageChanged(int page)
    {
        _currentPage = page;
        await PerformSearch();
    }

    private void ViewArticle(SearchResultDto result)
    {
        Navigation.NavigateTo($"/articles/{result.ArticleId}");
    }

    private async Task QuickSearch(string term)
    {
        _searchQuery = term;
        await PerformSearch();
    }

    private string HighlightText(string text)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(_lastSearchQuery))
            return text;

        var escapedText = System.Web.HttpUtility.HtmlEncode(text);
        var pattern = System.Text.RegularExpressions.Regex.Escape(_lastSearchQuery);
        var regex = new System.Text.RegularExpressions.Regex(pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);

        return regex.Replace(escapedText, match => $"<mark>{match.Value}</mark>");
    }

    private Color GetArticleTypeColor(string type) => type switch
    {
        "HowTo" => Color.Primary,
        "Troubleshooting" => Color.Error,
        "ReleaseNote" => Color.Info,
        "Faq" or "FAQ" => Color.Secondary,
        "ProductOverview" => Color.Tertiary,
        "BestPractice" => Color.Warning,
        _ => Color.Default
    };
}

using System.Text.Json;
using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Enums;
using KnowledgeBase.Core.Interfaces;
using KnowledgeBase.Infrastructure.Services.Agents;
using Microsoft.Extensions.Logging;

namespace KnowledgeBase.Infrastructure.Services;

/// <summary>
/// Orchestrates the AI-powered article creation workflow.
/// </summary>
public class ArticleOrchestrator
{
    private readonly IRouterAgentService _routerAgent;
    private readonly AgentFactory _agentFactory;
    private readonly IEvaluationAgentService _evaluationAgent;
    private readonly IArticleService _articleService;
    private readonly ILogger<ArticleOrchestrator> _logger;

    public ArticleOrchestrator(
        IRouterAgentService routerAgent,
        AgentFactory agentFactory,
        IEvaluationAgentService evaluationAgent,
        IArticleService articleService,
        ILogger<ArticleOrchestrator> logger)
    {
        _routerAgent = routerAgent;
        _agentFactory = agentFactory;
        _evaluationAgent = evaluationAgent;
        _articleService = articleService;
        _logger = logger;
    }

    /// <summary>
    /// Create an article from user input using the AI pipeline.
    /// </summary>
    public async Task<ArticleCreationResult> CreateArticleAsync(
        string userInput,
        ArticleType? overrideType = null,
        Guid? moduleId = null,
        Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting article creation orchestration");

        var result = new ArticleCreationResult();

        // Step 1: Classify content (unless type is overridden)
        ClassificationResult classification;
        if (overrideType.HasValue)
        {
            classification = new ClassificationResult
            {
                ArticleType = overrideType.Value,
                Confidence = 1.0m,
                Reasoning = "User specified article type"
            };
        }
        else
        {
            classification = await _routerAgent.ClassifyAsync(userInput, cancellationToken);
        }

        result.Classification = classification;

        // Check if we need clarification
        if (classification.NeedsClarification)
        {
            result.Success = false;
            result.ClarificationNeeded = $"I'm not sure what type of article you want to create. " +
                $"The content looks like it could be a {classification.ArticleType} article " +
                $"(confidence: {classification.Confidence:P0}). {classification.Reasoning}. " +
                $"Please confirm the article type or provide more context.";
            return result;
        }

        // Step 2: Generate article content
        var agent = _agentFactory.GetAgent(classification.ArticleType);
        var generationResult = await agent.GenerateAsync(userInput, cancellationToken);

        if (!generationResult.Success)
        {
            result.Success = false;
            result.ErrorMessage = generationResult.ErrorMessage;
            return result;
        }

        // Step 3: Evaluate the generated content
        var evaluation = await _evaluationAgent.EvaluateAsync(
            generationResult.Title,
            generationResult.Summary,
            generationResult.ContentJson,
            classification.ArticleType,
            cancellationToken);

        result.Evaluation = evaluation;

        // Step 4: Create the article entity
        var article = new Article
        {
            ArticleType = classification.ArticleType,
            Title = generationResult.Title,
            Summary = generationResult.Summary,
            Content = generationResult.ContentJson,
            ModuleId = moduleId ?? Guid.Empty, // Will need to be set by user if not provided
            Category = generationResult.SuggestedCategory ?? GetDefaultCategory(classification.ArticleType),
            Tags = generationResult.Tags.Count > 0 ? JsonSerializer.Serialize(generationResult.Tags) : null,
            EstimatedTimeMinutes = generationResult.EstimatedTimeMinutes,
            Difficulty = generationResult.Difficulty,
            Status = ArticleStatus.Draft
        };

        // Save if we have a user ID
        if (userId.HasValue && moduleId.HasValue)
        {
            article = await _articleService.CreateAsync(article, userId.Value, cancellationToken);
            result.ArticleId = article.ArticleId;
        }

        result.Success = true;
        result.Article = article;

        _logger.LogInformation(
            "Article creation complete. Type: {Type}, Title: {Title}, Score: {Score}/10",
            classification.ArticleType, article.Title, evaluation.OverallScore);

        return result;
    }

    /// <summary>
    /// Regenerate article content with a different type.
    /// </summary>
    public async Task<ArticleCreationResult> RegenerateAsTypeAsync(
        string userInput,
        ArticleType articleType,
        CancellationToken cancellationToken = default)
    {
        return await CreateArticleAsync(userInput, articleType, cancellationToken: cancellationToken);
    }

    /// <summary>
    /// Evaluate an existing article.
    /// </summary>
    public async Task<EvaluationResult> EvaluateArticleAsync(
        Guid articleId,
        CancellationToken cancellationToken = default)
    {
        var article = await _articleService.GetByIdAsync(articleId, cancellationToken);
        if (article == null)
        {
            throw new InvalidOperationException($"Article {articleId} not found.");
        }

        return await _evaluationAgent.EvaluateAsync(
            article.Title,
            article.Summary,
            article.Content,
            article.ArticleType,
            cancellationToken);
    }

    private static string GetDefaultCategory(ArticleType type)
    {
        return type switch
        {
            ArticleType.HowTo => "configuration",
            ArticleType.Troubleshooting => "error_resolution",
            ArticleType.ReleaseNote => "release_notes",
            ArticleType.Faq => "faq",
            ArticleType.ProductOverview => "product",
            ArticleType.BestPractice => "guidance",
            _ => "general"
        };
    }
}

/// <summary>
/// Result from the article creation orchestration.
/// </summary>
public class ArticleCreationResult
{
    public bool Success { get; set; }
    public Guid? ArticleId { get; set; }
    public Article? Article { get; set; }
    public ClassificationResult? Classification { get; set; }
    public EvaluationResult? Evaluation { get; set; }
    public string? ClarificationNeeded { get; set; }
    public string? ErrorMessage { get; set; }
}

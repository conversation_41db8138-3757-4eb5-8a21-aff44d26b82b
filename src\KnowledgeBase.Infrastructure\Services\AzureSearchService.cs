using System.Text.Json;
using Azure;
using Azure.Search.Documents;
using Azure.Search.Documents.Indexes;
using Azure.Search.Documents.Indexes.Models;
using Azure.Search.Documents.Models;
using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace KnowledgeBase.Infrastructure.Services;

/// <summary>
/// Azure AI Search implementation of the search service.
/// </summary>
public class AzureSearchService : ISearchService
{
    private readonly SearchClient _searchClient;
    private readonly SearchIndexClient _indexClient;
    private readonly IAzureOpenAIClient _openAIClient;
    private readonly ILogger<AzureSearchService> _logger;
    private readonly string _indexName;
    private const int EmbeddingDimensions = 1536; // text-embedding-3-small
    private const string VectorSearchProfileName = "vector-profile";
    private const string VectorAlgorithmName = "hnsw-algorithm";

    public AzureSearchService(
        IConfiguration configuration,
        IAzureOpenAIClient openAIClient,
        ILogger<AzureSearchService> logger)
    {
        _logger = logger;
        _openAIClient = openAIClient;

        var endpoint = configuration["AzureSearch:Endpoint"]
            ?? throw new InvalidOperationException("AzureSearch:Endpoint is not configured");
        var apiKey = configuration["AzureSearch:ApiKey"]
            ?? throw new InvalidOperationException("AzureSearch:ApiKey is not configured");
        _indexName = configuration["AzureSearch:IndexName"] ?? "knowledgebase-articles";

        var credential = new AzureKeyCredential(apiKey);
        _indexClient = new SearchIndexClient(new Uri(endpoint), credential);
        _searchClient = new SearchClient(new Uri(endpoint), _indexName, credential);
    }

    public async Task<(List<ArticleSearchResult> Items, int TotalCount)> SearchAsync(
        SearchQuery query,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Searching articles: {Query}", query.Query);

        var options = new SearchOptions
        {
            Size = query.PageSize,
            Skip = (query.Page - 1) * query.PageSize,
            IncludeTotalCount = true,
            QueryType = SearchQueryType.Full,
            SearchMode = SearchMode.Any,
            HighlightPreTag = "<mark>",
            HighlightPostTag = "</mark>"
        };

        // Add highlighting
        options.HighlightFields.Add("title");
        options.HighlightFields.Add("summary");
        options.HighlightFields.Add("content");

        // Add select fields
        options.Select.Add("articleId");
        options.Select.Add("articleType");
        options.Select.Add("title");
        options.Select.Add("summary");
        options.Select.Add("moduleName");
        options.Select.Add("category");
        options.Select.Add("tags");
        options.Select.Add("lastModifiedDate");
        options.Select.Add("viewCount");
        options.Select.Add("rating");

        // Build filter
        var filters = new List<string>();

        if (!string.IsNullOrEmpty(query.ArticleType))
        {
            filters.Add($"articleType eq '{query.ArticleType}'");
        }

        if (query.ModuleId.HasValue)
        {
            filters.Add($"moduleId eq '{query.ModuleId}'");
        }

        if (!string.IsNullOrEmpty(query.Category))
        {
            filters.Add($"category eq '{query.Category}'");
        }

        if (!string.IsNullOrEmpty(query.Status))
        {
            filters.Add($"status eq '{query.Status}'");
        }

        if (query.Tags?.Any() == true)
        {
            var tagFilters = query.Tags.Select(t => $"tags/any(tag: tag eq '{t}')");
            filters.Add($"({string.Join(" or ", tagFilters)})");
        }

        if (filters.Any())
        {
            options.Filter = string.Join(" and ", filters);
        }

        // Add sorting
        var orderBy = query.SortBy?.ToLower() switch
        {
            "date" or "lastmodifieddate" => query.SortDescending ? "lastModifiedDate desc" : "lastModifiedDate asc",
            "title" => query.SortDescending ? "title desc" : "title asc",
            "views" or "viewcount" => query.SortDescending ? "viewCount desc" : "viewCount asc",
            "rating" => query.SortDescending ? "rating desc" : "rating asc",
            _ => null // Default to relevance
        };

        if (!string.IsNullOrEmpty(orderBy))
        {
            options.OrderBy.Add(orderBy);
        }

        // Add facets
        options.Facets.Add("articleType,count:10");
        options.Facets.Add("category,count:10");
        options.Facets.Add("tags,count:20");

        // Add vector search for hybrid results when there's a real query
        if (!string.IsNullOrWhiteSpace(query.Query))
        {
            try
            {
                var queryEmbedding = await _openAIClient.GetEmbeddingsAsync(query.Query, cancellationToken);
                options.VectorSearch = new VectorSearchOptions
                {
                    Queries =
                    {
                        new VectorizedQuery(queryEmbedding)
                        {
                            KNearestNeighborsCount = query.PageSize * 2,
                            Fields = { "contentVector" }
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to generate query embedding, falling back to text-only search");
            }
        }

        try
        {
            var searchText = string.IsNullOrWhiteSpace(query.Query) ? "*" : query.Query;
            var response = await _searchClient.SearchAsync<SearchDocument>(searchText, options, cancellationToken);

            var results = new List<ArticleSearchResult>();
            await foreach (var result in response.Value.GetResultsAsync())
            {
                var searchResult = new ArticleSearchResult
                {
                    ArticleId = Guid.Parse(result.Document["articleId"].ToString()!),
                    ArticleType = result.Document["articleType"]?.ToString() ?? "",
                    Title = result.Document["title"]?.ToString() ?? "",
                    Summary = result.Document["summary"]?.ToString() ?? "",
                    ModuleName = result.Document["moduleName"]?.ToString() ?? "",
                    Category = result.Document["category"]?.ToString() ?? "",
                    Tags = result.Document["tags"] is IEnumerable<object> tags
                        ? tags.Select(t => t.ToString()!).ToList()
                        : new List<string>(),
                    LastModifiedDate = result.Document.TryGetValue("lastModifiedDate", out var date)
                        ? DateTimeOffset.Parse(date.ToString()!).DateTime
                        : DateTime.MinValue,
                    ViewCount = result.Document.TryGetValue("viewCount", out var views)
                        ? Convert.ToInt32(views)
                        : 0,
                    Rating = result.Document.TryGetValue("rating", out var rating)
                        ? Convert.ToDecimal(rating)
                        : null,
                    SearchRank = results.Count + 1
                };

                results.Add(searchResult);
            }

            var totalCount = (int)(response.Value.TotalCount ?? results.Count);

            _logger.LogInformation("Search returned {Count} results out of {Total}", results.Count, totalCount);

            return (results, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching articles");
            throw;
        }
    }

    public async Task<List<string>> GetSuggestionsAsync(
        string query,
        int maxResults = 10,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(query))
            return new List<string>();

        try
        {
            var options = new AutocompleteOptions
            {
                Mode = AutocompleteMode.OneTermWithContext,
                Size = maxResults
            };

            var response = await _searchClient.AutocompleteAsync(
                query,
                "sg", // Suggester name defined in index
                options,
                cancellationToken);

            return response.Value.Results
                .Select(r => r.Text)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting suggestions for: {Query}", query);
            return new List<string>();
        }
    }

    public async Task IndexArticleAsync(Article article, CancellationToken cancellationToken = default)
    {
        await IndexArticlesAsync(new[] { article }, cancellationToken);
    }

    public async Task IndexArticlesAsync(IEnumerable<Article> articles, CancellationToken cancellationToken = default)
    {
        var articleList = articles.ToList();
        var documents = new List<SearchDocument>();

        foreach (var article in articleList)
        {
            var searchableContent = ExtractSearchableContent(article.Content);
            var embeddingText = $"{article.Title} {article.Summary} {searchableContent}";

            float[]? embedding = null;
            try
            {
                embedding = await _openAIClient.GetEmbeddingsAsync(embeddingText, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to generate embedding for article {ArticleId}, indexing without vector", article.ArticleId);
            }

            var doc = new SearchDocument
            {
                ["articleId"] = article.ArticleId.ToString(),
                ["articleType"] = article.ArticleType.ToString(),
                ["title"] = article.Title,
                ["summary"] = article.Summary,
                ["content"] = searchableContent,
                ["moduleId"] = article.ModuleId.ToString(),
                ["moduleName"] = article.Module?.Name ?? "",
                ["category"] = article.Category,
                ["tags"] = ParseTags(article.Tags),
                ["status"] = article.Status.ToString().ToLower(),
                ["difficulty"] = article.Difficulty?.ToString(),
                ["estimatedTimeMinutes"] = article.EstimatedTimeMinutes,
                ["createdDate"] = article.CreatedDate,
                ["lastModifiedDate"] = article.LastModifiedDate,
                ["viewCount"] = article.ViewCount,
                ["helpfulCount"] = article.HelpfulCount,
                ["notHelpfulCount"] = article.NotHelpfulCount,
                ["rating"] = article.Rating
            };

            if (embedding != null)
            {
                doc["contentVector"] = embedding;
            }

            documents.Add(doc);
        }

        try
        {
            var batch = IndexDocumentsBatch.Upload(documents);
            var response = await _searchClient.IndexDocumentsAsync(batch, cancellationToken: cancellationToken);

            var succeeded = response.Value.Results.Count(r => r.Succeeded);
            var failed = response.Value.Results.Count(r => !r.Succeeded);

            _logger.LogInformation("Indexed {Succeeded} articles, {Failed} failed", succeeded, failed);

            if (failed > 0)
            {
                foreach (var result in response.Value.Results.Where(r => !r.Succeeded))
                {
                    _logger.LogWarning("Failed to index article {Key}: {Message}", result.Key, result.ErrorMessage);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error indexing articles");
            throw;
        }
    }

    public async Task RemoveFromIndexAsync(Guid articleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var document = new SearchDocument { ["articleId"] = articleId.ToString() };
            var batch = IndexDocumentsBatch.Delete(new[] { document });
            await _searchClient.IndexDocumentsAsync(batch, cancellationToken: cancellationToken);

            _logger.LogInformation("Removed article {ArticleId} from search index", articleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing article {ArticleId} from search index", articleId);
            throw;
        }
    }

    public async Task EnsureIndexExistsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await _indexClient.GetIndexAsync(_indexName, cancellationToken);
            _logger.LogInformation("Search index {IndexName} already exists", _indexName);
        }
        catch (RequestFailedException ex) when (ex.Status == 404)
        {
            _logger.LogInformation("Creating search index {IndexName}", _indexName);
            await CreateIndexAsync(cancellationToken);
        }
    }

    private async Task CreateIndexAsync(CancellationToken cancellationToken)
    {
        var index = new SearchIndex(_indexName)
        {
            Fields = new List<SearchField>
            {
                new SearchField("articleId", SearchFieldDataType.String) { IsKey = true, IsFilterable = true },
                new SearchField("articleType", SearchFieldDataType.String) { IsFilterable = true, IsFacetable = true },
                new SearchField("title", SearchFieldDataType.String) { IsSearchable = true, IsSortable = true },
                new SearchField("summary", SearchFieldDataType.String) { IsSearchable = true },
                new SearchField("content", SearchFieldDataType.String) { IsSearchable = true },
                new SearchField("contentVector", SearchFieldDataType.Collection(SearchFieldDataType.Single))
                {
                    IsSearchable = true,
                    VectorSearchDimensions = EmbeddingDimensions,
                    VectorSearchProfileName = VectorSearchProfileName
                },
                new SearchField("moduleId", SearchFieldDataType.String) { IsFilterable = true },
                new SearchField("moduleName", SearchFieldDataType.String) { IsSearchable = true, IsFacetable = true },
                new SearchField("category", SearchFieldDataType.String) { IsFilterable = true, IsFacetable = true, IsSortable = true },
                new SearchField("tags", SearchFieldDataType.Collection(SearchFieldDataType.String)) { IsFilterable = true, IsFacetable = true },
                new SearchField("status", SearchFieldDataType.String) { IsFilterable = true },
                new SearchField("difficulty", SearchFieldDataType.String) { IsFilterable = true, IsFacetable = true },
                new SearchField("estimatedTimeMinutes", SearchFieldDataType.Int32) { IsFilterable = true, IsSortable = true },
                new SearchField("createdDate", SearchFieldDataType.DateTimeOffset) { IsFilterable = true, IsSortable = true },
                new SearchField("lastModifiedDate", SearchFieldDataType.DateTimeOffset) { IsFilterable = true, IsSortable = true },
                new SearchField("viewCount", SearchFieldDataType.Int32) { IsFilterable = true, IsSortable = true },
                new SearchField("helpfulCount", SearchFieldDataType.Int32) { IsFilterable = true, IsSortable = true },
                new SearchField("notHelpfulCount", SearchFieldDataType.Int32) { IsFilterable = true },
                new SearchField("rating", SearchFieldDataType.Double) { IsFilterable = true, IsSortable = true }
            },
            VectorSearch = new VectorSearch
            {
                Algorithms =
                {
                    new HnswAlgorithmConfiguration(VectorAlgorithmName)
                    {
                        Parameters = new HnswParameters
                        {
                            Metric = VectorSearchAlgorithmMetric.Cosine,
                            M = 4,
                            EfConstruction = 400,
                            EfSearch = 500
                        }
                    }
                },
                Profiles =
                {
                    new VectorSearchProfile(VectorSearchProfileName, VectorAlgorithmName)
                }
            },
            Suggesters =
            {
                new SearchSuggester("sg", "title", "summary", "tags")
            },
            ScoringProfiles =
            {
                new ScoringProfile("boost-recent")
                {
                    FunctionAggregation = ScoringFunctionAggregation.Sum,
                    Functions =
                    {
                        new FreshnessScoringFunction(
                            "lastModifiedDate",
                            2.0,
                            new FreshnessScoringParameters(TimeSpan.FromDays(30)))
                        {
                            Interpolation = ScoringFunctionInterpolation.Linear
                        },
                        new MagnitudeScoringFunction(
                            "viewCount",
                            1.5,
                            new MagnitudeScoringParameters(0, 1000))
                        {
                            Interpolation = ScoringFunctionInterpolation.Logarithmic
                        },
                        new MagnitudeScoringFunction(
                            "helpfulCount",
                            1.2,
                            new MagnitudeScoringParameters(0, 100))
                        {
                            Interpolation = ScoringFunctionInterpolation.Linear
                        }
                    },
                    TextWeights = new TextWeights(new Dictionary<string, double>
                    {
                        ["title"] = 3.0,
                        ["summary"] = 2.0,
                        ["content"] = 1.0,
                        ["tags"] = 2.5
                    })
                }
            },
            DefaultScoringProfile = "boost-recent"
        };

        await _indexClient.CreateIndexAsync(index, cancellationToken);
        _logger.LogInformation("Created search index {IndexName}", _indexName);
    }

    private static string ExtractSearchableContent(string jsonContent)
    {
        if (string.IsNullOrEmpty(jsonContent))
            return string.Empty;

        try
        {
            using var doc = JsonDocument.Parse(jsonContent);
            var texts = new List<string>();
            ExtractText(doc.RootElement, texts);
            return string.Join(" ", texts);
        }
        catch
        {
            return jsonContent;
        }
    }

    private static void ExtractText(JsonElement element, List<string> texts)
    {
        switch (element.ValueKind)
        {
            case JsonValueKind.String:
                var text = element.GetString();
                if (!string.IsNullOrWhiteSpace(text))
                    texts.Add(text);
                break;

            case JsonValueKind.Object:
                foreach (var property in element.EnumerateObject())
                {
                    ExtractText(property.Value, texts);
                }
                break;

            case JsonValueKind.Array:
                foreach (var item in element.EnumerateArray())
                {
                    ExtractText(item, texts);
                }
                break;
        }
    }

    private static string[] ParseTags(string? tagsJson)
    {
        if (string.IsNullOrEmpty(tagsJson))
            return Array.Empty<string>();

        try
        {
            return JsonSerializer.Deserialize<string[]>(tagsJson) ?? Array.Empty<string>();
        }
        catch
        {
            return Array.Empty<string>();
        }
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Base - Create Article</title>
    <link href="https://fonts.googleapis.com/css2?family=Instrument+Sans:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Professional, trustworthy color palette with depth */
            --color-primary: #2563eb;
            --color-primary-dark: #1e40af;
            --color-primary-light: #3b82f6;
            --color-secondary: #0891b2;
            --color-accent: #f59e0b;
            --color-success: #10b981;
            --color-warning: #f59e0b;
            --color-error: #ef4444;
            
            --color-bg-base: #fafbfc;
            --color-bg-elevated: #ffffff;
            --color-bg-subtle: #f1f5f9;
            --color-bg-hover: #e2e8f0;
            
            --color-text-primary: #0f172a;
            --color-text-secondary: #475569;
            --color-text-muted: #94a3b8;
            
            --color-border: #e2e8f0;
            --color-border-strong: #cbd5e1;
            
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            
            --font-sans: 'Instrument Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-mono: 'JetBrains Mono', 'Courier New', monospace;
            
            --spacing-unit: 8px;
            --radius-sm: 6px;
            --radius-md: 10px;
            --radius-lg: 14px;
            --radius-xl: 18px;
            
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: var(--font-sans);
            background: var(--color-bg-base);
            color: var(--color-text-primary);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Layout */
        .app-container {
            display: grid;
            grid-template-columns: 260px 1fr;
            grid-template-rows: 64px 1fr;
            height: 100vh;
            overflow: hidden;
        }

        /* Header */
        .app-header {
            grid-column: 1 / -1;
            background: var(--color-bg-elevated);
            border-bottom: 1px solid var(--color-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 calc(var(--spacing-unit) * 3);
            box-shadow: var(--shadow-sm);
            z-index: 100;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: calc(var(--spacing-unit) * 1.5);
            font-weight: 700;
            font-size: 18px;
            color: var(--color-text-primary);
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 16px;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: calc(var(--spacing-unit) * 2);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f59e0b, #ef4444);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: transform var(--transition-fast);
        }

        .user-avatar:hover {
            transform: scale(1.05);
        }

        /* Sidebar */
        .sidebar {
            background: var(--color-bg-elevated);
            border-right: 1px solid var(--color-border);
            padding: calc(var(--spacing-unit) * 3);
            overflow-y: auto;
        }

        .sidebar-section {
            margin-bottom: calc(var(--spacing-unit) * 4);
        }

        .sidebar-section-title {
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--color-text-muted);
            margin-bottom: calc(var(--spacing-unit) * 2);
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: calc(var(--spacing-unit) * 1.5);
            padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 2);
            border-radius: var(--radius-md);
            color: var(--color-text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            transition: all var(--transition-fast);
            margin-bottom: calc(var(--spacing-unit) * 0.5);
        }

        .nav-item:hover {
            background: var(--color-bg-hover);
            color: var(--color-text-primary);
        }

        .nav-item.active {
            background: var(--color-primary);
            color: white;
        }

        .nav-item-icon {
            font-size: 18px;
        }

        /* Main Content */
        .main-content {
            overflow-y: auto;
            padding: calc(var(--spacing-unit) * 4);
            background: var(--color-bg-base);
        }

        .content-wrapper {
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Page Header */
        .page-header {
            margin-bottom: calc(var(--spacing-unit) * 4);
            animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--color-text-primary);
            margin-bottom: calc(var(--spacing-unit) * 1);
        }

        .page-subtitle {
            font-size: 16px;
            color: var(--color-text-secondary);
        }

        /* Workflow Steps */
        .workflow-steps {
            display: flex;
            gap: calc(var(--spacing-unit) * 2);
            margin-bottom: calc(var(--spacing-unit) * 5);
            padding-bottom: calc(var(--spacing-unit) * 4);
            border-bottom: 1px solid var(--color-border);
            animation: slideDown 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both;
        }

        .workflow-step {
            flex: 1;
            display: flex;
            align-items: center;
            gap: calc(var(--spacing-unit) * 2);
            padding: calc(var(--spacing-unit) * 2);
            background: var(--color-bg-elevated);
            border: 2px solid var(--color-border);
            border-radius: var(--radius-lg);
            transition: all var(--transition-normal);
        }

        .workflow-step.active {
            border-color: var(--color-primary);
            background: var(--color-bg-elevated);
            box-shadow: var(--shadow-md);
        }

        .workflow-step.completed {
            border-color: var(--color-success);
            background: rgba(16, 185, 129, 0.05);
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 16px;
            background: var(--color-bg-subtle);
            color: var(--color-text-muted);
            flex-shrink: 0;
            transition: all var(--transition-normal);
        }

        .workflow-step.active .step-number {
            background: var(--color-primary);
            color: white;
        }

        .workflow-step.completed .step-number {
            background: var(--color-success);
            color: white;
        }

        .step-content {
            flex: 1;
            min-width: 0;
        }

        .step-title {
            font-weight: 600;
            font-size: 14px;
            color: var(--color-text-primary);
            margin-bottom: 2px;
        }

        .step-description {
            font-size: 12px;
            color: var(--color-text-muted);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Content Input Section */
        .content-section {
            background: var(--color-bg-elevated);
            border-radius: var(--radius-xl);
            padding: calc(var(--spacing-unit) * 4);
            margin-bottom: calc(var(--spacing-unit) * 3);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--color-border);
            animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: calc(var(--spacing-unit) * 3);
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--color-text-primary);
            display: flex;
            align-items: center;
            gap: calc(var(--spacing-unit) * 1.5);
        }

        .section-icon {
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        /* Input Area */
        .input-area {
            position: relative;
        }

        .input-tabs {
            display: flex;
            gap: calc(var(--spacing-unit) * 1);
            margin-bottom: calc(var(--spacing-unit) * 2);
            border-bottom: 1px solid var(--color-border);
        }

        .input-tab {
            padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 3);
            border: none;
            background: none;
            color: var(--color-text-secondary);
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            position: relative;
            transition: all var(--transition-fast);
            border-bottom: 2px solid transparent;
        }

        .input-tab:hover {
            color: var(--color-text-primary);
        }

        .input-tab.active {
            color: var(--color-primary);
            border-bottom-color: var(--color-primary);
        }

        .textarea-wrapper {
            position: relative;
        }

        .content-textarea {
            width: 100%;
            min-height: 300px;
            padding: calc(var(--spacing-unit) * 3);
            border: 2px solid var(--color-border);
            border-radius: var(--radius-lg);
            font-family: var(--font-sans);
            font-size: 15px;
            line-height: 1.6;
            color: var(--color-text-primary);
            background: var(--color-bg-base);
            resize: vertical;
            transition: all var(--transition-normal);
        }

        .content-textarea:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .content-textarea::placeholder {
            color: var(--color-text-muted);
        }

        .textarea-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: calc(var(--spacing-unit) * 2);
            padding: 0 calc(var(--spacing-unit) * 1);
        }

        .char-count {
            font-size: 13px;
            color: var(--color-text-muted);
            font-family: var(--font-mono);
        }

        .quick-actions {
            display: flex;
            gap: calc(var(--spacing-unit) * 1);
        }

        .quick-action-btn {
            padding: calc(var(--spacing-unit) * 1) calc(var(--spacing-unit) * 2);
            border: 1px solid var(--color-border);
            background: var(--color-bg-elevated);
            border-radius: var(--radius-sm);
            font-size: 13px;
            color: var(--color-text-secondary);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .quick-action-btn:hover {
            background: var(--color-bg-hover);
            color: var(--color-text-primary);
            border-color: var(--color-border-strong);
        }

        /* File Upload */
        .file-upload-area {
            display: none;
            border: 2px dashed var(--color-border);
            border-radius: var(--radius-lg);
            padding: calc(var(--spacing-unit) * 6);
            text-align: center;
            background: var(--color-bg-base);
            cursor: pointer;
            transition: all var(--transition-normal);
        }

        .file-upload-area.active {
            display: block;
        }

        .file-upload-area:hover {
            border-color: var(--color-primary);
            background: rgba(37, 99, 235, 0.03);
        }

        .file-upload-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto calc(var(--spacing-unit) * 2);
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
        }

        .file-upload-text {
            font-size: 16px;
            font-weight: 600;
            color: var(--color-text-primary);
            margin-bottom: calc(var(--spacing-unit) * 1);
        }

        .file-upload-subtext {
            font-size: 14px;
            color: var(--color-text-secondary);
        }

        /* AI Processing Section */
        .ai-section {
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.05), rgba(8, 145, 178, 0.05));
            border: 2px solid rgba(37, 99, 235, 0.2);
            border-radius: var(--radius-xl);
            padding: calc(var(--spacing-unit) * 4);
            margin-bottom: calc(var(--spacing-unit) * 3);
            display: none;
            animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .ai-section.active {
            display: block;
        }

        .ai-header {
            display: flex;
            align-items: center;
            gap: calc(var(--spacing-unit) * 2);
            margin-bottom: calc(var(--spacing-unit) * 3);
        }

        .ai-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.9;
            }
        }

        .ai-status {
            flex: 1;
        }

        .ai-status-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--color-text-primary);
            margin-bottom: 4px;
        }

        .ai-status-text {
            font-size: 14px;
            color: var(--color-text-secondary);
        }

        .ai-progress {
            height: 6px;
            background: rgba(37, 99, 235, 0.1);
            border-radius: 999px;
            overflow: hidden;
            margin-top: calc(var(--spacing-unit) * 2);
        }

        .ai-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
            border-radius: 999px;
            transition: width var(--transition-slow);
            animation: shimmer 1.5s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% {
                background-position: -200% center;
            }
            100% {
                background-position: 200% center;
            }
        }

        /* Article Type Selection */
        .type-selection {
            display: none;
            animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .type-selection.active {
            display: block;
        }

        .type-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: calc(var(--spacing-unit) * 3);
            margin-top: calc(var(--spacing-unit) * 3);
        }

        .type-card {
            background: var(--color-bg-elevated);
            border: 2px solid var(--color-border);
            border-radius: var(--radius-lg);
            padding: calc(var(--spacing-unit) * 3);
            cursor: pointer;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .type-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
            transform: scaleX(0);
            transition: transform var(--transition-normal);
        }

        .type-card:hover {
            border-color: var(--color-primary);
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .type-card:hover::before {
            transform: scaleX(1);
        }

        .type-card.selected {
            border-color: var(--color-primary);
            background: rgba(37, 99, 235, 0.05);
            box-shadow: var(--shadow-md);
        }

        .type-card.selected::before {
            transform: scaleX(1);
        }

        .type-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: calc(var(--spacing-unit) * 2);
            background: var(--color-bg-subtle);
            transition: all var(--transition-normal);
        }

        .type-card:hover .type-icon,
        .type-card.selected .type-icon {
            background: var(--color-primary);
            color: white;
            transform: scale(1.1);
        }

        .type-title {
            font-size: 16px;
            font-weight: 700;
            color: var(--color-text-primary);
            margin-bottom: calc(var(--spacing-unit) * 1);
        }

        .type-description {
            font-size: 14px;
            color: var(--color-text-secondary);
            line-height: 1.5;
        }

        .type-confidence {
            display: inline-block;
            margin-top: calc(var(--spacing-unit) * 2);
            padding: calc(var(--spacing-unit) * 0.5) calc(var(--spacing-unit) * 1.5);
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-success);
            border-radius: 999px;
            font-size: 12px;
            font-weight: 600;
        }

        /* Action Buttons */
        .action-bar {
            display: flex;
            gap: calc(var(--spacing-unit) * 2);
            justify-content: flex-end;
            padding-top: calc(var(--spacing-unit) * 3);
            border-top: 1px solid var(--color-border);
            animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
        }

        .btn {
            padding: calc(var(--spacing-unit) * 2) calc(var(--spacing-unit) * 4);
            border: none;
            border-radius: var(--radius-md);
            font-family: var(--font-sans);
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-normal);
            display: inline-flex;
            align-items: center;
            gap: calc(var(--spacing-unit) * 1.5);
        }

        .btn-secondary {
            background: var(--color-bg-elevated);
            color: var(--color-text-primary);
            border: 2px solid var(--color-border);
        }

        .btn-secondary:hover {
            background: var(--color-bg-hover);
            border-color: var(--color-border-strong);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Info Banner */
        .info-banner {
            background: rgba(37, 99, 235, 0.05);
            border: 1px solid rgba(37, 99, 235, 0.2);
            border-radius: var(--radius-lg);
            padding: calc(var(--spacing-unit) * 2.5);
            display: flex;
            gap: calc(var(--spacing-unit) * 2);
            margin-bottom: calc(var(--spacing-unit) * 3);
            animation: slideDown 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.15s both;
        }

        .info-icon {
            width: 24px;
            height: 24px;
            background: var(--color-primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            flex-shrink: 0;
        }

        .info-content {
            flex: 1;
        }

        .info-title {
            font-weight: 600;
            color: var(--color-text-primary);
            margin-bottom: 4px;
        }

        .info-text {
            font-size: 14px;
            color: var(--color-text-secondary);
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .app-container {
                grid-template-columns: 1fr;
            }

            .sidebar {
                display: none;
            }

            .workflow-steps {
                flex-direction: column;
            }

            .type-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="logo">
                <div class="logo-icon">K</div>
                <span>Knowledge Base</span>
            </div>
            <div class="header-actions">
                <div class="user-avatar">JD</div>
            </div>
        </header>

        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-section">
                <div class="sidebar-section-title">Main</div>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">🏠</span>
                    <span>Dashboard</span>
                </a>
                <a href="#" class="nav-item active">
                    <span class="nav-item-icon">✨</span>
                    <span>Create Article</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">📚</span>
                    <span>All Articles</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">🔍</span>
                    <span>Search</span>
                </a>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-title">Management</div>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">📋</span>
                    <span>Review Queue</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">📊</span>
                    <span>Analytics</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">🗂️</span>
                    <span>Modules</span>
                </a>
            </div>

            <div class="sidebar-section">
                <div class="sidebar-section-title">Settings</div>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">👥</span>
                    <span>Users</span>
                </a>
                <a href="#" class="nav-item">
                    <span class="nav-item-icon">⚙️</span>
                    <span>Settings</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-wrapper">
                <!-- Page Header -->
                <div class="page-header">
                    <h1 class="page-title">Create New Article</h1>
                    <p class="page-subtitle">AI-powered article creation from any source - paste text, upload documents, or enter manually</p>
                </div>

                <!-- Info Banner -->
                <div class="info-banner">
                    <div class="info-icon">💡</div>
                    <div class="info-content">
                        <div class="info-title">How it works</div>
                        <div class="info-text">Paste your content below and our AI will analyze it to determine the best article type, extract key information, and structure it properly. You can review and edit before publishing.</div>
                    </div>
                </div>

                <!-- Workflow Steps -->
                <div class="workflow-steps">
                    <div class="workflow-step active">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <div class="step-title">Input Content</div>
                            <div class="step-description">Paste or upload your content</div>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <div class="step-title">AI Processing</div>
                            <div class="step-description">AI extracts and structures</div>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="workflow-step-number">3</div>
                        <div class="step-content">
                            <div class="step-title">Review & Edit</div>
                            <div class="step-description">Refine the generated article</div>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <div class="step-title">Publish</div>
                            <div class="step-description">Save draft or submit for review</div>
                        </div>
                    </div>
                </div>

                <!-- Content Input Section -->
                <div class="content-section">
                    <div class="section-header">
                        <div class="section-title">
                            <div class="section-icon">📝</div>
                            <span>Input Your Content</span>
                        </div>
                    </div>

                    <div class="input-area">
                        <div class="input-tabs">
                            <button class="input-tab active" onclick="switchTab('paste')">✍️ Paste Text</button>
                            <button class="input-tab" onclick="switchTab('upload')">📄 Upload File</button>
                            <button class="input-tab" onclick="switchTab('manual')">⌨️ Manual Entry</button>
                        </div>

                        <!-- Paste Text Area -->
                        <div id="paste-tab" class="textarea-wrapper">
                            <textarea 
                                class="content-textarea" 
                                id="content-input"
                                placeholder="Paste your content here... 

You can paste:
• Text from documents (instructions, troubleshooting steps, release notes)
• Conversations from Slack or Teams
• Meeting notes or documentation
• Technical specifications

Our AI will analyze the content and suggest the best article type."
                            ></textarea>
                            <div class="textarea-footer">
                                <span class="char-count"><span id="char-count">0</span> characters</span>
                                <div class="quick-actions">
                                    <button class="quick-action-btn" onclick="clearContent()">Clear</button>
                                    <button class="quick-action-btn" onclick="pasteFromClipboard()">📋 Paste from Clipboard</button>
                                </div>
                            </div>
                        </div>

                        <!-- File Upload Area -->
                        <div id="upload-tab" class="file-upload-area">
                            <div class="file-upload-icon">📄</div>
                            <div class="file-upload-text">Drop files here or click to browse</div>
                            <div class="file-upload-subtext">Supports Word (.docx), PDF (.pdf), and text files • Max 10MB</div>
                        </div>
                    </div>
                </div>

                <!-- AI Processing Section (Hidden initially) -->
                <div class="ai-section" id="ai-processing">
                    <div class="ai-header">
                        <div class="ai-icon">🤖</div>
                        <div class="ai-status">
                            <div class="ai-status-title">AI is analyzing your content...</div>
                            <div class="ai-status-text">Extracting key information and determining article type</div>
                        </div>
                    </div>
                    <div class="ai-progress">
                        <div class="ai-progress-bar" style="width: 65%; background-size: 200% 100%;"></div>
                    </div>
                </div>

                <!-- Article Type Selection (Hidden initially) -->
                <div class="type-selection" id="type-selection">
                    <div class="content-section">
                        <div class="section-header">
                            <div class="section-title">
                                <div class="section-icon">🎯</div>
                                <span>Select Article Type</span>
                            </div>
                        </div>

                        <div class="type-grid">
                            <div class="type-card selected" onclick="selectType(this)">
                                <div class="type-icon">📖</div>
                                <div class="type-title">How-To / Tutorial</div>
                                <div class="type-description">Step-by-step instructions for completing a specific task or process</div>
                                <span class="type-confidence">95% confidence match</span>
                            </div>

                            <div class="type-card" onclick="selectType(this)">
                                <div class="type-icon">🔧</div>
                                <div class="type-title">Troubleshooting</div>
                                <div class="type-description">Problem diagnosis and solutions for fixing issues</div>
                            </div>

                            <div class="type-card" onclick="selectType(this)">
                                <div class="type-icon">📢</div>
                                <div class="type-title">Release Note</div>
                                <div class="type-description">Product changes, updates, new features, and bug fixes</div>
                            </div>

                            <div class="type-card" onclick="selectType(this)">
                                <div class="type-icon">❓</div>
                                <div class="type-title">FAQ</div>
                                <div class="type-description">Frequently asked questions with concise answers</div>
                            </div>

                            <div class="type-card" onclick="selectType(this)">
                                <div class="type-icon">🎨</div>
                                <div class="type-title">Product Overview</div>
                                <div class="type-description">High-level description of products, features, or capabilities</div>
                            </div>

                            <div class="type-card" onclick="selectType(this)">
                                <div class="type-icon">⭐</div>
                                <div class="type-title">Best Practice</div>
                                <div class="type-description">Recommended approaches, patterns, and guidelines</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-bar">
                    <button class="btn btn-secondary">
                        <span>💾</span>
                        <span>Save as Draft</span>
                    </button>
                    <button class="btn btn-primary" onclick="processContent()">
                        <span>✨</span>
                        <span>Process with AI</span>
                    </button>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Character counter
        const textarea = document.getElementById('content-input');
        const charCount = document.getElementById('char-count');
        
        textarea.addEventListener('input', () => {
            charCount.textContent = textarea.value.length;
        });

        // Tab switching
        function switchTab(tabName) {
            // Update active tab
            document.querySelectorAll('.input-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // Show corresponding content
            document.getElementById('paste-tab').style.display = tabName === 'paste' ? 'block' : 'none';
            document.getElementById('upload-tab').classList.toggle('active', tabName === 'upload');
        }

        // Content actions
        function clearContent() {
            textarea.value = '';
            charCount.textContent = '0';
        }

        function pasteFromClipboard() {
            navigator.clipboard.readText().then(text => {
                textarea.value = text;
                charCount.textContent = text.length;
            });
        }

        // Process content with AI
        function processContent() {
            // Hide input section, show AI processing
            document.querySelector('.content-section').style.opacity = '0.5';
            document.querySelector('.content-section').style.pointerEvents = 'none';
            
            const aiSection = document.getElementById('ai-processing');
            aiSection.classList.add('active');

            // Update workflow steps
            document.querySelectorAll('.workflow-step')[0].classList.remove('active');
            document.querySelectorAll('.workflow-step')[0].classList.add('completed');
            document.querySelectorAll('.workflow-step')[1].classList.add('active');

            // Simulate AI processing
            setTimeout(() => {
                aiSection.style.display = 'none';
                document.getElementById('type-selection').classList.add('active');
                
                // Update workflow steps
                document.querySelectorAll('.workflow-step')[1].classList.remove('active');
                document.querySelectorAll('.workflow-step')[1].classList.add('completed');
                document.querySelectorAll('.workflow-step')[2].classList.add('active');
            }, 3000);
        }

        // Type card selection
        function selectType(card) {
            document.querySelectorAll('.type-card').forEach(c => {
                c.classList.remove('selected');
            });
            card.classList.add('selected');
        }

        // Add sample content for demo
        textarea.value = `To set up single sign-on in the admin portal, first you need admin credentials. Log into the admin portal at admin.example.com, then go to Settings > Security. Click on "SSO Configuration" and choose your identity provider (we support Okta, Azure AD, and Google Workspace). For Azure AD, you'll need your tenant ID and client secret. Enter those values, then click "Test Connection" to verify. Once verified, enable SSO for your organization.`;
        charCount.textContent = textarea.value.length;
    </script>
</body>
</html>

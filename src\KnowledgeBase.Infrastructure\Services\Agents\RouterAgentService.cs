using System.Text.Json;
using KnowledgeBase.Core.Enums;
using KnowledgeBase.Core.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace KnowledgeBase.Infrastructure.Services.Agents;

/// <summary>
/// Router agent for classifying content into article types.
/// </summary>
public class RouterAgentService : IRouterAgentService
{
    private readonly IAzureOpenAIClient _openAIClient;
    private readonly ILogger<RouterAgentService> _logger;
    private readonly string _deploymentName;

    public RouterAgentService(
        IAzureOpenAIClient openAIClient,
        IConfiguration configuration,
        ILogger<RouterAgentService> logger)
    {
        _openAIClient = openAIClient;
        _logger = logger;
        _deploymentName = configuration["AzureOpenAI:DeploymentNames:Router"] ?? "gpt-4";
    }

    public async Task<ClassificationResult> ClassifyAsync(string userInput, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Classifying content with Router Agent");

        var response = await _openAIClient.GetChatCompletionAsync(
            _deploymentName,
            AgentPrompts.RouterAgent,
            userInput,
            temperature: 0.3f, // Lower temperature for more consistent classification
            maxTokens: 500,
            cancellationToken);

        if (!response.Success)
        {
            _logger.LogError("Router agent failed: {Error}", response.ErrorMessage);
            return new ClassificationResult
            {
                ArticleType = ArticleType.HowTo, // Default fallback
                Confidence = 0,
                Reasoning = $"Classification failed: {response.ErrorMessage}"
            };
        }

        try
        {
            var json = ExtractJson(response.Content);
            using var doc = JsonDocument.Parse(json);
            var root = doc.RootElement;

            var articleTypeStr = root.GetProperty("article_type").GetString() ?? "how_to";
            var confidence = root.GetProperty("confidence").GetDecimal();
            var reasoning = root.GetProperty("reasoning").GetString() ?? "";

            var articleType = ParseArticleType(articleTypeStr);

            _logger.LogInformation(
                "Classified as {ArticleType} with {Confidence:P0} confidence",
                articleType, confidence);

            return new ClassificationResult
            {
                ArticleType = articleType,
                Confidence = confidence,
                Reasoning = reasoning
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to parse router response: {Response}", response.Content);
            return new ClassificationResult
            {
                ArticleType = ArticleType.HowTo,
                Confidence = 0,
                Reasoning = $"Failed to parse classification: {ex.Message}"
            };
        }
    }

    private static ArticleType ParseArticleType(string typeStr)
    {
        return typeStr.ToLower().Replace("_", "") switch
        {
            "howto" => ArticleType.HowTo,
            "troubleshooting" => ArticleType.Troubleshooting,
            "releasenote" => ArticleType.ReleaseNote,
            "faq" => ArticleType.Faq,
            "productoverview" => ArticleType.ProductOverview,
            "bestpractice" => ArticleType.BestPractice,
            _ => ArticleType.HowTo
        };
    }

    private static string ExtractJson(string content)
    {
        // Handle cases where the response might have markdown code blocks
        content = content.Trim();
        if (content.StartsWith("```json"))
        {
            content = content[7..];
        }
        else if (content.StartsWith("```"))
        {
            content = content[3..];
        }
        if (content.EndsWith("```"))
        {
            content = content[..^3];
        }
        return content.Trim();
    }
}

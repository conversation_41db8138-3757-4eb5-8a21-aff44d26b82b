namespace KnowledgeBase.Core.Enums;

/// <summary>
/// Types of knowledge base articles supported by the platform.
/// </summary>
public enum ArticleType
{
    /// <summary>
    /// Step-by-step instructions for completing a specific task.
    /// </summary>
    HowTo,

    /// <summary>
    /// Problem diagnosis and solutions.
    /// </summary>
    Troubleshooting,

    /// <summary>
    /// Product changes, updates, and new features.
    /// </summary>
    ReleaseNote,

    /// <summary>
    /// Frequently asked questions and answers.
    /// </summary>
    Faq,

    /// <summary>
    /// Product descriptions and capabilities.
    /// </summary>
    ProductOverview,

    /// <summary>
    /// Recommended approaches and patterns.
    /// </summary>
    BestPractice
}

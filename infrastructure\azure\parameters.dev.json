{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"environment": {"value": "dev"}, "baseName": {"value": "meshworkskb"}, "sqlAdminLogin": {"value": "sqladmin"}, "sqlAdminPassword": {"reference": {"keyVault": {"id": "/subscriptions/{subscription-id}/resourceGroups/{rg-name}/providers/Microsoft.KeyVault/vaults/{keyvault-name}"}, "secretName": "sql-admin-password"}}, "azureOpenAIEndpoint": {"value": ""}, "azureOpenAIKey": {"reference": {"keyVault": {"id": "/subscriptions/{subscription-id}/resourceGroups/{rg-name}/providers/Microsoft.KeyVault/vaults/{keyvault-name}"}, "secretName": "azure-openai-key"}}}}
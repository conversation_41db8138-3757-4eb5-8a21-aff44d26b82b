using System.ComponentModel.DataAnnotations;
using KnowledgeBase.Core.Enums;

namespace KnowledgeBase.Core.Entities;

/// <summary>
/// Represents user feedback on an article (helpful/not helpful rating).
/// </summary>
public class ArticleFeedback
{
    [Key]
    public Guid FeedbackId { get; set; }

    public Guid ArticleId { get; set; }

    /// <summary>
    /// User who provided feedback. Null for anonymous feedback.
    /// </summary>
    public Guid? UserId { get; set; }

    public FeedbackType FeedbackType { get; set; }

    [MaxLength(1000)]
    public string? Comments { get; set; }

    public DateTime FeedbackDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Session ID for tracking anonymous feedback.
    /// </summary>
    [MaxLength(100)]
    public string? SessionId { get; set; }

    // Navigation properties
    public virtual Article Article { get; set; } = null!;
    public virtual User? User { get; set; }
}

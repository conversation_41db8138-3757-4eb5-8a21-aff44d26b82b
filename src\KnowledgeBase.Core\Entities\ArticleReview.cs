using System.ComponentModel.DataAnnotations;
using KnowledgeBase.Core.Enums;

namespace KnowledgeBase.Core.Entities;

/// <summary>
/// Represents a review of an article by an SME or reviewer.
/// </summary>
public class ArticleReview
{
    [Key]
    public Guid ReviewId { get; set; }

    public Guid ArticleId { get; set; }

    public Guid ReviewerUserId { get; set; }

    public DateTime ReviewDate { get; set; } = DateTime.UtcNow;

    public ReviewStatus Status { get; set; }

    [MaxLength(2000)]
    public string? Comments { get; set; }

    // Navigation properties
    public virtual Article Article { get; set; } = null!;
    public virtual User Reviewer { get; set; } = null!;
}

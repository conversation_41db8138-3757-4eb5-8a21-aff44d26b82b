using System.ComponentModel.DataAnnotations;
using KnowledgeBase.Core.Enums;

namespace KnowledgeBase.Core.Entities;

/// <summary>
/// Represents a user in the knowledge base system.
/// </summary>
public class User
{
    [Key]
    public Guid UserId { get; set; }

    [Required]
    [MaxLength(255)]
    public string Email { get; set; } = string.Empty;

    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    public UserRole Role { get; set; } = UserRole.User;

    public bool IsActive { get; set; } = true;

    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    public DateTime? LastLoginDate { get; set; }

    // Navigation properties
    public virtual ICollection<Article> CreatedArticles { get; set; } = new List<Article>();
    public virtual ICollection<Article> ModifiedArticles { get; set; } = new List<Article>();
    public virtual ICollection<Article> ReviewedArticles { get; set; } = new List<Article>();
    public virtual ICollection<Module> SMEModules { get; set; } = new List<Module>();
    public virtual ICollection<ArticleReview> Reviews { get; set; } = new List<ArticleReview>();
    public virtual ICollection<ArticleFeedback> Feedback { get; set; } = new List<ArticleFeedback>();
}

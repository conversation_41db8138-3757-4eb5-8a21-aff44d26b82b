using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Enums;
using Microsoft.EntityFrameworkCore;

namespace KnowledgeBase.Infrastructure.Data;

/// <summary>
/// Entity Framework Core database context for the Knowledge Base platform.
/// </summary>
public class KnowledgeBaseContext : DbContext
{
    public KnowledgeBaseContext(DbContextOptions<KnowledgeBaseContext> options)
        : base(options)
    {
    }

    public DbSet<User> Users => Set<User>();
    public DbSet<Module> Modules => Set<Module>();
    public DbSet<Article> Articles => Set<Article>();
    public DbSet<ArticleTicketReference> ArticleTicketReferences => Set<ArticleTicketReference>();
    public DbSet<RelatedArticle> RelatedArticles => Set<RelatedArticle>();
    public DbSet<ArticleVersion> ArticleVersions => Set<ArticleVersion>();
    public DbSet<ArticleReview> ArticleReviews => Set<ArticleReview>();
    public DbSet<ArticleFeedback> ArticleFeedback => Set<ArticleFeedback>();
    public DbSet<AuditLog> AuditLogs => Set<AuditLog>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply all configurations from the assembly
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(KnowledgeBaseContext).Assembly);

        // Seed default system user
        modelBuilder.Entity<User>().HasData(new User
        {
            UserId = Guid.Parse("00000000-0000-0000-0000-000000000001"),
            Email = "<EMAIL>",
            Name = "System User",
            Role = UserRole.Admin,
            IsActive = true,
            CreatedDate = new DateTime(2026, 1, 1, 0, 0, 0, DateTimeKind.Utc)
        });

        // Seed default modules
        var seedDate = new DateTime(2026, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        modelBuilder.Entity<Module>().HasData(
            new Module
            {
                ModuleId = Guid.Parse("10000000-0000-0000-0000-000000000001"),
                Name = "General",
                Description = "General knowledge base articles",
                IsActive = true,
                DisplayOrder = 1,
                CreatedDate = seedDate
            },
            new Module
            {
                ModuleId = Guid.Parse("10000000-0000-0000-0000-000000000002"),
                Name = "Meshworks Platform",
                Description = "Meshworks platform documentation and guides",
                IsActive = true,
                DisplayOrder = 2,
                CreatedDate = seedDate
            },
            new Module
            {
                ModuleId = Guid.Parse("10000000-0000-0000-0000-000000000003"),
                Name = "Administration",
                Description = "System administration and configuration",
                IsActive = true,
                DisplayOrder = 3,
                CreatedDate = seedDate
            },
            new Module
            {
                ModuleId = Guid.Parse("10000000-0000-0000-0000-000000000004"),
                Name = "Integrations",
                Description = "Third-party integrations and APIs",
                IsActive = true,
                DisplayOrder = 4,
                CreatedDate = seedDate
            }
        );
    }

    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is Article && (e.State == EntityState.Added || e.State == EntityState.Modified));

        foreach (var entry in entries)
        {
            var article = (Article)entry.Entity;
            article.LastModifiedDate = DateTime.UtcNow;

            if (entry.State == EntityState.Added)
            {
                article.CreatedDate = DateTime.UtcNow;
            }
        }
    }
}

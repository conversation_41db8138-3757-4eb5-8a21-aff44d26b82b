using Azure;
using Azure.AI.OpenAI;
using KnowledgeBase.Core.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using OpenAI.Chat;
using OpenAI.Embeddings;

namespace KnowledgeBase.Infrastructure.Services;

/// <summary>
/// Azure OpenAI client wrapper for AI operations.
/// </summary>
public class AzureOpenAIClientWrapper : IAzureOpenAIClient
{
    private readonly AzureOpenAIClient _client;
    private readonly ILogger<AzureOpenAIClientWrapper> _logger;
    private readonly string _embeddingDeployment;

    public AzureOpenAIClientWrapper(IConfiguration configuration, ILogger<AzureOpenAIClientWrapper> logger)
    {
        _logger = logger;

        var endpoint = configuration["AzureOpenAI:Endpoint"]
            ?? throw new InvalidOperationException("AzureOpenAI:Endpoint not configured");
        var apiKey = configuration["AzureOpenAI:ApiKey"]
            ?? throw new InvalidOperationException("AzureOpenAI:ApiKey not configured");

        _embeddingDeployment = configuration["AzureOpenAI:DeploymentNames:Embedding"] ?? "text-embedding-ada-002";

        _client = new AzureOpenAIClient(new Uri(endpoint), new AzureKeyCredential(apiKey));
    }

    public async Task<ChatCompletionResult> GetChatCompletionAsync(
        string deploymentName,
        string systemPrompt,
        string userMessage,
        float temperature = 0.7f,
        int maxTokens = 2000,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var chatClient = _client.GetChatClient(deploymentName);

            var messages = new List<ChatMessage>
            {
                new SystemChatMessage(systemPrompt),
                new UserChatMessage(userMessage)
            };

            var options = new ChatCompletionOptions
            {
                Temperature = temperature,
                MaxOutputTokenCount = maxTokens
            };

            var response = await chatClient.CompleteChatAsync(messages, options, cancellationToken);
            var completion = response.Value;

            _logger.LogInformation(
                "Chat completion for {Deployment}: {PromptTokens} prompt tokens, {CompletionTokens} completion tokens",
                deploymentName,
                completion.Usage.InputTokenCount,
                completion.Usage.OutputTokenCount);

            return new ChatCompletionResult
            {
                Success = true,
                Content = completion.Content[0].Text,
                PromptTokens = completion.Usage.InputTokenCount,
                CompletionTokens = completion.Usage.OutputTokenCount,
                TotalTokens = completion.Usage.TotalTokenCount,
                FinishReason = completion.FinishReason.ToString()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting chat completion from {Deployment}", deploymentName);

            return new ChatCompletionResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<float[]> GetEmbeddingsAsync(string text, CancellationToken cancellationToken = default)
    {
        try
        {
            var embeddingClient = _client.GetEmbeddingClient(_embeddingDeployment);
            var response = await embeddingClient.GenerateEmbeddingAsync(text, cancellationToken: cancellationToken);

            return response.Value.ToFloats().ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting embeddings");
            throw;
        }
    }
}

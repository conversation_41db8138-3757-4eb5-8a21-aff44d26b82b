using KnowledgeBase.Api.Models.DTOs;
using KnowledgeBase.Api.Models.Responses;
using Microsoft.AspNetCore.Mvc;

namespace KnowledgeBase.Api.Controllers;

/// <summary>
/// API endpoints for user management.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class UsersController : ControllerBase
{
    private readonly ILogger<UsersController> _logger;

    public UsersController(ILogger<UsersController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Get current user profile.
    /// </summary>
    [HttpGet("me")]
    [ProducesResponseType(typeof(ApiResponse<UserDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ApiResponse<UserDto>>> GetCurrentUser()
    {
        // TODO: Implement with auth and UserService
        _logger.LogInformation("Getting current user");

        // Return mock data for now
        var user = new UserDto
        {
            UserId = Guid.NewGuid(),
            Email = "<EMAIL>",
            Name = "Demo User",
            Role = "user",
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        };

        return Ok(ApiResponse<UserDto>.Ok(user));
    }

    /// <summary>
    /// Get all users (admin only).
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResponse<UserDto>>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<PagedResponse<UserDto>>>> GetUsers(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        // TODO: Implement with UserService
        _logger.LogInformation("Getting all users");

        var response = PagedResponse<UserDto>.Create(
            new List<UserDto>(),
            0,
            page,
            pageSize
        );

        return Ok(ApiResponse<PagedResponse<UserDto>>.Ok(response));
    }

    /// <summary>
    /// Update user role (admin only).
    /// </summary>
    [HttpPut("{id:guid}/role")]
    [ProducesResponseType(typeof(ApiResponse<UserDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<UserDto>>> UpdateUserRole(
        Guid id,
        [FromBody] UpdateRoleRequest request)
    {
        // TODO: Implement with UserService
        _logger.LogInformation("Updating role for user {UserId} to {Role}", id, request.Role);

        return NotFound(ApiResponse<UserDto>.Fail("User not found."));
    }
}

public class UpdateRoleRequest
{
    public string Role { get; set; } = string.Empty;
}

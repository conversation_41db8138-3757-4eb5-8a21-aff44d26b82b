# Azure Pipeline for Web Application Deployment
# Builds and deploys the KnowledgeBase.Web Blazor project
#
# PREREQUISITES:
# 1. Create a Service Connection named 'Azure-ServiceConnection' (Azure Resource Manager)
# 2. Run the infrastructure pipeline first to create Azure resources

trigger:
  branches:
    include:
      - master
  paths:
    include:
      - src/KnowledgeBase.Web/**

pr:
  branches:
    include:
      - main
  paths:
    include:
      - src/KnowledgeBase.Web/**

parameters:
  - name: environment
    displayName: 'Environment'
    type: string
    default: 'dev'
    values:
      - dev
      - staging
      - prod

variables:
  - name: buildConfiguration
    value: 'Release'
  - name: dotnetVersion
    value: '8.0.x'
  - name: azureServiceConnection
    value: 'Azure-ServiceConnection'
  - name: resourceGroupName
    value: 'rg-meshworkskb-${{ parameters.environment }}'
  - name: webAppName
    value: 'web-meshworkskb-${{ parameters.environment }}'
  - name: apiAppName
    value: 'api-meshworkskb-${{ parameters.environment }}'
  - name: projectPath
    value: 'src/KnowledgeBase.Web/KnowledgeBase.Web.csproj'
  - name: aspnetEnvironment
    ${{ if eq(parameters.environment, 'prod') }}:
      value: 'Production'
    ${{ else }}:
      value: 'Development'

stages:
  - stage: Build
    displayName: 'Build Web Application'
    jobs:
      - job: BuildWeb
        displayName: 'Build Blazor Web App'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - checkout: self

          - task: UseDotNet@2
            displayName: 'Install .NET SDK'
            inputs:
              version: $(dotnetVersion)
              includePreviewVersions: false

          - task: DotNetCoreCLI@2
            displayName: 'Restore NuGet Packages'
            inputs:
              command: 'restore'
              projects: '$(projectPath)'
              feedsToUse: 'select'

          - task: DotNetCoreCLI@2
            displayName: 'Build Web Project'
            inputs:
              command: 'build'
              projects: '$(projectPath)'
              arguments: '--configuration $(buildConfiguration) --no-restore'

          - task: DotNetCoreCLI@2
            displayName: 'Publish Web Project'
            inputs:
              command: 'publish'
              publishWebProjects: false
              projects: '$(projectPath)'
              arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/web --no-build'
              zipAfterPublish: true

          - task: PublishPipelineArtifact@1
            displayName: 'Publish Web Artifact'
            inputs:
              targetPath: '$(Build.ArtifactStagingDirectory)/web'
              artifact: 'web-drop'
              publishLocation: 'pipeline'

  - stage: Deploy
    displayName: 'Deploy Web Application'
    dependsOn: Build
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
    jobs:
      - job: DeployWeb
        displayName: 'Deploy to Azure App Service'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - download: current
            artifact: web-drop
            displayName: 'Download Web Artifact'

          - task: AzureWebApp@1
            displayName: 'Deploy to Azure App Service'
            inputs:
              azureSubscription: $(azureServiceConnection)
              appType: 'webAppLinux'
              appName: $(webAppName)
              package: '$(Pipeline.Workspace)/web-drop/**/*.zip'
              runtimeStack: 'DOTNETCORE|8.0'

          - task: AzureCLI@2
            displayName: 'Configure App Settings'
            inputs:
              azureSubscription: $(azureServiceConnection)
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                API_URL="https://$(apiAppName).azurewebsites.net"

                az webapp config appsettings set \
                  --resource-group $(resourceGroupName) \
                  --name $(webAppName) \
                  --settings \
                    "ApiBaseUrl=$API_URL" \
                    "ASPNETCORE_ENVIRONMENT=$(aspnetEnvironment)"

          - task: AzureCLI@2
            displayName: 'Configure Web App'
            inputs:
              azureSubscription: $(azureServiceConnection)
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                # Enable WebSockets for Blazor Server
                az webapp config set \
                  --resource-group $(resourceGroupName) \
                  --name $(webAppName) \
                  --web-sockets-enabled true

                # Set minimum TLS version
                az webapp config set \
                  --resource-group $(resourceGroupName) \
                  --name $(webAppName) \
                  --min-tls-version 1.2

                # Enable HTTP/2
                az webapp config set \
                  --resource-group $(resourceGroupName) \
                  --name $(webAppName) \
                  --http20-enabled true

  - stage: Verify
    displayName: 'Verify Deployment'
    dependsOn: Deploy
    condition: succeeded()
    jobs:
      - job: HealthCheck
        displayName: 'Web App Health Check'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: Bash@3
            displayName: 'Verify Web App Health'
            inputs:
              targetType: 'inline'
              script: |
                WEB_URL="https://$(webAppName).azurewebsites.net"

                echo "Waiting for deployment to complete..."
                sleep 60

                for i in {1..5}; do
                  HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$WEB_URL" || echo "000")

                  if [ "$HTTP_STATUS" -eq "200" ]; then
                    echo "Web app health check passed (attempt $i)"
                    exit 0
                  fi

                  echo "Attempt $i: Status $HTTP_STATUS, retrying..."
                  sleep 10
                done

                echo "Web app health check failed after 5 attempts"
                exit 1

          - task: Bash@3
            displayName: 'Verify API Connectivity'
            inputs:
              targetType: 'inline'
              script: |
                API_URL="https://$(apiAppName).azurewebsites.net/health"

                HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL" || echo "000")

                if [ "$HTTP_STATUS" -eq "200" ]; then
                  echo "Web app can reach the API successfully"
                else
                  echo "Warning: API connectivity check returned status $HTTP_STATUS"
                fi

-- Knowledge Base Platform Full-Text Search Setup
-- Version: 1.0.0
-- Created: 2026-01-29

-- =============================================
-- Create Full-Text Catalog
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.fulltext_catalogs WHERE name = 'KnowledgeBaseCatalog')
BEGIN
    CREATE FULLTEXT CATALOG KnowledgeBaseCatalog AS DEFAULT;
END
GO

-- =============================================
-- Create Full-Text Index on Articles
-- =============================================
-- Note: Full-text index requires a unique, non-nullable key column
-- Using ArticleId as the key

IF NOT EXISTS (
    SELECT * FROM sys.fulltext_indexes
    WHERE object_id = OBJECT_ID('Articles')
)
BEGIN
    CREATE FULLTEXT INDEX ON Articles
    (
        Title LANGUAGE 1033,      -- English
        Summary LANGUAGE 1033,
        Content LANGUAGE 1033,
        Tags LANGUAGE 1033
    )
    KEY INDEX PK__Articles__9C6270C8  -- Replace with actual PK constraint name after creation
    ON KnowledgeBaseCatalog
    WITH STOPLIST = SYSTEM, CHANGE_TRACKING AUTO;
END
GO

-- =============================================
-- Create Full-Text Index on Modules
-- =============================================
IF NOT EXISTS (
    SELECT * FROM sys.fulltext_indexes
    WHERE object_id = OBJECT_ID('Modules')
)
BEGIN
    CREATE FULLTEXT INDEX ON Modules
    (
        Name LANGUAGE 1033,
        Description LANGUAGE 1033
    )
    KEY INDEX PK__Modules__2B747777  -- Replace with actual PK constraint name after creation
    ON KnowledgeBaseCatalog
    WITH STOPLIST = SYSTEM, CHANGE_TRACKING AUTO;
END
GO

-- =============================================
-- Helper View for Article Search
-- =============================================
CREATE OR ALTER VIEW vw_SearchableArticles AS
SELECT
    a.ArticleId,
    a.ArticleType,
    a.Title,
    a.Summary,
    a.Content,
    a.ModuleId,
    m.Name AS ModuleName,
    a.Category,
    a.Tags,
    a.Status,
    a.CreatedDate,
    a.LastModifiedDate,
    a.ViewCount,
    a.HelpfulCount,
    a.NotHelpfulCount,
    CASE
        WHEN (a.HelpfulCount + a.NotHelpfulCount) > 0
        THEN CAST(a.HelpfulCount AS DECIMAL(5,2)) / (a.HelpfulCount + a.NotHelpfulCount)
        ELSE NULL
    END AS Rating,
    a.EstimatedTimeMinutes,
    a.Difficulty,
    u.Name AS AuthorName,
    a.Version
FROM Articles a
INNER JOIN Modules m ON a.ModuleId = m.ModuleId
INNER JOIN Users u ON a.CreatedByUserId = u.UserId
WHERE a.IsDeleted = 0;
GO

-- =============================================
-- Search Stored Procedure
-- =============================================
CREATE OR ALTER PROCEDURE sp_SearchArticles
    @SearchTerm NVARCHAR(200),
    @ArticleType VARCHAR(50) = NULL,
    @ModuleId UNIQUEIDENTIFIER = NULL,
    @Status VARCHAR(20) = 'published',
    @PageNumber INT = 1,
    @PageSize INT = 20
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;

    -- Search using CONTAINS for full-text search
    SELECT
        a.ArticleId,
        a.ArticleType,
        a.Title,
        a.Summary,
        a.ModuleId,
        m.Name AS ModuleName,
        a.Category,
        a.Tags,
        a.Status,
        a.CreatedDate,
        a.ViewCount,
        CASE
            WHEN (a.HelpfulCount + a.NotHelpfulCount) > 0
            THEN CAST(a.HelpfulCount AS DECIMAL(5,2)) / (a.HelpfulCount + a.NotHelpfulCount)
            ELSE NULL
        END AS Rating,
        KEY_TBL.RANK AS SearchRank
    FROM Articles a
    INNER JOIN Modules m ON a.ModuleId = m.ModuleId
    INNER JOIN CONTAINSTABLE(Articles, (Title, Summary, Content, Tags), @SearchTerm) AS KEY_TBL
        ON a.ArticleId = KEY_TBL.[KEY]
    WHERE a.IsDeleted = 0
        AND (@ArticleType IS NULL OR a.ArticleType = @ArticleType)
        AND (@ModuleId IS NULL OR a.ModuleId = @ModuleId)
        AND (@Status IS NULL OR a.Status = @Status)
    ORDER BY KEY_TBL.RANK DESC, a.ViewCount DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END
GO

-- =============================================
-- Get Total Search Results Count
-- =============================================
CREATE OR ALTER PROCEDURE sp_SearchArticlesCount
    @SearchTerm NVARCHAR(200),
    @ArticleType VARCHAR(50) = NULL,
    @ModuleId UNIQUEIDENTIFIER = NULL,
    @Status VARCHAR(20) = 'published'
AS
BEGIN
    SET NOCOUNT ON;

    SELECT COUNT(*) AS TotalCount
    FROM Articles a
    INNER JOIN CONTAINSTABLE(Articles, (Title, Summary, Content, Tags), @SearchTerm) AS KEY_TBL
        ON a.ArticleId = KEY_TBL.[KEY]
    WHERE a.IsDeleted = 0
        AND (@ArticleType IS NULL OR a.ArticleType = @ArticleType)
        AND (@ModuleId IS NULL OR a.ModuleId = @ModuleId)
        AND (@Status IS NULL OR a.Status = @Status);
END
GO

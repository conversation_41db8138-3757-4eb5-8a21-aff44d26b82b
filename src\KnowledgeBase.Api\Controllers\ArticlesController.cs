using System.Text.Json;
using KnowledgeBase.Api.Models.DTOs;
using KnowledgeBase.Api.Models.Requests;
using KnowledgeBase.Api.Models.Responses;
using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Enums;
using KnowledgeBase.Core.Interfaces;
using KnowledgeBase.Infrastructure.Services;
using Microsoft.AspNetCore.Mvc;

namespace KnowledgeBase.Api.Controllers;

/// <summary>
/// API endpoints for managing knowledge base articles.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ArticlesController : ControllerBase
{
    private readonly IArticleService _articleService;
    private readonly ArticleOrchestrator? _orchestrator;
    private readonly ILogger<ArticlesController> _logger;

    public ArticlesController(
        IArticleService articleService,
        ILogger<ArticlesController> logger,
        ArticleOrchestrator? orchestrator = null)
    {
        _articleService = articleService;
        _logger = logger;
        _orchestrator = orchestrator;
    }

    /// <summary>
    /// Get a paginated list of articles.
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResponse<ArticleSummaryDto>>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<PagedResponse<ArticleSummaryDto>>>> GetArticles(
        [FromQuery] SearchRequest request,
        CancellationToken cancellationToken)
    {
        var criteria = new ArticleSearchCriteria
        {
            Query = request.Query,
            ArticleType = ParseArticleType(request.ArticleType),
            ModuleId = request.ModuleId,
            Category = request.Category,
            Status = ParseArticleStatus(request.Status),
            Tags = request.Tags?.Split(',').ToList(),
            SortBy = request.SortBy,
            SortDescending = request.SortDirection.ToLower() == "desc",
            Page = request.Page,
            PageSize = Math.Min(request.PageSize, 100)
        };

        var (items, totalCount) = await _articleService.GetArticlesAsync(criteria, cancellationToken);

        var dtos = items.Select(MapToSummaryDto).ToList();
        var response = PagedResponse<ArticleSummaryDto>.Create(dtos, totalCount, request.Page, request.PageSize);

        return Ok(ApiResponse<PagedResponse<ArticleSummaryDto>>.Ok(response));
    }

    /// <summary>
    /// Get a single article by ID.
    /// </summary>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(ApiResponse<ArticleDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<ArticleDto>>> GetArticle(Guid id, CancellationToken cancellationToken)
    {
        var article = await _articleService.GetByIdAsync(id, cancellationToken);
        if (article == null)
        {
            return NotFound(ApiResponse<ArticleDto>.Fail("Article not found."));
        }

        // Increment view count asynchronously
        _ = _articleService.IncrementViewCountAsync(id, CancellationToken.None);

        return Ok(ApiResponse<ArticleDto>.Ok(MapToDto(article)));
    }

    /// <summary>
    /// Create a new article using AI processing.
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(ArticleCreationResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ArticleCreationResponse>> CreateArticle(
        [FromBody] CreateArticleRequest request,
        CancellationToken cancellationToken)
    {
        if (_orchestrator == null)
        {
            return StatusCode(StatusCodes.Status503ServiceUnavailable,
                new ArticleCreationResponse { Success = false, ClarificationNeeded = "AI services not available." });
        }

        var articleType = ParseArticleType(request.ArticleType);

        // TODO: Get actual user ID from authentication
        var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

        var result = await _orchestrator.CreateArticleAsync(
            request.UserInput,
            articleType,
            request.ModuleId,
            userId,
            cancellationToken);

        if (!result.Success)
        {
            return BadRequest(new ArticleCreationResponse
            {
                Success = false,
                ClarificationNeeded = result.ClarificationNeeded,
                Classification = result.Classification != null ? new ArticleClassificationResult
                {
                    ArticleType = result.Classification.ArticleType.ToString(),
                    Confidence = result.Classification.Confidence,
                    Reasoning = result.Classification.Reasoning
                } : null
            });
        }

        var response = new ArticleCreationResponse
        {
            Success = true,
            Article = result.Article != null ? MapToDto(result.Article) : null,
            Classification = result.Classification != null ? new ArticleClassificationResult
            {
                ArticleType = result.Classification.ArticleType.ToString(),
                Confidence = result.Classification.Confidence,
                Reasoning = result.Classification.Reasoning
            } : null,
            Evaluation = result.Evaluation != null ? new ArticleEvaluationResult
            {
                OverallScore = result.Evaluation.OverallScore,
                Scores = result.Evaluation.Scores,
                Strengths = result.Evaluation.Strengths,
                Issues = result.Evaluation.Issues.Select(i => new ArticleIssue
                {
                    Severity = i.Severity,
                    Category = i.Category,
                    Description = i.Description,
                    Suggestion = i.Suggestion
                }).ToList(),
                RecommendedChanges = result.Evaluation.RecommendedChanges,
                ReadabilityLevel = result.Evaluation.ReadabilityLevel,
                EstimatedReadTimeMinutes = result.Evaluation.EstimatedReadTimeMinutes
            } : null
        };

        return CreatedAtAction(nameof(GetArticle), new { id = result.ArticleId }, response);
    }

    /// <summary>
    /// Generate an article using AI processing (without immediately saving).
    /// </summary>
    [HttpPost("generate")]
    [ProducesResponseType(typeof(ApiResponse<ArticleCreationResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
    public async Task<ActionResult<ApiResponse<ArticleCreationResponse>>> GenerateArticle(
        [FromBody] GenerateArticleApiRequest request,
        CancellationToken cancellationToken)
    {
        if (_orchestrator == null)
        {
            return StatusCode(StatusCodes.Status503ServiceUnavailable,
                ApiResponse<ArticleCreationResponse>.Fail("AI services not available."));
        }

        var articleType = ParseArticleType(request.ArticleType);
        // TODO: Get actual user ID from authentication
        var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

        var result = await _orchestrator.CreateArticleAsync(
            request.Content,
            articleType,
            request.ModuleId,
            userId,
            cancellationToken);

        if (!result.Success)
        {
            return BadRequest(ApiResponse<ArticleCreationResponse>.Fail(
                result.ClarificationNeeded ?? "Failed to generate article"));
        }

        var response = new ArticleCreationResponse
        {
            Success = true,
            Article = result.Article != null ? MapToDto(result.Article) : null,
            Classification = result.Classification != null ? new ArticleClassificationResult
            {
                ArticleType = result.Classification.ArticleType.ToString(),
                Confidence = result.Classification.Confidence,
                Reasoning = result.Classification.Reasoning
            } : null,
            Evaluation = result.Evaluation != null ? new ArticleEvaluationResult
            {
                OverallScore = result.Evaluation.OverallScore,
                Scores = result.Evaluation.Scores,
                Strengths = result.Evaluation.Strengths,
                Issues = result.Evaluation.Issues.Select(i => new ArticleIssue
                {
                    Severity = i.Severity,
                    Category = i.Category,
                    Description = i.Description,
                    Suggestion = i.Suggestion
                }).ToList(),
                RecommendedChanges = result.Evaluation.RecommendedChanges,
                ReadabilityLevel = result.Evaluation.ReadabilityLevel,
                EstimatedReadTimeMinutes = result.Evaluation.EstimatedReadTimeMinutes
            } : null
        };

        return Ok(ApiResponse<ArticleCreationResponse>.Ok(response));
    }

    /// <summary>
    /// Save a new article directly (without AI processing).
    /// </summary>
    [HttpPost("save")]
    [ProducesResponseType(typeof(ApiResponse<ArticleDto>), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<ArticleDto>>> SaveArticle(
        [FromBody] SaveArticleRequest request,
        CancellationToken cancellationToken)
    {
        // TODO: Get actual user ID from authentication
        var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

        var article = new Article
        {
            ArticleType = ParseArticleType(request.ArticleType) ?? ArticleType.HowTo,
            Title = request.Title,
            Summary = request.Summary,
            Content = JsonSerializer.Serialize(request.Content),
            ModuleId = request.ModuleId,
            Category = request.Category,
            Tags = request.Tags != null ? JsonSerializer.Serialize(request.Tags) : null,
            EstimatedTimeMinutes = request.EstimatedTimeMinutes,
            Difficulty = ParseDifficulty(request.Difficulty),
            AppliesTo = request.AppliesTo != null ? JsonSerializer.Serialize(request.AppliesTo) : null,
            Status = ArticleStatus.Draft
        };

        var created = await _articleService.CreateAsync(article, userId, cancellationToken);

        return CreatedAtAction(nameof(GetArticle), new { id = created.ArticleId },
            ApiResponse<ArticleDto>.Ok(MapToDto(created)));
    }

    /// <summary>
    /// Save/update an article.
    /// </summary>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(typeof(ApiResponse<ArticleDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<ArticleDto>>> UpdateArticle(
        Guid id,
        [FromBody] SaveArticleRequest request,
        CancellationToken cancellationToken)
    {
        var existing = await _articleService.GetByIdAsync(id, cancellationToken);
        if (existing == null)
        {
            return NotFound(ApiResponse<ArticleDto>.Fail("Article not found."));
        }

        var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

        existing.Title = request.Title;
        existing.Summary = request.Summary;
        existing.Content = JsonSerializer.Serialize(request.Content);
        existing.ModuleId = request.ModuleId;
        existing.Category = request.Category;
        existing.Tags = request.Tags != null ? JsonSerializer.Serialize(request.Tags) : null;
        existing.EstimatedTimeMinutes = request.EstimatedTimeMinutes;
        existing.Difficulty = ParseDifficulty(request.Difficulty);
        existing.AppliesTo = request.AppliesTo != null ? JsonSerializer.Serialize(request.AppliesTo) : null;

        var updated = await _articleService.UpdateAsync(existing, userId, "Updated via API", cancellationToken);

        return Ok(ApiResponse<ArticleDto>.Ok(MapToDto(updated)));
    }

    /// <summary>
    /// Delete an article (soft delete).
    /// </summary>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse>> DeleteArticle(Guid id, CancellationToken cancellationToken)
    {
        var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");
        var deleted = await _articleService.DeleteAsync(id, userId, cancellationToken);

        if (!deleted)
        {
            return NotFound(ApiResponse.Fail("Article not found."));
        }

        return Ok(ApiResponse.Ok("Article deleted successfully."));
    }

    /// <summary>
    /// Update article status (e.g., Draft, InReview, Published, Archived).
    /// </summary>
    [HttpPatch("{id:guid}/status")]
    [ProducesResponseType(typeof(ApiResponse<ArticleDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<ArticleDto>>> UpdateArticleStatus(
        Guid id,
        [FromBody] UpdateStatusRequest request,
        CancellationToken cancellationToken)
    {
        var article = await _articleService.GetByIdAsync(id, cancellationToken);
        if (article == null)
        {
            return NotFound(ApiResponse<ArticleDto>.Fail("Article not found."));
        }

        var newStatus = ParseArticleStatus(request.Status);
        if (newStatus == null)
        {
            return BadRequest(ApiResponse<ArticleDto>.Fail($"Invalid status: {request.Status}"));
        }

        // TODO: Get actual user ID from authentication
        var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

        // Use dedicated methods for publish/archive, direct update for others
        Article updated;
        if (newStatus == ArticleStatus.Published)
        {
            updated = await _articleService.PublishAsync(id, userId, "Published via status update", cancellationToken);
        }
        else if (newStatus == ArticleStatus.Archived)
        {
            updated = await _articleService.ArchiveAsync(id, userId, cancellationToken);
        }
        else
        {
            article.Status = newStatus.Value;
            updated = await _articleService.UpdateAsync(article, userId, $"Status changed to {newStatus}", cancellationToken);
        }

        return Ok(ApiResponse<ArticleDto>.Ok(MapToDto(updated)));
    }

    /// <summary>
    /// Publish an article.
    /// </summary>
    [HttpPost("{id:guid}/publish")]
    [ProducesResponseType(typeof(ApiResponse<ArticleDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<ArticleDto>>> PublishArticle(
        Guid id,
        [FromBody] PublishArticleRequest request,
        CancellationToken cancellationToken)
    {
        var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

        try
        {
            var article = await _articleService.PublishAsync(id, userId, request.ChangeDescription, cancellationToken);
            return Ok(ApiResponse<ArticleDto>.Ok(MapToDto(article)));
        }
        catch (InvalidOperationException)
        {
            return NotFound(ApiResponse<ArticleDto>.Fail("Article not found."));
        }
    }

    /// <summary>
    /// Submit feedback on an article.
    /// </summary>
    [HttpPost("{id:guid}/feedback")]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse>> SubmitFeedback(
        Guid id,
        [FromBody] ArticleFeedbackRequest request,
        CancellationToken cancellationToken)
    {
        var feedbackType = request.FeedbackType.ToLower() == "helpful"
            ? FeedbackType.Helpful
            : FeedbackType.NotHelpful;

        try
        {
            await _articleService.SubmitFeedbackAsync(id, null, feedbackType, request.Comments, null, cancellationToken);
            return Ok(ApiResponse.Ok("Feedback submitted successfully."));
        }
        catch (InvalidOperationException)
        {
            return NotFound(ApiResponse.Fail("Article not found."));
        }
    }

    /// <summary>
    /// Get article version history.
    /// </summary>
    [HttpGet("{id:guid}/versions")]
    [ProducesResponseType(typeof(ApiResponse<List<ArticleVersionDto>>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<List<ArticleVersionDto>>>> GetArticleVersions(
        Guid id,
        CancellationToken cancellationToken)
    {
        var versions = await _articleService.GetVersionHistoryAsync(id, cancellationToken);

        var dtos = versions.Select(v => new ArticleVersionDto
        {
            VersionId = v.VersionId,
            VersionNumber = v.VersionNumber,
            Title = v.Title,
            ChangeDescription = v.ChangeDescription,
            ModifiedByName = v.ModifiedBy?.Name ?? "Unknown",
            ModifiedDate = v.ModifiedDate
        }).ToList();

        return Ok(ApiResponse<List<ArticleVersionDto>>.Ok(dtos));
    }

    #region Helper Methods

    private static ArticleDto MapToDto(Article article)
    {
        return new ArticleDto
        {
            ArticleId = article.ArticleId,
            ArticleType = article.ArticleType.ToString(),
            Title = article.Title,
            Summary = article.Summary,
            Content = TryParseJson(article.Content),
            ModuleId = article.ModuleId,
            ModuleName = article.Module?.Name ?? "",
            Category = article.Category,
            Tags = TryParseStringArray(article.Tags),
            EstimatedTimeMinutes = article.EstimatedTimeMinutes,
            Difficulty = article.Difficulty?.ToString(),
            AppliesTo = TryParseStringArray(article.AppliesTo),
            Status = article.Status.ToString(),
            CreatedByName = article.CreatedBy?.Name ?? "",
            CreatedDate = article.CreatedDate,
            LastModifiedByName = article.LastModifiedBy?.Name ?? "",
            LastModifiedDate = article.LastModifiedDate,
            ViewCount = article.ViewCount,
            HelpfulCount = article.HelpfulCount,
            NotHelpfulCount = article.NotHelpfulCount,
            Rating = article.Rating,
            Version = article.Version,
            RelatedArticles = article.RelatedArticles?.Select(r => new RelatedArticleDto
            {
                ArticleId = r.RelatedArticleId,
                Title = r.Related?.Title ?? "",
                RelationshipType = r.RelationshipType.ToString(),
                RelevanceScore = r.RelevanceScore
            }).ToList() ?? new(),
            TicketReferences = article.TicketReferences?.Select(t => new TicketReferenceDto
            {
                TicketId = t.TicketId,
                TicketSystem = t.TicketSystem,
                TicketUrl = t.TicketUrl
            }).ToList() ?? new()
        };
    }

    private static ArticleSummaryDto MapToSummaryDto(Article article)
    {
        return new ArticleSummaryDto
        {
            ArticleId = article.ArticleId,
            ArticleType = article.ArticleType.ToString(),
            Title = article.Title,
            Summary = article.Summary,
            ModuleName = article.Module?.Name ?? "",
            Category = article.Category,
            Tags = TryParseStringArray(article.Tags),
            Status = article.Status.ToString(),
            CreatedDate = article.CreatedDate,
            LastModifiedDate = article.LastModifiedDate,
            ViewCount = article.ViewCount,
            Rating = article.Rating
        };
    }

    private static object? TryParseJson(string? json)
    {
        if (string.IsNullOrEmpty(json)) return null;
        try { return JsonSerializer.Deserialize<object>(json); }
        catch { return json; }
    }

    private static List<string> TryParseStringArray(string? json)
    {
        if (string.IsNullOrEmpty(json)) return new List<string>();
        try { return JsonSerializer.Deserialize<List<string>>(json) ?? new List<string>(); }
        catch { return new List<string>(); }
    }

    private static ArticleType? ParseArticleType(string? type)
    {
        if (string.IsNullOrEmpty(type)) return null;
        return type.ToLower().Replace("_", "") switch
        {
            "howto" => ArticleType.HowTo,
            "troubleshooting" => ArticleType.Troubleshooting,
            "releasenote" => ArticleType.ReleaseNote,
            "faq" => ArticleType.Faq,
            "productoverview" => ArticleType.ProductOverview,
            "bestpractice" => ArticleType.BestPractice,
            _ => null
        };
    }

    private static ArticleStatus? ParseArticleStatus(string? status)
    {
        if (string.IsNullOrEmpty(status)) return null;
        return status.ToLower() switch
        {
            "draft" => ArticleStatus.Draft,
            "inreview" or "in_review" => ArticleStatus.InReview,
            "published" => ArticleStatus.Published,
            "archived" => ArticleStatus.Archived,
            _ => null
        };
    }

    private static Difficulty? ParseDifficulty(string? difficulty)
    {
        if (string.IsNullOrEmpty(difficulty)) return null;
        return difficulty.ToLower() switch
        {
            "beginner" => Difficulty.Beginner,
            "intermediate" => Difficulty.Intermediate,
            "advanced" => Difficulty.Advanced,
            _ => null
        };
    }

    #endregion
}

public class ArticleVersionDto
{
    public Guid VersionId { get; set; }
    public int VersionNumber { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? ChangeDescription { get; set; }
    public string ModifiedByName { get; set; } = string.Empty;
    public DateTime ModifiedDate { get; set; }
}

using KnowledgeBase.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KnowledgeBase.Infrastructure.Data.Configurations;

public class ArticleTicketReferenceConfiguration : IEntityTypeConfiguration<ArticleTicketReference>
{
    public void Configure(EntityTypeBuilder<ArticleTicketReference> builder)
    {
        builder.ToTable("ArticleTicketReferences");

        builder.<PERSON><PERSON>ey(t => t.Id);

        builder.Property(t => t.TicketId)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(t => t.TicketSystem)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(t => t.TicketUrl)
            .HasMaxLength(500);

        builder.Property(t => t.CreatedDate)
            .HasDefaultValueSql("GETUTCDATE()");

        builder.HasOne(t => t.Article)
            .WithMany(a => a.TicketReferences)
            .HasForeignKey(t => t.ArticleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(t => new { t.ArticleId, t.TicketId, t.TicketSystem })
            .IsUnique();
    }
}

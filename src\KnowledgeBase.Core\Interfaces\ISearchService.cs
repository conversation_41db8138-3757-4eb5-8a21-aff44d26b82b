using KnowledgeBase.Core.Entities;

namespace KnowledgeBase.Core.Interfaces;

/// <summary>
/// Service interface for full-text search operations.
/// </summary>
public interface ISearchService
{
    /// <summary>
    /// Search articles with full-text search.
    /// </summary>
    Task<(List<ArticleSearchResult> Items, int TotalCount)> SearchAsync(SearchQuery query, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get search suggestions based on partial input.
    /// </summary>
    Task<List<string>> GetSuggestionsAsync(string query, int maxResults = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Index an article for search.
    /// </summary>
    Task IndexArticleAsync(Article article, CancellationToken cancellationToken = default);

    /// <summary>
    /// Index multiple articles for search.
    /// </summary>
    Task IndexArticlesAsync(IEnumerable<Article> articles, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove an article from the search index.
    /// </summary>
    Task RemoveFromIndexAsync(Guid articleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create or update the search index schema.
    /// </summary>
    Task EnsureIndexExistsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Search query parameters.
/// </summary>
public class SearchQuery
{
    public string Query { get; set; } = string.Empty;
    public string? ArticleType { get; set; }
    public Guid? ModuleId { get; set; }
    public string? Category { get; set; }
    public string Status { get; set; } = "published";
    public List<string>? Tags { get; set; }
    public string SortBy { get; set; } = "relevance";
    public bool SortDescending { get; set; } = true;
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

/// <summary>
/// Search result with relevance ranking.
/// </summary>
public class ArticleSearchResult
{
    public Guid ArticleId { get; set; }
    public string ArticleType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public string ModuleName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();
    public DateTime LastModifiedDate { get; set; }
    public int ViewCount { get; set; }
    public decimal? Rating { get; set; }
    public int SearchRank { get; set; }
}

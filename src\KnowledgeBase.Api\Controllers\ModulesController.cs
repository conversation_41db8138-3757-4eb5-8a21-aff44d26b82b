using KnowledgeBase.Api.Models.DTOs;
using KnowledgeBase.Api.Models.Responses;
using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace KnowledgeBase.Api.Controllers;

/// <summary>
/// API endpoints for managing modules.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ModulesController : ControllerBase
{
    private readonly IModuleService _moduleService;
    private readonly ILogger<ModulesController> _logger;

    public ModulesController(IModuleService moduleService, ILogger<ModulesController> logger)
    {
        _moduleService = moduleService;
        _logger = logger;
    }

    /// <summary>
    /// Get all modules.
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<List<ModuleDto>>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<List<ModuleDto>>>> GetModules(
        [FromQuery] bool includeInactive = false,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting all modules (includeInactive: {IncludeInactive})", includeInactive);

        var modules = await _moduleService.GetAllAsync(includeInactive, cancellationToken);
        var dtos = new List<ModuleDto>();

        foreach (var module in modules)
        {
            var dto = await MapToDto(module, cancellationToken);
            dtos.Add(dto);
        }

        return Ok(ApiResponse<List<ModuleDto>>.Ok(dtos));
    }

    /// <summary>
    /// Get a single module by ID.
    /// </summary>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(ApiResponse<ModuleDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<ModuleDto>>> GetModule(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting module {ModuleId}", id);

        var module = await _moduleService.GetByIdAsync(id, cancellationToken);
        if (module == null)
        {
            return NotFound(ApiResponse<ModuleDto>.Fail("Module not found."));
        }

        var dto = await MapToDto(module, cancellationToken);
        return Ok(ApiResponse<ModuleDto>.Ok(dto));
    }

    /// <summary>
    /// Create a new module (admin only).
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<ModuleDto>), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<ModuleDto>>> CreateModule(
        [FromBody] CreateModuleRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating module {ModuleName}", request.Name);

        if (string.IsNullOrWhiteSpace(request.Name))
        {
            return BadRequest(ApiResponse<ModuleDto>.Fail("Module name is required."));
        }

        var module = new Module
        {
            Name = request.Name,
            Description = request.Description,
            SMEUserId = request.SMEUserId,
            ParentModuleId = request.ParentModuleId,
            DisplayOrder = request.DisplayOrder
        };

        var created = await _moduleService.CreateAsync(module, cancellationToken);
        var dto = await MapToDto(created, cancellationToken);

        return CreatedAtAction(nameof(GetModule), new { id = dto.ModuleId }, ApiResponse<ModuleDto>.Ok(dto));
    }

    /// <summary>
    /// Update a module (admin only).
    /// </summary>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(typeof(ApiResponse<ModuleDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<ModuleDto>>> UpdateModule(
        Guid id,
        [FromBody] UpdateModuleRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Updating module {ModuleId}", id);

        var existing = await _moduleService.GetByIdAsync(id, cancellationToken);
        if (existing == null)
        {
            return NotFound(ApiResponse<ModuleDto>.Fail("Module not found."));
        }

        existing.Name = request.Name;
        existing.Description = request.Description;
        existing.SMEUserId = request.SMEUserId;
        existing.ParentModuleId = request.ParentModuleId;
        existing.IsActive = request.IsActive;
        existing.DisplayOrder = request.DisplayOrder;

        var updated = await _moduleService.UpdateAsync(existing, cancellationToken);
        var dto = await MapToDto(updated, cancellationToken);

        return Ok(ApiResponse<ModuleDto>.Ok(dto));
    }

    /// <summary>
    /// Toggle module active status.
    /// </summary>
    [HttpPatch("{id:guid}/status")]
    [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<bool>>> SetActiveStatus(
        Guid id,
        [FromBody] SetActiveStatusRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Setting module {ModuleId} active status to {IsActive}", id, request.IsActive);

        var result = await _moduleService.SetActiveStatusAsync(id, request.IsActive, cancellationToken);
        if (!result)
        {
            return NotFound(ApiResponse<bool>.Fail("Module not found."));
        }

        return Ok(ApiResponse<bool>.Ok(true));
    }

    private async Task<ModuleDto> MapToDto(Module module, CancellationToken cancellationToken)
    {
        var articleCount = await _moduleService.GetArticleCountAsync(module.ModuleId, cancellationToken);

        return new ModuleDto
        {
            ModuleId = module.ModuleId,
            Name = module.Name,
            Description = module.Description,
            SMEName = module.SME?.Name,
            SMEUserId = module.SMEUserId,
            ParentModuleId = module.ParentModuleId,
            ParentModuleName = module.ParentModule?.Name,
            IsActive = module.IsActive,
            DisplayOrder = module.DisplayOrder,
            ArticleCount = articleCount
        };
    }
}

public class CreateModuleRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public Guid? SMEUserId { get; set; }
    public Guid? ParentModuleId { get; set; }
    public int DisplayOrder { get; set; }
}

public class UpdateModuleRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public Guid? SMEUserId { get; set; }
    public Guid? ParentModuleId { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
}

public class SetActiveStatusRequest
{
    public bool IsActive { get; set; }
}

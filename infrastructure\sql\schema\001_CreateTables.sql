-- Knowledge Base Platform Database Schema
-- Version: 1.0.0
-- Created: 2026-01-29

-- =============================================
-- Users Table
-- =============================================
CREATE TABLE Users (
    UserId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Email NVARCHAR(255) NOT NULL,
    Name NVARCHAR(200) NOT NULL,
    Role VARCHAR(20) NOT NULL DEFAULT 'user', -- 'guest', 'user', 'power_user', 'admin'
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastLoginDate DATETIME2 NULL,

    CONSTRAINT UQ_Users_Email UNIQUE (Email),
    CONSTRAINT CK_Users_Role CHECK (Role IN ('guest', 'user', 'power_user', 'admin'))
);

-- =============================================
-- Modules Table (Subject Areas)
-- =============================================
CREATE TABLE Modules (
    ModuleId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500) NULL,
    SMEUserId UNIQUEIDENTIFIER NULL, -- Subject Matter Expert
    ParentModuleId UNIQUEIDENTIFIER NULL, -- For hierarchical modules
    IsActive BIT NOT NULL DEFAULT 1,
    DisplayOrder INT NULL DEFAULT 0,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),

    CONSTRAINT FK_Modules_SME FOREIGN KEY (SMEUserId) REFERENCES Users(UserId),
    CONSTRAINT FK_Modules_Parent FOREIGN KEY (ParentModuleId) REFERENCES Modules(ModuleId)
);

-- =============================================
-- Articles Table (Core entity)
-- =============================================
CREATE TABLE Articles (
    ArticleId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ArticleType VARCHAR(50) NOT NULL, -- 'how_to', 'troubleshooting', 'release_note', 'faq', 'product_overview', 'best_practice'
    Title NVARCHAR(200) NOT NULL,
    Summary NVARCHAR(500) NOT NULL,
    Content NVARCHAR(MAX) NOT NULL, -- JSON structure varies by article type

    -- Categorization
    ModuleId UNIQUEIDENTIFIER NOT NULL,
    Category VARCHAR(50) NOT NULL, -- 'configuration', 'error_resolution', 'release_notes', 'faq', 'product', 'guidance'
    Tags NVARCHAR(500) NULL, -- JSON array: ["tag1", "tag2"]

    -- Metadata
    EstimatedTimeMinutes INT NULL,
    Difficulty VARCHAR(20) NULL, -- 'beginner', 'intermediate', 'advanced'
    AppliesTo NVARCHAR(200) NULL, -- JSON array of versions: ["3.0", "3.1"]

    -- Status & Workflow
    Status VARCHAR(20) NOT NULL DEFAULT 'draft', -- 'draft', 'in_review', 'published', 'archived'

    -- Ownership & Dates
    CreatedByUserId UNIQUEIDENTIFIER NOT NULL,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastModifiedByUserId UNIQUEIDENTIFIER NOT NULL,
    LastModifiedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastReviewedDate DATETIME2 NULL,
    LastReviewedByUserId UNIQUEIDENTIFIER NULL,

    -- Engagement Metrics
    ViewCount INT NOT NULL DEFAULT 0,
    HelpfulCount INT NOT NULL DEFAULT 0,
    NotHelpfulCount INT NOT NULL DEFAULT 0,

    -- Versioning
    Version INT NOT NULL DEFAULT 1,

    -- Soft Delete
    IsDeleted BIT NOT NULL DEFAULT 0,
    DeletedDate DATETIME2 NULL,
    DeletedByUserId UNIQUEIDENTIFIER NULL,

    CONSTRAINT FK_Articles_Module FOREIGN KEY (ModuleId) REFERENCES Modules(ModuleId),
    CONSTRAINT FK_Articles_CreatedBy FOREIGN KEY (CreatedByUserId) REFERENCES Users(UserId),
    CONSTRAINT FK_Articles_LastModifiedBy FOREIGN KEY (LastModifiedByUserId) REFERENCES Users(UserId),
    CONSTRAINT FK_Articles_LastReviewedBy FOREIGN KEY (LastReviewedByUserId) REFERENCES Users(UserId),
    CONSTRAINT FK_Articles_DeletedBy FOREIGN KEY (DeletedByUserId) REFERENCES Users(UserId),
    CONSTRAINT CK_Articles_Type CHECK (ArticleType IN ('how_to', 'troubleshooting', 'release_note', 'faq', 'product_overview', 'best_practice')),
    CONSTRAINT CK_Articles_Status CHECK (Status IN ('draft', 'in_review', 'published', 'archived')),
    CONSTRAINT CK_Articles_Difficulty CHECK (Difficulty IS NULL OR Difficulty IN ('beginner', 'intermediate', 'advanced'))
);

-- =============================================
-- Article Ticket References (Many-to-Many)
-- =============================================
CREATE TABLE ArticleTicketReferences (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ArticleId UNIQUEIDENTIFIER NOT NULL,
    TicketId NVARCHAR(50) NOT NULL, -- External ticket ID (e.g., "JIRA-1234")
    TicketSystem VARCHAR(50) NOT NULL, -- 'jira', 'azure_devops', 'servicenow', 'zendesk'
    TicketUrl NVARCHAR(500) NULL,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),

    CONSTRAINT FK_ArticleTickets_Article FOREIGN KEY (ArticleId) REFERENCES Articles(ArticleId) ON DELETE CASCADE,
    CONSTRAINT UQ_ArticleTickets UNIQUE (ArticleId, TicketId, TicketSystem)
);

-- =============================================
-- Related Articles (Many-to-Many, Self-Referential)
-- =============================================
CREATE TABLE RelatedArticles (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ArticleId UNIQUEIDENTIFIER NOT NULL,
    RelatedArticleId UNIQUEIDENTIFIER NOT NULL,
    RelationshipType VARCHAR(50) NULL DEFAULT 'related', -- 'prerequisite', 'follow_up', 'alternative', 'related'
    RelevanceScore DECIMAL(3,2) NULL, -- 0.00-1.00 from AI
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedByUserId UNIQUEIDENTIFIER NULL,

    CONSTRAINT FK_RelatedArticles_Article FOREIGN KEY (ArticleId) REFERENCES Articles(ArticleId) ON DELETE CASCADE,
    CONSTRAINT FK_RelatedArticles_Related FOREIGN KEY (RelatedArticleId) REFERENCES Articles(ArticleId),
    CONSTRAINT FK_RelatedArticles_CreatedBy FOREIGN KEY (CreatedByUserId) REFERENCES Users(UserId),
    CONSTRAINT CK_RelatedArticles_NotSelf CHECK (ArticleId != RelatedArticleId),
    CONSTRAINT UQ_RelatedArticles UNIQUE (ArticleId, RelatedArticleId)
);

-- =============================================
-- Article Versions (Full History)
-- =============================================
CREATE TABLE ArticleVersions (
    VersionId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ArticleId UNIQUEIDENTIFIER NOT NULL,
    VersionNumber INT NOT NULL,
    Title NVARCHAR(200) NOT NULL,
    Summary NVARCHAR(500) NOT NULL,
    Content NVARCHAR(MAX) NOT NULL, -- Full article JSON snapshot
    ChangeDescription NVARCHAR(1000) NULL,
    ModifiedByUserId UNIQUEIDENTIFIER NOT NULL,
    ModifiedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),

    CONSTRAINT FK_ArticleVersions_Article FOREIGN KEY (ArticleId) REFERENCES Articles(ArticleId) ON DELETE CASCADE,
    CONSTRAINT FK_ArticleVersions_User FOREIGN KEY (ModifiedByUserId) REFERENCES Users(UserId),
    CONSTRAINT UQ_ArticleVersions UNIQUE (ArticleId, VersionNumber)
);

-- =============================================
-- Article Reviews (Workflow)
-- =============================================
CREATE TABLE ArticleReviews (
    ReviewId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ArticleId UNIQUEIDENTIFIER NOT NULL,
    ReviewerUserId UNIQUEIDENTIFIER NOT NULL,
    ReviewDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    Status VARCHAR(20) NOT NULL, -- 'approved', 'rejected', 'changes_requested'
    Comments NVARCHAR(2000) NULL,

    CONSTRAINT FK_ArticleReviews_Article FOREIGN KEY (ArticleId) REFERENCES Articles(ArticleId) ON DELETE CASCADE,
    CONSTRAINT FK_ArticleReviews_Reviewer FOREIGN KEY (ReviewerUserId) REFERENCES Users(UserId),
    CONSTRAINT CK_ArticleReviews_Status CHECK (Status IN ('approved', 'rejected', 'changes_requested'))
);

-- =============================================
-- Article Feedback (User Ratings)
-- =============================================
CREATE TABLE ArticleFeedback (
    FeedbackId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ArticleId UNIQUEIDENTIFIER NOT NULL,
    UserId UNIQUEIDENTIFIER NULL, -- NULL for anonymous feedback
    FeedbackType VARCHAR(20) NOT NULL, -- 'helpful', 'not_helpful'
    Comments NVARCHAR(1000) NULL,
    FeedbackDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    SessionId NVARCHAR(100) NULL, -- For anonymous tracking

    CONSTRAINT FK_ArticleFeedback_Article FOREIGN KEY (ArticleId) REFERENCES Articles(ArticleId) ON DELETE CASCADE,
    CONSTRAINT FK_ArticleFeedback_User FOREIGN KEY (UserId) REFERENCES Users(UserId),
    CONSTRAINT CK_ArticleFeedback_Type CHECK (FeedbackType IN ('helpful', 'not_helpful'))
);

-- =============================================
-- Audit Log (for tracking changes)
-- =============================================
CREATE TABLE AuditLog (
    AuditId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    EntityType VARCHAR(50) NOT NULL,
    EntityId UNIQUEIDENTIFIER NOT NULL,
    Action VARCHAR(20) NOT NULL, -- 'create', 'update', 'delete', 'publish', 'archive'
    OldValues NVARCHAR(MAX) NULL, -- JSON of old values
    NewValues NVARCHAR(MAX) NULL, -- JSON of new values
    UserId UNIQUEIDENTIFIER NULL,
    Timestamp DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    IpAddress NVARCHAR(50) NULL,
    UserAgent NVARCHAR(500) NULL
);

GO

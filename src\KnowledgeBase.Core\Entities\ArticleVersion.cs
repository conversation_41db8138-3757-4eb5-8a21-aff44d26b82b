using System.ComponentModel.DataAnnotations;

namespace KnowledgeBase.Core.Entities;

/// <summary>
/// Represents a historical version of an article.
/// </summary>
public class ArticleVersion
{
    [Key]
    public Guid VersionId { get; set; }

    public Guid ArticleId { get; set; }

    public int VersionNumber { get; set; }

    [Required]
    [MaxLength(200)]
    public string Title { get; set; } = string.Empty;

    [Required]
    [MaxLength(500)]
    public string Summary { get; set; } = string.Empty;

    /// <summary>
    /// Full JSON snapshot of the article content.
    /// </summary>
    [Required]
    public string Content { get; set; } = "{}";

    [MaxLength(1000)]
    public string? ChangeDescription { get; set; }

    public Guid ModifiedByUserId { get; set; }

    public DateTime ModifiedDate { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual Article Article { get; set; } = null!;
    public virtual User ModifiedBy { get; set; } = null!;
}

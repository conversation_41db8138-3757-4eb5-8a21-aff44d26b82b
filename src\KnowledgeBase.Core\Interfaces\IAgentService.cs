using KnowledgeBase.Core.Enums;

namespace KnowledgeBase.Core.Interfaces;

/// <summary>
/// Base interface for all AI agent services.
/// </summary>
public interface IAgentService
{
    /// <summary>
    /// The type of article this agent specializes in.
    /// </summary>
    ArticleType ArticleType { get; }

    /// <summary>
    /// Generate a structured article from user input.
    /// </summary>
    Task<AgentArticleResult> GenerateAsync(string userInput, CancellationToken cancellationToken = default);
}

/// <summary>
/// Router agent for classifying content type.
/// </summary>
public interface IRouterAgentService
{
    Task<ClassificationResult> ClassifyAsync(string userInput, CancellationToken cancellationToken = default);
}

/// <summary>
/// Evaluation agent for assessing article quality.
/// </summary>
public interface IEvaluationAgentService
{
    Task<EvaluationResult> EvaluateAsync(string title, string summary, string content, ArticleType articleType, CancellationToken cancellationToken = default);
}

/// <summary>
/// Relationship agent for finding related articles.
/// </summary>
public interface IRelationshipAgentService
{
    Task<List<RelatedArticleResult>> FindRelatedAsync(string title, string summary, string content, CancellationToken cancellationToken = default);
}

/// <summary>
/// Result from content classification.
/// </summary>
public class ClassificationResult
{
    public ArticleType ArticleType { get; set; }
    public decimal Confidence { get; set; }
    public string Reasoning { get; set; } = string.Empty;
    public bool NeedsClarification => Confidence < 0.7m;
}

/// <summary>
/// Result from article generation.
/// </summary>
public class AgentArticleResult
{
    public bool Success { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public string ContentJson { get; set; } = "{}";
    public List<string> Tags { get; set; } = new();
    public string? SuggestedCategory { get; set; }
    public int? EstimatedTimeMinutes { get; set; }
    public Difficulty? Difficulty { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Result from article evaluation.
/// </summary>
public class EvaluationResult
{
    public decimal OverallScore { get; set; }
    public Dictionary<string, decimal> Scores { get; set; } = new()
    {
        ["completeness"] = 0,
        ["clarity"] = 0,
        ["accuracy"] = 0,
        ["actionability"] = 0,
        ["structure"] = 0,
        ["seo"] = 0
    };
    public List<string> Strengths { get; set; } = new();
    public List<EvaluationIssue> Issues { get; set; } = new();
    public List<string> RecommendedChanges { get; set; } = new();
    public string ReadabilityLevel { get; set; } = "intermediate";
    public int EstimatedReadTimeMinutes { get; set; }
}

/// <summary>
/// An issue identified during evaluation.
/// </summary>
public class EvaluationIssue
{
    public string Severity { get; set; } = "minor"; // critical, major, minor
    public string Category { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Suggestion { get; set; } = string.Empty;
}

/// <summary>
/// Related article found by the relationship agent.
/// </summary>
public class RelatedArticleResult
{
    public Guid ArticleId { get; set; }
    public string Title { get; set; } = string.Empty;
    public decimal RelevanceScore { get; set; }
    public string RelationshipType { get; set; } = "related";
    public string Explanation { get; set; } = string.Empty;
}

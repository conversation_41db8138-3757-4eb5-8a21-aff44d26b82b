using System.ComponentModel.DataAnnotations;

namespace KnowledgeBase.Core.Entities;

/// <summary>
/// Represents an audit log entry for tracking changes.
/// </summary>
public class AuditLog
{
    [Key]
    public Guid AuditId { get; set; }

    [Required]
    [MaxLength(50)]
    public string EntityType { get; set; } = string.Empty;

    public Guid EntityId { get; set; }

    [Required]
    [MaxLength(20)]
    public string Action { get; set; } = string.Empty;

    /// <summary>
    /// JSON of old values before the change.
    /// </summary>
    public string? OldValues { get; set; }

    /// <summary>
    /// JSON of new values after the change.
    /// </summary>
    public string? NewValues { get; set; }

    public Guid? UserId { get; set; }

    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    [MaxLength(50)]
    public string? IpAddress { get; set; }

    [MaxLength(500)]
    public string? UserAgent { get; set; }
}

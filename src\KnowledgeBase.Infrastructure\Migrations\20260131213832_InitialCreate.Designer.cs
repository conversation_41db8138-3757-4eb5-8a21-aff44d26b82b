﻿// <auto-generated />
using System;
using KnowledgeBase.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace KnowledgeBase.Infrastructure.Migrations
{
    [DbContext(typeof(KnowledgeBaseContext))]
    [Migration("20260131213832_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.12")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("KnowledgeBase.Core.Entities.Article", b =>
                {
                    b.Property<Guid>("ArticleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AppliesTo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ArticleType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<Guid?>("DeletedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Difficulty")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int?>("EstimatedTimeMinutes")
                        .HasColumnType("int");

                    b.Property<int>("HelpfulCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<Guid>("LastModifiedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("LastModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<Guid?>("LastReviewedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastReviewedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("ModuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("NotHelpfulCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasDefaultValue("Draft");

                    b.Property<string>("Summary")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Tags")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("Version")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("ViewCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.HasKey("ArticleId");

                    b.HasIndex("ArticleType");

                    b.HasIndex("Category");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("DeletedByUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.HasIndex("LastModifiedDate");

                    b.HasIndex("LastReviewedByUserId");

                    b.HasIndex("ModuleId");

                    b.HasIndex("Status");

                    b.HasIndex("Status", "ModuleId", "ArticleType");

                    b.ToTable("Articles", (string)null);
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.ArticleFeedback", b =>
                {
                    b.Property<Guid>("FeedbackId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ArticleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comments")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("FeedbackDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("FeedbackType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("SessionId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("FeedbackId");

                    b.HasIndex("ArticleId");

                    b.HasIndex("UserId");

                    b.ToTable("ArticleFeedback", (string)null);
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.ArticleReview", b =>
                {
                    b.Property<Guid>("ReviewId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ArticleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comments")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<DateTime>("ReviewDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<Guid>("ReviewerUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("ReviewId");

                    b.HasIndex("ReviewerUserId");

                    b.HasIndex("ArticleId", "ReviewDate");

                    b.ToTable("ArticleReviews", (string)null);
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.ArticleTicketReference", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ArticleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("TicketId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("TicketSystem")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("TicketUrl")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("ArticleId", "TicketId", "TicketSystem")
                        .IsUnique();

                    b.ToTable("ArticleTicketReferences", (string)null);
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.ArticleVersion", b =>
                {
                    b.Property<Guid>("VersionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ArticleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ChangeDescription")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("ModifiedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("ModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Summary")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("VersionNumber")
                        .HasColumnType("int");

                    b.HasKey("VersionId");

                    b.HasIndex("ModifiedByUserId");

                    b.HasIndex("ArticleId", "VersionNumber")
                        .IsUnique();

                    b.ToTable("ArticleVersions", (string)null);
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.AuditLog", b =>
                {
                    b.Property<Guid>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("NewValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OldValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Timestamp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("AuditId");

                    b.HasIndex("Timestamp");

                    b.HasIndex("UserId");

                    b.HasIndex("EntityType", "EntityId");

                    b.ToTable("AuditLog", (string)null);
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.Module", b =>
                {
                    b.Property<Guid>("ModuleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ParentModuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SMEUserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("ModuleId");

                    b.HasIndex("ParentModuleId");

                    b.HasIndex("SMEUserId");

                    b.ToTable("Modules", (string)null);
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.RelatedArticle", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ArticleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<Guid>("RelatedArticleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("RelationshipType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasDefaultValue("Related");

                    b.Property<decimal?>("RelevanceScore")
                        .HasPrecision(3, 2)
                        .HasColumnType("decimal(3,2)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("RelatedArticleId");

                    b.HasIndex("ArticleId", "RelatedArticleId")
                        .IsUnique();

                    b.ToTable("RelatedArticles", null, t =>
                        {
                            t.HasCheckConstraint("CK_RelatedArticles_NotSelf", "[ArticleId] != [RelatedArticleId]");
                        });
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.User", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("UserId");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.ToTable("Users", (string)null);
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.Article", b =>
                {
                    b.HasOne("KnowledgeBase.Core.Entities.User", "CreatedBy")
                        .WithMany("CreatedArticles")
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("KnowledgeBase.Core.Entities.User", "DeletedBy")
                        .WithMany()
                        .HasForeignKey("DeletedByUserId");

                    b.HasOne("KnowledgeBase.Core.Entities.User", "LastModifiedBy")
                        .WithMany("ModifiedArticles")
                        .HasForeignKey("LastModifiedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("KnowledgeBase.Core.Entities.User", "LastReviewedBy")
                        .WithMany("ReviewedArticles")
                        .HasForeignKey("LastReviewedByUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("KnowledgeBase.Core.Entities.Module", "Module")
                        .WithMany("Articles")
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("DeletedBy");

                    b.Navigation("LastModifiedBy");

                    b.Navigation("LastReviewedBy");

                    b.Navigation("Module");
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.ArticleFeedback", b =>
                {
                    b.HasOne("KnowledgeBase.Core.Entities.Article", "Article")
                        .WithMany("Feedback")
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("KnowledgeBase.Core.Entities.User", "User")
                        .WithMany("Feedback")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Article");

                    b.Navigation("User");
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.ArticleReview", b =>
                {
                    b.HasOne("KnowledgeBase.Core.Entities.Article", "Article")
                        .WithMany("Reviews")
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("KnowledgeBase.Core.Entities.User", "Reviewer")
                        .WithMany("Reviews")
                        .HasForeignKey("ReviewerUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Article");

                    b.Navigation("Reviewer");
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.ArticleTicketReference", b =>
                {
                    b.HasOne("KnowledgeBase.Core.Entities.Article", "Article")
                        .WithMany("TicketReferences")
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Article");
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.ArticleVersion", b =>
                {
                    b.HasOne("KnowledgeBase.Core.Entities.Article", "Article")
                        .WithMany("Versions")
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("KnowledgeBase.Core.Entities.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Article");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.Module", b =>
                {
                    b.HasOne("KnowledgeBase.Core.Entities.Module", "ParentModule")
                        .WithMany("ChildModules")
                        .HasForeignKey("ParentModuleId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("KnowledgeBase.Core.Entities.User", "SME")
                        .WithMany("SMEModules")
                        .HasForeignKey("SMEUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ParentModule");

                    b.Navigation("SME");
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.RelatedArticle", b =>
                {
                    b.HasOne("KnowledgeBase.Core.Entities.Article", "Article")
                        .WithMany("RelatedArticles")
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("KnowledgeBase.Core.Entities.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId");

                    b.HasOne("KnowledgeBase.Core.Entities.Article", "Related")
                        .WithMany("RelatedTo")
                        .HasForeignKey("RelatedArticleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Article");

                    b.Navigation("CreatedBy");

                    b.Navigation("Related");
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.Article", b =>
                {
                    b.Navigation("Feedback");

                    b.Navigation("RelatedArticles");

                    b.Navigation("RelatedTo");

                    b.Navigation("Reviews");

                    b.Navigation("TicketReferences");

                    b.Navigation("Versions");
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.Module", b =>
                {
                    b.Navigation("Articles");

                    b.Navigation("ChildModules");
                });

            modelBuilder.Entity("KnowledgeBase.Core.Entities.User", b =>
                {
                    b.Navigation("CreatedArticles");

                    b.Navigation("Feedback");

                    b.Navigation("ModifiedArticles");

                    b.Navigation("ReviewedArticles");

                    b.Navigation("Reviews");

                    b.Navigation("SMEModules");
                });
#pragma warning restore 612, 618
        }
    }
}

# Knowledge Base Article Types - Definitions, Examples & Data Model

## Article Type Taxonomy

Based on your requirements and common knowledge base patterns, here are the recommended article types:

---

## 1. HOW-TO / TUTORIAL

**Purpose**: Step-by-step instructions for completing a specific task

**When to Use**: 
- User wants to accomplish something
- Process has multiple steps
- Clear beginning and end

**Key Characteristics**:
- Numbered steps
- Prerequisites
- Expected outcomes
- Time estimate
- Difficulty level

### Example 1: How-To Article

**Raw Input** (what user pastes):
```
To set up single sign-on in the admin portal, first you need admin credentials. 
Log into the admin portal at admin.example.com, then go to Settings > Security. 
Click on "SSO Configuration" and choose your identity provider (we support Okta, 
Azure AD, and Google Workspace). For Azure AD, you'll need your tenant ID and 
client secret. Enter those values, then click "Test Connection" to verify. 
Once verified, enable SSO for your organization. Users will be redirected to 
your identity provider on next login.
```

**Generated Article Output**:
```json
{
  "article_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "article_type": "how_to",
  "title": "How to Configure Single Sign-On (SSO) in Admin Portal",
  "summary": "Enable SSO authentication for your organization using Azure AD, Okta, or Google Workspace in under 10 minutes.",
  "content": {
    "prerequisites": [
      {
        "item": "Admin-level access to the platform",
        "type": "permission"
      },
      {
        "item": "Identity provider credentials (Tenant ID and Client Secret for Azure AD)",
        "type": "credential"
      },
      {
        "item": "Access to your identity provider admin console",
        "type": "access"
      }
    ],
    "steps": [
      {
        "step_number": 1,
        "title": "Access the Admin Portal",
        "action": "Navigate to admin.example.com and log in with your admin credentials",
        "expected_outcome": "You should see the admin dashboard",
        "screenshot_recommended": true,
        "warning": null
      },
      {
        "step_number": 2,
        "title": "Navigate to Security Settings",
        "action": "Click on Settings in the left sidebar, then select the Security tab",
        "expected_outcome": "Security configuration options are displayed",
        "screenshot_recommended": true,
        "warning": null
      },
      {
        "step_number": 3,
        "title": "Open SSO Configuration",
        "action": "Click on 'SSO Configuration' button",
        "expected_outcome": "SSO setup wizard opens",
        "screenshot_recommended": true,
        "warning": null
      },
      {
        "step_number": 4,
        "title": "Select Identity Provider",
        "action": "Choose your identity provider from the dropdown: Azure AD, Okta, or Google Workspace",
        "expected_outcome": "Provider-specific configuration fields appear",
        "screenshot_recommended": true,
        "warning": null
      },
      {
        "step_number": 5,
        "title": "Enter Provider Credentials",
        "action": "For Azure AD: Enter your Tenant ID in the first field and Client Secret in the second field",
        "expected_outcome": "Credential fields are populated",
        "screenshot_recommended": true,
        "warning": "Keep your Client Secret secure - do not share it"
      },
      {
        "step_number": 6,
        "title": "Test the Connection",
        "action": "Click 'Test Connection' button to verify the configuration",
        "expected_outcome": "Green checkmark appears with message 'Connection successful'",
        "screenshot_recommended": true,
        "warning": "If test fails, double-check your credentials and ensure your IP is whitelisted in Azure AD"
      },
      {
        "step_number": 7,
        "title": "Enable SSO",
        "action": "Toggle the 'Enable SSO for Organization' switch to ON, then click 'Save Changes'",
        "expected_outcome": "Confirmation message: 'SSO has been enabled'",
        "screenshot_recommended": true,
        "warning": "All users will be required to use SSO on their next login"
      }
    ],
    "validation": {
      "title": "Verify SSO is Working",
      "steps": [
        "Log out of your current session",
        "Navigate to the login page",
        "You should be automatically redirected to your identity provider",
        "After authenticating, you should be logged into the platform"
      ]
    },
    "troubleshooting": [
      {
        "issue": "Test connection fails",
        "solution": "Verify your Tenant ID and Client Secret are correct. Check that your IP address is whitelisted in your identity provider."
      },
      {
        "issue": "Users not redirected to identity provider",
        "solution": "Clear browser cache and cookies, then try again. Ensure SSO is enabled in step 7."
      }
    ],
    "next_steps": [
      "Configure user provisioning (SCIM)",
      "Set up multi-factor authentication (MFA)",
      "Create SSO troubleshooting guide for end users"
    ]
  },
  "metadata": {
    "estimated_time_minutes": 10,
    "difficulty": "intermediate",
    "last_tested_date": "2026-01-15",
    "applies_to_versions": ["3.0", "3.1", "3.2"]
  },
  "module_id": "auth-module-guid",
  "tags": ["sso", "authentication", "azure-ad", "okta", "security", "admin"],
  "category": "configuration",
  "related_article_ids": ["azure-ad-setup-guid", "user-provisioning-guid"],
  "ticket_references": ["JIRA-1234", "SUPPORT-5678"],
  "created_by_user_id": "user-guid",
  "created_date": "2026-01-29T10:30:00Z",
  "status": "published",
  "version": 1
}
```

---

## 2. TROUBLESHOOTING / PROBLEM-SOLUTION

**Purpose**: Help users diagnose and fix issues

**When to Use**:
- Something is broken or not working
- Users report errors
- Need to guide diagnosis process

**Key Characteristics**:
- Symptoms description
- Possible causes (ranked by likelihood)
- Diagnostic steps
- Solutions for each cause
- Escalation path

### Example 2: Troubleshooting Article

**Raw Input** (Slack conversation):
```
User: Hey team, customers are reporting they can't upload files larger than 5MB. 
Getting error "Upload failed - file too large"

Dev: What browser are they using?

User: Mostly Chrome, some Firefox. Started after yesterday's deployment.

Dev: Ah, looks like the IIS upload limit wasn't updated in the deployment. 
Default is 4MB. Need to update web.config maxRequestLength and maxAllowedContentLength.

User: How do we fix for customers now?

Dev: They can compress files as workaround. We'll deploy the fix in 2 hours. 
Also need to update our deployment checklist to prevent this.
```

**Generated Article Output**:
```json
{
  "article_id": "b2c3d4e5-f6a7-8901-bcde-f12345678901",
  "article_type": "troubleshooting",
  "title": "File Upload Fails - \"Upload Failed - File Too Large\" Error",
  "summary": "Users unable to upload files larger than 5MB after recent deployment due to server upload limit misconfiguration.",
  "content": {
    "symptoms": [
      "Error message: 'Upload failed - file too large'",
      "Affects files larger than 4-5MB",
      "Occurs across all browsers (Chrome, Firefox, Safari)",
      "Started immediately after version 3.1.5 deployment",
      "Smaller files upload successfully"
    ],
    "affected_versions": ["3.1.5"],
    "severity": "high",
    "impact": "Users cannot upload files >5MB, blocking critical workflows",
    "root_causes": [
      {
        "cause": "IIS upload size limit not updated during deployment",
        "likelihood": "confirmed",
        "technical_details": "web.config maxRequestLength (4MB) and maxAllowedContentLength (4MB) not increased to match application requirements (50MB)",
        "diagnostic_steps": [
          "Check application logs for 'Request Entity Too Large' errors",
          "Verify web.config settings: maxRequestLength and maxAllowedContentLength",
          "Test upload with file exactly 5MB to confirm size threshold"
        ],
        "solution": {
          "type": "configuration_change",
          "target": "server_side",
          "steps": [
            {
              "step": 1,
              "action": "Open web.config file on the application server",
              "command": null
            },
            {
              "step": 2,
              "action": "Locate <system.web> section and update maxRequestLength",
              "command": "<httpRuntime maxRequestLength=\"51200\" executionTimeout=\"3600\" />"
            },
            {
              "step": 3,
              "action": "Locate <system.webServer> section and update maxAllowedContentLength",
              "command": "<requestLimits maxAllowedContentLength=\"52428800\" />"
            },
            {
              "step": 4,
              "action": "Restart IIS application pool",
              "command": "appcmd recycle apppool /apppool.name:YourAppPoolName"
            },
            {
              "step": 5,
              "action": "Test upload with 10MB file to verify fix",
              "command": null
            }
          ],
          "time_to_resolve": "15 minutes",
          "requires_downtime": false
        }
      },
      {
        "cause": "Client-side file size validation too restrictive",
        "likelihood": "low",
        "technical_details": "JavaScript validation may have incorrect file size limit",
        "diagnostic_steps": [
          "Check browser console for JavaScript errors",
          "Review client-side validation code for file size checks",
          "Test with validation disabled"
        ],
        "solution": {
          "type": "code_change",
          "target": "client_side",
          "steps": [
            {
              "step": 1,
              "action": "Review upload component validation logic",
              "command": null
            },
            {
              "step": 2,
              "action": "Update MAX_FILE_SIZE constant to 52428800 (50MB)",
              "command": "const MAX_FILE_SIZE = 52428800;"
            },
            {
              "step": 3,
              "action": "Deploy updated client code",
              "command": null
            }
          ],
          "time_to_resolve": "1 hour",
          "requires_downtime": false
        }
      }
    ],
    "workarounds": [
      {
        "description": "Compress files before uploading",
        "effectiveness": "temporary",
        "instructions": "Use file compression tools (e.g., WinZip, 7-Zip) to reduce file size below 5MB threshold"
      },
      {
        "description": "Split large files into smaller chunks",
        "effectiveness": "temporary",
        "instructions": "If possible, break content into multiple smaller files"
      }
    ],
    "prevention": [
      "Add upload limit configuration to deployment checklist",
      "Include upload size validation in integration tests",
      "Document server configuration requirements in deployment guide",
      "Set up monitoring alert for upload failures spike"
    ],
    "escalation": {
      "when": "If solutions above don't resolve the issue within 30 minutes",
      "contact": "DevOps team via #ops-support Slack <NAME_EMAIL>",
      "priority": "P1 - High Priority"
    }
  },
  "metadata": {
    "resolution_time_minutes": 15,
    "difficulty": "intermediate",
    "last_verified_date": "2026-01-29"
  },
  "module_id": "file-management-module-guid",
  "tags": ["file-upload", "error", "deployment", "iis", "web-config", "configuration"],
  "category": "error_resolution",
  "related_article_ids": ["iis-configuration-guid", "deployment-checklist-guid"],
  "ticket_references": ["INCIDENT-9876", "JIRA-4567"],
  "created_by_user_id": "user-guid",
  "created_date": "2026-01-29T14:22:00Z",
  "status": "published",
  "version": 1
}
```

---

## 3. RELEASE NOTE

**Purpose**: Communicate product changes, updates, and new features

**When to Use**:
- New version deployed
- Features added/changed
- Bugs fixed
- Breaking changes

**Key Characteristics**:
- Version number
- Release date
- Changes grouped by type
- Impact assessment
- Migration guidance

### Example 3: Release Note Article

**Raw Input**:
```
Version 3.2.0 released January 15, 2026. 

New features:
- Dark mode support across all pages
- Bulk user import via CSV
- Advanced search filters (date range, author, tags)
- Real-time collaboration on documents

Improvements:
- Dashboard loads 40% faster
- File upload now supports up to 50MB (was 10MB)
- Email notifications now include direct links to articles

Bug fixes:
- Fixed: Search results not highlighting keywords
- Fixed: Mobile navigation menu overlapping content
- Fixed: Export to PDF cutting off long tables

Breaking changes:
- API v1 deprecated, please migrate to API v2 by March 1
- Old authentication tokens will expire, users must re-login
```

**Generated Article Output**:
```json
{
  "article_id": "c3d4e5f6-a7b8-9012-cdef-123456789012",
  "article_type": "release_note",
  "title": "Release Notes - Version 3.2.0",
  "summary": "Version 3.2.0 introduces dark mode, bulk user import, advanced search, and real-time collaboration, plus performance improvements and critical bug fixes.",
  "content": {
    "version": "3.2.0",
    "release_date": "2026-01-15",
    "release_type": "minor",
    "highlights": [
      "Dark mode support for better viewing in low-light environments",
      "Bulk user import saves hours of manual data entry",
      "40% faster dashboard performance",
      "Real-time collaboration enables team productivity"
    ],
    "sections": [
      {
        "type": "new_features",
        "title": "✨ New Features",
        "items": [
          {
            "title": "Dark Mode Support",
            "description": "Toggle between light and dark themes from your profile settings. Dark mode reduces eye strain and saves battery on mobile devices.",
            "impact": "All users can now choose their preferred visual theme",
            "user_action": "Enable in Profile > Appearance > Theme",
            "screenshot_available": true,
            "related_tickets": ["FEATURE-234"],
            "benefits": [
              "Reduced eye strain in low-light environments",
              "Better battery life on OLED screens",
              "Modern, customizable user experience"
            ]
          },
          {
            "title": "Bulk User Import via CSV",
            "description": "Administrators can now import hundreds of users at once by uploading a CSV file. System validates data and provides detailed error reports for any issues.",
            "impact": "Administrators onboarding large teams",
            "user_action": "Go to Admin > Users > Import CSV. Download the template for correct format.",
            "screenshot_available": true,
            "related_tickets": ["FEATURE-189"],
            "benefits": [
              "Import 500+ users in under 5 minutes",
              "Automatic validation prevents data errors",
              "Detailed error reporting for easy correction"
            ]
          },
          {
            "title": "Advanced Search Filters",
            "description": "Search now supports filtering by date range, author, tags, and module. Combine filters for precise results.",
            "impact": "All users searching for specific content",
            "user_action": "Click 'Advanced Filters' on search page",
            "screenshot_available": true,
            "related_tickets": ["FEATURE-201"],
            "benefits": [
              "Find relevant content 3x faster",
              "Filter by multiple criteria simultaneously",
              "Save frequent searches as favorites"
            ]
          },
          {
            "title": "Real-Time Collaboration",
            "description": "Multiple users can now edit documents simultaneously. See who else is viewing/editing in real-time with live cursors and presence indicators.",
            "impact": "Teams working on shared documents",
            "user_action": "Simply open a document - collaboration is automatic",
            "screenshot_available": true,
            "related_tickets": ["FEATURE-156"],
            "benefits": [
              "No more version conflicts",
              "See changes as they happen",
              "Comment and discuss inline"
            ]
          }
        ]
      },
      {
        "type": "improvements",
        "title": "🚀 Improvements",
        "items": [
          {
            "title": "Dashboard Performance: 40% Faster Load Time",
            "description": "Optimized database queries and implemented caching. Dashboard now loads in under 1 second (previously 2-3 seconds).",
            "impact": "All users accessing the dashboard",
            "user_action": "None - improvement is automatic",
            "related_tickets": ["PERF-445"],
            "metrics": {
              "before": "2.5 seconds average",
              "after": "0.9 seconds average",
              "improvement": "64% reduction"
            }
          },
          {
            "title": "File Upload Size Increased to 50MB",
            "description": "Maximum file upload size increased from 10MB to 50MB. Supports larger presentations, videos, and datasets.",
            "impact": "Users uploading large files",
            "user_action": "None - new limit applies automatically",
            "related_tickets": ["IMPROVE-332"]
          },
          {
            "title": "Enhanced Email Notifications",
            "description": "Email notifications now include direct deep links to articles, comments, and mentions. Click to go directly to the relevant content.",
            "impact": "All users receiving email notifications",
            "user_action": "None - applies to all new emails",
            "related_tickets": ["IMPROVE-298"]
          }
        ]
      },
      {
        "type": "bug_fixes",
        "title": "🐛 Bug Fixes",
        "items": [
          {
            "title": "Search Keyword Highlighting Fixed",
            "description": "Search results now correctly highlight matching keywords in yellow. Previously, highlights were not appearing.",
            "impact": "Users relying on search",
            "severity": "medium",
            "related_tickets": ["BUG-789"]
          },
          {
            "title": "Mobile Navigation Menu Fixed",
            "description": "Mobile hamburger menu no longer overlaps page content. Menu now properly slides over content with backdrop.",
            "impact": "Mobile users (30% of user base)",
            "severity": "high",
            "related_tickets": ["BUG-801"]
          },
          {
            "title": "PDF Export Table Rendering Fixed",
            "description": "Long tables in PDF exports no longer cut off mid-page. Tables now properly break across pages.",
            "impact": "Users exporting articles to PDF",
            "severity": "medium",
            "related_tickets": ["BUG-756"]
          }
        ]
      },
      {
        "type": "breaking_changes",
        "title": "⚠️ Breaking Changes & Required Actions",
        "items": [
          {
            "title": "API v1 Deprecated - Migration Required by March 1, 2026",
            "description": "API version 1 is officially deprecated and will be shut down on March 1, 2026. All integrations must migrate to API v2.",
            "impact": "Developers using API v1 endpoints",
            "user_action": "REQUIRED: Update all API calls to v2 endpoints. See migration guide.",
            "deadline": "2026-03-01",
            "migration_guide_url": "https://docs.example.com/api-v2-migration",
            "severity": "critical",
            "related_tickets": ["DEPRECATE-12"],
            "migration_steps": [
              "Audit your codebase for API v1 calls (look for /api/v1/ in URLs)",
              "Review API v2 documentation for endpoint changes",
              "Update authentication to use OAuth 2.0 (v1 used API keys)",
              "Update request/response models (some fields renamed)",
              "Test thoroughly in staging environment",
              "Deploy to production before March 1, 2026"
            ]
          },
          {
            "title": "Authentication Token Refresh - Re-Login Required",
            "description": "Due to security improvements, all existing authentication tokens will expire. Users will be prompted to log in again.",
            "impact": "All users",
            "user_action": "Re-login when prompted (one-time)",
            "severity": "medium",
            "related_tickets": ["SECURITY-445"]
          }
        ]
      }
    ],
    "known_issues": [
      {
        "title": "Excel export timeout for 10,000+ rows",
        "description": "Exporting very large datasets (>10,000 rows) to Excel may timeout. Working on fix for v3.2.1.",
        "workaround": "Export in smaller batches or use CSV export",
        "target_fix_version": "3.2.1",
        "ticket": "BUG-823"
      }
    ],
    "upgrade_instructions": {
      "method": "automatic",
      "estimated_downtime": "None - rolling deployment",
      "rollback_plan": "Automated rollback to v3.1.9 if critical issues detected",
      "post_upgrade_steps": [
        "Clear browser cache (Ctrl+Shift+Delete) for best performance",
        "Re-login to refresh authentication tokens",
        "Review dark mode settings if desired"
      ]
    },
    "compatibility": {
      "minimum_browser_versions": {
        "chrome": "90+",
        "firefox": "88+",
        "safari": "14+",
        "edge": "90+"
      },
      "api_versions_supported": ["v2"],
      "mobile_app_compatibility": "iOS 14+, Android 10+"
    }
  },
  "metadata": {
    "release_manager": "Jane Smith",
    "deployment_date": "2026-01-15",
    "rollout_type": "phased",
    "rollout_percentage": "100%"
  },
  "module_id": "platform-module-guid",
  "tags": ["release", "3.2.0", "dark-mode", "collaboration", "performance", "api-deprecation"],
  "category": "release_notes",
  "related_article_ids": ["api-v2-migration-guid", "dark-mode-setup-guid"],
  "ticket_references": ["RELEASE-320"],
  "created_by_user_id": "user-guid",
  "created_date": "2026-01-15T09:00:00Z",
  "status": "published",
  "version": 1
}
```

---

## 4. FAQ (Frequently Asked Questions)

**Purpose**: Quick answers to common questions

**When to Use**:
- Same questions asked repeatedly
- Need quick reference
- Simple Q&A format works best

**Key Characteristics**:
- Question in user's language
- Concise answer (2-4 sentences)
- Links to detailed articles
- Grouped by topic

### Example 4: FAQ Article

**Raw Input**:
```
Common questions about our pricing:
- What payment methods do you accept? We accept credit cards (Visa, MC, Amex), PayPal, and wire transfer for enterprise.
- Can I change my plan anytime? Yes, upgrade or downgrade anytime. Changes take effect immediately.
- Do you offer refunds? 30-day money-back guarantee, no questions asked.
- Is there a free trial? Yes, 14-day free trial, no credit card required.
- What happens if I exceed my user limit? We'll notify you and you can upgrade or remove users within 7 days.
```

**Generated Article Output**:
```json
{
  "article_id": "d4e5f6a7-b8c9-0123-def0-123456789abc",
  "article_type": "faq",
  "title": "FAQ - Pricing and Billing",
  "summary": "Answers to frequently asked questions about pricing plans, payment methods, upgrades, and refunds.",
  "content": {
    "categories": [
      {
        "category_name": "Payment Methods",
        "questions": [
          {
            "question": "What payment methods do you accept?",
            "answer": "We accept all major credit cards (Visa, Mastercard, American Express), PayPal, and wire transfers for enterprise customers. Payment is processed securely through our PCI-compliant payment processor.",
            "related_articles": ["payment-setup-guid"],
            "tags": ["payment", "billing"]
          }
        ]
      },
      {
        "category_name": "Plan Changes",
        "questions": [
          {
            "question": "Can I change my plan anytime?",
            "answer": "Yes, you can upgrade or downgrade your plan at any time from your account settings. Upgrades take effect immediately, and downgrades take effect at the end of your current billing cycle. You'll only pay the prorated difference.",
            "related_articles": ["plan-upgrade-guide-guid", "plan-downgrade-guide-guid"],
            "tags": ["upgrade", "downgrade", "plan-change"]
          },
          {
            "question": "What happens if I exceed my user limit?",
            "answer": "We'll send you an email notification when you reach 90% of your user limit. If you exceed the limit, you'll have 7 days to either upgrade to a higher plan or remove users. No service interruption during this grace period.",
            "related_articles": ["user-management-guid"],
            "tags": ["user-limit", "overage"]
          }
        ]
      },
      {
        "category_name": "Refunds & Trials",
        "questions": [
          {
            "question": "Do you offer refunds?",
            "answer": "Yes, we offer a 30-day money-back guarantee for all plans. If you're not satisfied for any reason, contact support within 30 days of your purchase for a full refund, no questions asked.",
            "related_articles": ["refund-process-guid"],
            "tags": ["refund", "money-back"]
          },
          {
            "question": "Is there a free trial?",
            "answer": "Yes! All plans include a 14-day free trial with full access to all features. No credit card required to start. You'll only be charged if you choose to continue after the trial ends.",
            "related_articles": ["free-trial-signup-guid"],
            "tags": ["trial", "free"]
          }
        ]
      }
    ]
  },
  "metadata": {
    "last_updated_date": "2026-01-20",
    "review_frequency_days": 30
  },
  "module_id": "billing-module-guid",
  "tags": ["faq", "pricing", "billing", "payment", "refund", "trial"],
  "category": "faq",
  "related_article_ids": ["pricing-overview-guid", "billing-guide-guid"],
  "ticket_references": [],
  "created_by_user_id": "user-guid",
  "created_date": "2026-01-10T11:00:00Z",
  "status": "published",
  "version": 2
}
```

---

## 5. PRODUCT OVERVIEW / FEATURE DESCRIPTION

**Purpose**: Explain what a product/feature is and why it matters

**When to Use**:
- Introducing new products/features
- High-level overview needed
- Marketing/sales enablement

**Key Characteristics**:
- Elevator pitch
- Benefits (not just features)
- Use cases
- Target audience
- Getting started

### Example 5: Product Overview Article

**Raw Input**:
```
Our new Analytics Dashboard gives you real-time insights into user behavior, 
article performance, and search patterns. See which articles are most helpful, 
track user engagement over time, and identify knowledge gaps. Built for 
product managers, content teams, and customer success managers. 

Features include customizable date ranges, exportable reports, drill-down 
capabilities, and scheduled email digests. Integrates with Google Analytics 
and Mixpanel for deeper insights.
```

**Generated Article Output**:
```json
{
  "article_id": "e5f6a7b8-c9d0-1234-ef01-23456789abcd",
  "article_type": "product_overview",
  "title": "Analytics Dashboard - Product Overview",
  "summary": "Gain real-time insights into content performance, user behavior, and knowledge gaps with our comprehensive Analytics Dashboard.",
  "content": {
    "elevator_pitch": "The Analytics Dashboard transforms your knowledge base data into actionable insights, helping you understand what content resonates, where users struggle, and how to improve their experience.",
    "target_audience": [
      {
        "role": "Product Managers",
        "benefit": "Data-driven product decisions based on actual user behavior and content consumption patterns"
      },
      {
        "role": "Content Teams",
        "benefit": "Identify high-performing articles and content gaps to prioritize content creation"
      },
      {
        "role": "Customer Success Managers",
        "benefit": "Proactively address customer issues by spotting trending support topics"
      },
      {
        "role": "Support Teams",
        "benefit": "Measure article effectiveness and reduce support ticket volume"
      }
    ],
    "key_benefits": [
      {
        "benefit": "Make data-driven decisions",
        "description": "Stop guessing what content users need. See exactly what they're searching for, reading, and finding helpful.",
        "metric": "Reduce content creation guesswork by 70%"
      },
      {
        "benefit": "Improve content quality",
        "description": "Identify underperforming articles and knowledge gaps. Focus your efforts where they'll have the biggest impact.",
        "metric": "Increase article helpfulness ratings by 40%"
      },
      {
        "benefit": "Reduce support costs",
        "description": "Track deflection rates and see which articles prevent support tickets. Optimize high-impact content.",
        "metric": "Reduce support ticket volume by 25%"
      },
      {
        "benefit": "Understand user journey",
        "description": "See how users navigate your knowledge base. Identify friction points and optimize information architecture.",
        "metric": "Improve time-to-resolution by 35%"
      }
    ],
    "use_cases": [
      {
        "scenario": "Content Gap Analysis",
        "description": "Your support team notices increasing tickets about a feature, but search shows no matching articles.",
        "solution": "Analytics Dashboard shows 'API authentication' is the #3 search term with zero results. Content team creates targeted articles, reducing related tickets by 40%.",
        "outcome": "Proactive content creation based on actual user needs"
      },
      {
        "scenario": "Article Performance Optimization",
        "description": "You've published 500 articles but don't know which ones are actually helping users.",
        "solution": "Dashboard reveals top 20 articles drive 60% of traffic. These articles have high ratings and low bounce rates. You identify common patterns and apply them to other articles.",
        "outcome": "Systematic improvement of content quality"
      },
      {
        "scenario": "Quarterly Business Review",
        "description": "Leadership wants to see ROI of knowledge base investment.",
        "solution": "Export quarterly report showing: 10,000+ self-service resolutions, $50K in support cost savings, 85% user satisfaction. Scheduled monthly digest keeps stakeholders informed.",
        "outcome": "Clear demonstration of business value"
      }
    ],
    "core_capabilities": [
      {
        "capability": "Real-Time Dashboards",
        "description": "Live metrics update every 5 minutes. See current activity, trending searches, and recent article views.",
        "availability": "All plans"
      },
      {
        "capability": "Article Performance Metrics",
        "description": "Track views, ratings, helpful votes, time-on-page, and bounce rate for every article. Identify winners and losers.",
        "availability": "All plans"
      },
      {
        "capability": "Search Analytics",
        "description": "See what users search for, which queries return zero results, and which articles they click. Identify content gaps.",
        "availability": "All plans"
      },
      {
        "capability": "User Behavior Tracking",
        "description": "Understand user journey: entry points, navigation paths, exit pages. Optimize information architecture.",
        "availability": "Professional and Enterprise plans"
      },
      {
        "capability": "Custom Reports & Export",
        "description": "Create custom reports with your chosen metrics and date ranges. Export to Excel, PDF, or CSV.",
        "availability": "Professional and Enterprise plans"
      },
      {
        "capability": "Scheduled Email Digests",
        "description": "Receive weekly or monthly email summaries of key metrics. Keep stakeholders informed automatically.",
        "availability": "Enterprise plan"
      },
      {
        "capability": "Third-Party Integrations",
        "description": "Connect with Google Analytics, Mixpanel, or Segment for unified analytics across platforms.",
        "availability": "Enterprise plan"
      }
    ],
    "getting_started": {
      "description": "The Analytics Dashboard is automatically enabled for all accounts. No setup required - data collection starts immediately.",
      "first_steps": [
        "Navigate to Analytics from the main menu",
        "Explore the Overview dashboard for key metrics",
        "Use date range selector to view trends over time",
        "Click into specific metrics to see detailed breakdowns",
        "Create your first custom report (Professional plan and up)"
      ],
      "time_to_value": "Immediate - basic metrics available as soon as you have user activity"
    },
    "technical_details": {
      "data_retention": "13 months for all plans, 36 months for Enterprise",
      "update_frequency": "Real-time (5 minute refresh)",
      "api_access": "Yes - REST API available for custom integrations (Enterprise)",
      "data_export": "Excel, PDF, CSV formats"
    }
  },
  "metadata": {
    "product_status": "generally_available",
    "release_date": "2025-12-01",
    "latest_version": "2.1"
  },
  "module_id": "analytics-module-guid",
  "tags": ["analytics", "dashboard", "reporting", "metrics", "insights", "data"],
  "category": "product_overview",
  "related_article_ids": ["analytics-setup-guid", "custom-reports-guid", "integration-guide-guid"],
  "ticket_references": [],
  "created_by_user_id": "user-guid",
  "created_date": "2025-12-01T10:00:00Z",
  "status": "published",
  "version": 1
}
```

---

## 6. BEST PRACTICE / RECOMMENDATION

**Purpose**: Share recommended approaches, patterns, and guidelines

**When to Use**:
- Multiple ways to do something
- Want to guide users to optimal approach
- Industry standards or company conventions

**Key Characteristics**:
- Context: when to use this practice
- Rationale: why this is best
- Do's and Don'ts
- Examples
- Anti-patterns to avoid

### Example 6: Best Practice Article (Brief)

```json
{
  "article_type": "best_practice",
  "title": "Best Practices - Writing Effective Knowledge Articles",
  "summary": "Guidelines for creating clear, helpful knowledge base articles that users can easily find and understand.",
  "content": {
    "context": "When creating any knowledge article",
    "principles": [
      {
        "principle": "Start with the user's question",
        "rationale": "Users search with questions in mind. Title should match how they'd phrase it.",
        "example_good": "How to Reset Your Password",
        "example_bad": "Password Management Procedures"
      },
      {
        "principle": "Use simple, direct language",
        "rationale": "Users want quick answers, not literature. Avoid jargon.",
        "example_good": "Click the blue Save button",
        "example_bad": "Utilize the azure-hued persistence actuator"
      }
    ],
    "dos": [
      "Include screenshots for visual steps",
      "Test every step before publishing",
      "Link to related articles",
      "Update when processes change"
    ],
    "donts": [
      "Assume prior knowledge",
      "Use vague language like 'simply' or 'just'",
      "Skip error scenarios",
      "Let articles go stale (review quarterly)"
    ]
  }
}
```

---

## UNIFIED DATA MODEL

### Core Article Schema (All Types)

```sql
-- Main Articles Table
CREATE TABLE Articles (
    ArticleId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ArticleType VARCHAR(50) NOT NULL, -- 'how_to', 'troubleshooting', 'release_note', 'faq', 'product_overview', 'best_practice'
    Title NVARCHAR(200) NOT NULL,
    Summary NVARCHAR(500) NOT NULL,
    Content NVARCHAR(MAX) NOT NULL, -- JSON structure (varies by article type)
    
    -- Categorization
    ModuleId UNIQUEIDENTIFIER NOT NULL,
    Category VARCHAR(50) NOT NULL, -- 'configuration', 'error_resolution', 'release_notes', etc.
    Tags NVARCHAR(500), -- JSON array: ["tag1", "tag2"]
    
    -- Metadata
    EstimatedTimeMinutes INT NULL,
    Difficulty VARCHAR(20) NULL, -- 'beginner', 'intermediate', 'advanced'
    AppliesTo NVARCHAR(200) NULL, -- JSON array of versions: ["3.0", "3.1"]
    
    -- Status & Workflow
    Status VARCHAR(20) NOT NULL DEFAULT 'draft', -- 'draft', 'in_review', 'published', 'archived'
    
    -- Ownership & Dates
    CreatedByUserId UNIQUEIDENTIFIER NOT NULL,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastModifiedByUserId UNIQUEIDENTIFIER NOT NULL,
    LastModifiedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastReviewedDate DATETIME2 NULL,
    LastReviewedByUserId UNIQUEIDENTIFIER NULL,
    
    -- Engagement Metrics
    ViewCount INT NOT NULL DEFAULT 0,
    HelpfulCount INT NOT NULL DEFAULT 0,
    NotHelpfulCount INT NOT NULL DEFAULT 0,
    Rating DECIMAL(3,2) NULL, -- Calculated: HelpfulCount / (HelpfulCount + NotHelpfulCount)
    
    -- Versioning
    Version INT NOT NULL DEFAULT 1,
    
    -- Full-Text Search
    SearchVector AS (Title + ' ' + Summary + ' ' + CAST(Content AS NVARCHAR(MAX))),
    
    -- Soft Delete
    IsDeleted BIT NOT NULL DEFAULT 0,
    DeletedDate DATETIME2 NULL,
    DeletedByUserId UNIQUEIDENTIFIER NULL,
    
    CONSTRAINT FK_Articles_Module FOREIGN KEY (ModuleId) REFERENCES Modules(ModuleId),
    CONSTRAINT FK_Articles_CreatedBy FOREIGN KEY (CreatedByUserId) REFERENCES Users(UserId),
    CONSTRAINT FK_Articles_LastModifiedBy FOREIGN KEY (LastModifiedByUserId) REFERENCES Users(UserId),
    CONSTRAINT FK_Articles_LastReviewedBy FOREIGN KEY (LastReviewedByUserId) REFERENCES Users(UserId)
);

-- Full-text index for search
CREATE FULLTEXT INDEX ON Articles(SearchVector) 
KEY INDEX PK_Articles
WITH STOPLIST = SYSTEM;

-- Modules (Subject Areas)
CREATE TABLE Modules (
    ModuleId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500) NULL,
    SMEUserId UNIQUEIDENTIFIER NULL, -- Subject Matter Expert
    ParentModuleId UNIQUEIDENTIFIER NULL, -- For hierarchical modules
    IsActive BIT NOT NULL DEFAULT 1,
    DisplayOrder INT NULL,
    
    CONSTRAINT FK_Modules_SME FOREIGN KEY (SMEUserId) REFERENCES Users(UserId),
    CONSTRAINT FK_Modules_Parent FOREIGN KEY (ParentModuleId) REFERENCES Modules(ModuleId)
);

-- Ticket References (Many-to-Many)
CREATE TABLE ArticleTicketReferences (
    ArticleId UNIQUEIDENTIFIER NOT NULL,
    TicketId NVARCHAR(50) NOT NULL, -- External ticket ID (e.g., "JIRA-1234")
    TicketSystem VARCHAR(50) NOT NULL, -- 'jira', 'azure_devops', 'servicenow'
    TicketUrl NVARCHAR(500) NULL,
    
    PRIMARY KEY (ArticleId, TicketId),
    CONSTRAINT FK_ArticleTickets_Article FOREIGN KEY (ArticleId) REFERENCES Articles(ArticleId) ON DELETE CASCADE
);

-- Related Articles (Many-to-Many, Self-Referential)
CREATE TABLE RelatedArticles (
    ArticleId UNIQUEIDENTIFIER NOT NULL,
    RelatedArticleId UNIQUEIDENTIFIER NOT NULL,
    RelationshipType VARCHAR(50) NULL, -- 'prerequisite', 'follow_up', 'alternative', 'related'
    RelevanceScore DECIMAL(3,2) NULL, -- 0.00-1.00 from AI
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    PRIMARY KEY (ArticleId, RelatedArticleId),
    CONSTRAINT FK_RelatedArticles_Article FOREIGN KEY (ArticleId) REFERENCES Articles(ArticleId) ON DELETE CASCADE,
    CONSTRAINT FK_RelatedArticles_Related FOREIGN KEY (RelatedArticleId) REFERENCES Articles(ArticleId),
    CONSTRAINT CK_RelatedArticles_NotSelf CHECK (ArticleId != RelatedArticleId)
);

-- Article Versions (Full History)
CREATE TABLE ArticleVersions (
    VersionId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ArticleId UNIQUEIDENTIFIER NOT NULL,
    VersionNumber INT NOT NULL,
    Content NVARCHAR(MAX) NOT NULL, -- Full article JSON snapshot
    ChangeDescription NVARCHAR(1000) NULL,
    ModifiedByUserId UNIQUEIDENTIFIER NOT NULL,
    ModifiedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_ArticleVersions_Article FOREIGN KEY (ArticleId) REFERENCES Articles(ArticleId) ON DELETE CASCADE,
    CONSTRAINT FK_ArticleVersions_User FOREIGN KEY (ModifiedByUserId) REFERENCES Users(UserId)
);

-- Review Workflow
CREATE TABLE ArticleReviews (
    ReviewId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ArticleId UNIQUEIDENTIFIER NOT NULL,
    ReviewerUserId UNIQUEIDENTIFIER NOT NULL,
    ReviewDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    Status VARCHAR(20) NOT NULL, -- 'approved', 'rejected', 'changes_requested'
    Comments NVARCHAR(2000) NULL,
    
    CONSTRAINT FK_ArticleReviews_Article FOREIGN KEY (ArticleId) REFERENCES Articles(ArticleId) ON DELETE CASCADE,
    CONSTRAINT FK_ArticleReviews_Reviewer FOREIGN KEY (ReviewerUserId) REFERENCES Users(UserId)
);

-- User Feedback on Articles
CREATE TABLE ArticleFeedback (
    FeedbackId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ArticleId UNIQUEIDENTIFIER NOT NULL,
    UserId UNIQUEIDENTIFIER NULL, -- NULL for anonymous feedback
    FeedbackType VARCHAR(20) NOT NULL, -- 'helpful', 'not_helpful'
    Comments NVARCHAR(1000) NULL,
    FeedbackDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_ArticleFeedback_Article FOREIGN KEY (ArticleId) REFERENCES Articles(ArticleId) ON DELETE CASCADE,
    CONSTRAINT FK_ArticleFeedback_User FOREIGN KEY (UserId) REFERENCES Users(UserId)
);

-- Users
CREATE TABLE Users (
    UserId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Email NVARCHAR(255) NOT NULL UNIQUE,
    Name NVARCHAR(200) NOT NULL,
    Role VARCHAR(20) NOT NULL, -- 'guest', 'user', 'power_user', 'admin'
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastLoginDate DATETIME2 NULL
);
```

### JSON Content Structure by Article Type

Each article type stores its specific structure in the `Content` NVARCHAR(MAX) column as JSON.

**How-To Article Content Structure**:
```json
{
  "prerequisites": [...],
  "steps": [...],
  "validation": {...},
  "troubleshooting": [...],
  "next_steps": [...]
}
```

**Troubleshooting Article Content Structure**:
```json
{
  "symptoms": [...],
  "affected_versions": [...],
  "severity": "...",
  "impact": "...",
  "root_causes": [...],
  "workarounds": [...],
  "prevention": [...],
  "escalation": {...}
}
```

**Release Note Content Structure**:
```json
{
  "version": "...",
  "release_date": "...",
  "release_type": "...",
  "highlights": [...],
  "sections": [...],
  "known_issues": [...],
  "upgrade_instructions": {...},
  "compatibility": {...}
}
```

*(And so on for each article type)*

---

## Summary of Article Types

| Type | Purpose | Key Structure | Primary Users |
|------|---------|---------------|---------------|
| How-To | Step-by-step instructions | Numbered steps, prerequisites | End users, admins |
| Troubleshooting | Fix problems | Symptoms, causes, solutions | Support, end users |
| Release Note | Communicate changes | Version, sections (features, bugs) | All users, stakeholders |
| FAQ | Quick answers | Q&A pairs, categories | End users |
| Product Overview | Explain features | Benefits, use cases | Sales, new users |
| Best Practice | Guidance | Principles, dos/don'ts | Power users, admins |

---

## Next Steps

Now that we have defined article types and data model, would you like me to:

1. **Create UI mockups** showing how each article type looks in the interface?
2. **Build the database schema scripts** (SQL for Azure SQL)?
3. **Create C# entity models** matching this schema?
4. **Design the AI agent prompts** for each article type?

Let me know what you'd like to tackle next!

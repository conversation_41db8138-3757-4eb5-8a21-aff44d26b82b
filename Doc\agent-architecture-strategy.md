# Azure AI Foundry Agent Architecture & Strategy

## Executive Summary

The agent architecture is the core differentiator of this knowledge base platform. The key decision: **Should we use multiple specialized agents OR a single agent with different prompts?**

**TL;DR Recommendation**: **Hybrid Approach** - Use a Router Agent + Specialized Agents for different content types, with shared context and evaluation capabilities.

---

## Agent Architecture Decision: Multiple Agents vs Single Agent

### Option 1: Single Agent with Dynamic Prompts ❌

**Approach**: One Azure OpenAI deployment with different system prompts based on article type

```
User Input → [Classifier] → Single Agent + Context-Specific Prompt → Output
```

**Pros:**
- Simpler infrastructure (1 deployment)
- Lower cost (no redundancy)
- Easier to maintain
- Consistent behavior across types

**Cons:**
- ❌ **Prompt pollution**: System prompt becomes massive and conflicting
- ❌ **Less optimization**: Can't fine-tune for specific use cases
- ❌ **Hard to test**: Changes affect all article types
- ❌ **Token waste**: Sending irrelevant context for each request
- ❌ **Quality ceiling**: Jack of all trades, master of none
- ❌ **Difficult versioning**: Can't update one type without risk to others

**Example Problem:**
```
System: You are an expert at creating how-to articles, troubleshooting guides, 
release notes, FAQs, product descriptions, and technical documentation. 
For how-to articles, focus on step-by-step instructions...
For release notes, focus on changes and impact...
For troubleshooting, focus on symptoms and solutions...
[This becomes unwieldy and creates confusion]
```

---

### Option 2: Multiple Specialized Agents ✅

**Approach**: Separate Azure OpenAI deployments/prompts for each content type

```
User Input → [Router Agent] → Specialized Agent (How-To, Troubleshooting, Release Note, etc.) → Output
```

**Pros:**
- ✅ **Focused excellence**: Each agent optimized for its specific task
- ✅ **Easier testing**: Test and update agents independently
- ✅ **Better quality**: Specialized prompts produce better results
- ✅ **Independent evolution**: Tune each agent based on feedback
- ✅ **Clear separation**: Easier to understand and maintain
- ✅ **Versioning**: Can version agents independently
- ✅ **A/B testing**: Easy to compare agent versions

**Cons:**
- More deployments to manage (but Azure makes this easy)
- Slightly higher cost (but marginal with same model)
- Need routing logic (but this is valuable anyway)

**Cost Reality Check:**
- Azure OpenAI pricing is per token, not per deployment
- Multiple deployments of same model = no extra cost
- You pay for what you use, not what you deploy

---

## Recommended Architecture: Router + Specialized Agents

### High-Level Flow

```
┌─────────────────────────────────────────────────────────────────┐
│                          USER INPUT                              │
│  "Here's a conversation from Slack about fixing the login bug"  │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                      ROUTER AGENT (GPT-4)                        │
│  Purpose: Classify input and determine which specialist needed   │
│  Output: { type: "troubleshooting", confidence: 0.95 }          │
└─────────────────────────────────────────────────────────────────┘
                              │
                ┌─────────────┼─────────────┐
                ▼             ▼             ▼
    ┌───────────────┐  ┌───────────────┐  ┌───────────────┐
    │   How-To      │  │Troubleshooting│  │ Release Note  │
    │    Agent      │  │    Agent      │  │    Agent      │
    └───────────────┘  └───────────────┘  └───────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    STRUCTURED ARTICLE OUTPUT                     │
│  Title, Summary, Content, Tags, Category, Related Topics        │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    EVALUATION AGENT (GPT-4)                      │
│  Purpose: Quality check - completeness, clarity, accuracy       │
│  Output: { score: 8.5, issues: [...], suggestions: [...] }     │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    RELATIONSHIP AGENT                            │
│  Purpose: Find related articles using embeddings                │
│  Output: [article_id_1, article_id_2, ...]                     │
└─────────────────────────────────────────────────────────────────┘
```

---

## Specialized Agent Definitions

### 1. **Router Agent** (Classification)

**Purpose**: Analyze user input and determine content type

**Model**: GPT-4 Turbo (fast, accurate classification)

**System Prompt**:
```
You are a content classification expert. Analyze user input and determine 
the type of knowledge article they want to create.

Article Types:
- how_to: Step-by-step instructions for completing a task
- troubleshooting: Problem diagnosis and solutions
- release_note: Product changes, updates, new features
- faq: Common questions and answers
- product_overview: Product descriptions and capabilities
- technical_spec: Technical documentation and specifications
- best_practice: Recommended approaches and patterns

Return JSON:
{
  "article_type": "...",
  "confidence": 0.0-1.0,
  "reasoning": "brief explanation"
}

If confidence < 0.7, ask user for clarification.
```

**Example Classifications**:
```
Input: "Users can't log in after the latest update"
→ { type: "troubleshooting", confidence: 0.95 }

Input: "Here's how to set up SSO in our product"
→ { type: "how_to", confidence: 0.92 }

Input: "We released version 2.5 with new dashboard features"
→ { type: "release_note", confidence: 0.98 }
```

---

### 2. **How-To Article Agent**

**Purpose**: Create step-by-step instructional content

**Model**: GPT-4 (for quality)

**System Prompt Structure**:
```
You are an expert technical writer specializing in how-to guides.

Your goal: Create clear, actionable step-by-step instructions that users 
can follow to accomplish a specific task.

Guidelines:
1. Start with prerequisites (what users need before starting)
2. Break down into numbered steps (aim for 5-10 steps)
3. Each step should be a single, clear action
4. Include expected outcomes ("You should see...")
5. Add warnings/cautions where needed
6. End with validation ("To verify it worked...")
7. Suggest related tasks ("Next, you might want to...")

Format Output as JSON:
{
  "title": "How to [Action] [Object]",
  "summary": "Brief 1-2 sentence description",
  "prerequisites": ["item1", "item2"],
  "steps": [
    {
      "number": 1,
      "action": "Clear instruction",
      "expected_outcome": "What user should see",
      "screenshot_needed": true/false,
      "warning": "Optional warning"
    }
  ],
  "validation": "How to verify success",
  "related_topics": ["topic1", "topic2"],
  "estimated_time": "5 minutes",
  "difficulty": "beginner|intermediate|advanced",
  "tags": ["tag1", "tag2"]
}
```

**Example Input → Output**:
```
Input: "Paste from doc: To enable MFA, go to settings, click security, 
toggle MFA on, scan QR code with app, enter verification code"

Output:
{
  "title": "How to Enable Multi-Factor Authentication (MFA)",
  "summary": "Secure your account by enabling MFA in 5 simple steps",
  "prerequisites": [
    "Active account with admin privileges",
    "Authenticator app installed (e.g., Microsoft Authenticator, Google Authenticator)"
  ],
  "steps": [
    {
      "number": 1,
      "action": "Navigate to Settings from the main menu",
      "expected_outcome": "Settings page opens",
      "screenshot_needed": true
    },
    {
      "number": 2,
      "action": "Click on the 'Security' tab",
      "expected_outcome": "Security settings displayed",
      "screenshot_needed": true
    },
    ...
  ]
}
```

---

### 3. **Troubleshooting Article Agent**

**Purpose**: Create problem-solution documentation

**Model**: GPT-4

**System Prompt Structure**:
```
You are an expert in creating troubleshooting documentation.

Your goal: Transform problem reports into structured troubleshooting guides 
that help users diagnose and fix issues.

Guidelines:
1. Start with clear symptom description
2. List possible causes (most common first)
3. Provide diagnostic steps to identify root cause
4. Offer solutions for each cause
5. Include preventive measures
6. Add escalation path if solutions don't work

Format Output as JSON:
{
  "title": "[Problem] - Troubleshooting Guide",
  "summary": "Brief description of the issue",
  "symptoms": ["symptom1", "symptom2"],
  "affected_versions": ["version range"],
  "possible_causes": [
    {
      "cause": "Description of cause",
      "likelihood": "high|medium|low",
      "diagnostic_steps": ["step1", "step2"],
      "solution": {
        "steps": ["action1", "action2"],
        "expected_result": "What should happen",
        "time_to_fix": "estimate"
      }
    }
  ],
  "prevention": ["preventive measure1", "preventive measure2"],
  "escalation": "Contact support if none of the above solutions work",
  "related_issues": ["related problem1", "related problem2"],
  "tags": ["tag1", "tag2"]
}
```

**Example Input → Output**:
```
Input: "Slack conversation: Users reporting login failures after update. 
Turns out session tokens expired. Fixed by clearing cookies or re-auth."

Output:
{
  "title": "Login Failures After Update - Troubleshooting Guide",
  "summary": "Users unable to log in after recent platform update",
  "symptoms": [
    "Login page appears but credentials are rejected",
    "Error message: 'Invalid session'",
    "Affects users who were previously logged in"
  ],
  "possible_causes": [
    {
      "cause": "Expired session tokens after update",
      "likelihood": "high",
      "diagnostic_steps": [
        "Check browser console for 401 errors",
        "Verify user can access login page",
        "Confirm issue started after update deployment"
      ],
      "solution": {
        "steps": [
          "Clear browser cookies for the application domain",
          "Close all browser tabs",
          "Navigate to login page and re-authenticate"
        ],
        "expected_result": "User can log in successfully",
        "time_to_fix": "2 minutes"
      }
    }
  ],
  "prevention": [
    "Token migration script in future updates",
    "Automatic re-authentication prompt"
  ]
}
```

---

### 4. **Release Note Agent**

**Purpose**: Create structured release notes from change descriptions

**Model**: GPT-4

**System Prompt Structure**:
```
You are an expert at writing clear, user-focused release notes.

Your goal: Transform technical change descriptions into release notes that 
help users understand what changed, why it matters, and what to do.

Guidelines:
1. Group changes by type (New Features, Improvements, Bug Fixes, Breaking Changes)
2. Use user-friendly language (not technical jargon)
3. Explain impact/benefit to user
4. Include migration steps for breaking changes
5. Add visual indicators (✨ new, 🐛 fix, ⚠️ breaking)

Format Output as JSON:
{
  "title": "Release Notes - Version X.Y.Z",
  "version": "X.Y.Z",
  "release_date": "YYYY-MM-DD",
  "summary": "High-level overview of this release",
  "highlights": ["top change 1", "top change 2", "top change 3"],
  "sections": [
    {
      "type": "new_features|improvements|bug_fixes|breaking_changes",
      "items": [
        {
          "title": "Short description",
          "description": "User-friendly explanation",
          "impact": "Who this affects and why it matters",
          "action_required": "What users need to do (if anything)",
          "related_tickets": ["TICKET-123"],
          "icon": "emoji"
        }
      ]
    }
  ],
  "upgrade_notes": "Special instructions for upgrading",
  "known_issues": ["issue1", "issue2"],
  "tags": ["tag1", "tag2"]
}
```

---

### 5. **FAQ Article Agent**

**Purpose**: Create Q&A format articles

**Model**: GPT-4 Turbo

**System Prompt Structure**:
```
You are an expert at creating FAQ (Frequently Asked Questions) articles.

Your goal: Transform common questions into clear, concise Q&A pairs.

Guidelines:
1. Phrase questions as users would ask them
2. Provide direct, actionable answers
3. Link to detailed articles when needed
4. Group related questions
5. Keep answers concise (2-4 sentences)

Format Output as JSON:
{
  "title": "FAQ - [Topic]",
  "summary": "Common questions about [topic]",
  "categories": [
    {
      "name": "Category name",
      "questions": [
        {
          "question": "User-phrased question?",
          "answer": "Clear, concise answer",
          "related_articles": ["article1", "article2"]
        }
      ]
    }
  ],
  "tags": ["tag1", "tag2"]
}
```

---

### 6. **Product Overview Agent**

**Purpose**: Create product/feature descriptions

**Model**: GPT-4

**System Prompt Structure**:
```
You are an expert product marketer and technical writer.

Your goal: Create compelling product overviews that explain what a product 
does, who it's for, and why it matters.

Guidelines:
1. Start with elevator pitch (1-2 sentences)
2. Explain key benefits (not features)
3. Describe ideal use cases
4. List core capabilities
5. Include getting started guidance

Format Output as JSON:
{
  "title": "[Product/Feature Name] Overview",
  "summary": "Elevator pitch",
  "target_audience": "Who this is for",
  "key_benefits": ["benefit1", "benefit2", "benefit3"],
  "use_cases": [
    {
      "scenario": "Description of use case",
      "solution": "How product solves it"
    }
  ],
  "core_capabilities": ["capability1", "capability2"],
  "getting_started": "Brief guide to first steps",
  "tags": ["tag1", "tag2"]
}
```

---

### 7. **Evaluation Agent** (Quality Control)

**Purpose**: Assess article quality and completeness

**Model**: GPT-4

**System Prompt Structure**:
```
You are a quality assurance expert for knowledge base articles.

Your goal: Evaluate article quality across multiple dimensions and provide 
actionable feedback.

Evaluation Criteria:
1. Completeness: Does it cover the topic fully?
2. Clarity: Is it easy to understand?
3. Accuracy: Are there any obvious errors?
4. Actionability: Can users act on this information?
5. Structure: Is it well-organized?
6. SEO: Does it have good keywords and tags?

Format Output as JSON:
{
  "overall_score": 0-10,
  "scores": {
    "completeness": 0-10,
    "clarity": 0-10,
    "accuracy": 0-10,
    "actionability": 0-10,
    "structure": 0-10,
    "seo": 0-10
  },
  "strengths": ["strength1", "strength2"],
  "issues": [
    {
      "severity": "critical|major|minor",
      "category": "completeness|clarity|accuracy|etc",
      "description": "What's wrong",
      "suggestion": "How to fix it"
    }
  ],
  "recommended_changes": ["change1", "change2"],
  "readability_level": "beginner|intermediate|advanced",
  "estimated_read_time": "X minutes"
}
```

---

### 8. **Relationship Agent** (Article Linking)

**Purpose**: Find related articles using semantic similarity

**Model**: text-embedding-ada-002 + GPT-4

**Approach**:
1. Generate embedding for new article
2. Compare with existing article embeddings (vector similarity)
3. GPT-4 validates relevance and explains relationships
4. Return top N related articles with relevance scores

**System Prompt Structure**:
```
You are an expert at identifying relationships between knowledge articles.

You will receive:
1. New article content
2. List of potentially related articles (from vector search)

Your goal: Validate relationships and explain why articles are related.

Format Output as JSON:
{
  "related_articles": [
    {
      "article_id": "uuid",
      "title": "Article title",
      "relevance_score": 0.0-1.0,
      "relationship_type": "prerequisite|related_topic|follow_up|alternative_solution",
      "explanation": "Why these articles are related"
    }
  ],
  "suggested_links": [
    {
      "section": "Section in new article where link should be added",
      "link_text": "Suggested anchor text",
      "target_article": "article_id"
    }
  ]
}
```

---

## Azure AI Foundry Implementation

### Azure OpenAI Deployments

```
Resource Group: rg-knowledge-base-prod

Azure OpenAI Service: oai-knowledge-base-prod
├── Deployment: gpt-4-router (GPT-4 Turbo, 10K TPM)
├── Deployment: gpt-4-howto (GPT-4, 20K TPM)
├── Deployment: gpt-4-troubleshooting (GPT-4, 20K TPM)
├── Deployment: gpt-4-releasenote (GPT-4, 20K TPM)
├── Deployment: gpt-4-faq (GPT-4 Turbo, 15K TPM)
├── Deployment: gpt-4-overview (GPT-4, 15K TPM)
├── Deployment: gpt-4-evaluator (GPT-4, 10K TPM)
└── Deployment: text-embedding-ada-002 (50K TPM)
```

**Cost Optimization**:
- Use GPT-4 Turbo where speed matters (Router, FAQ)
- Use GPT-4 where quality is critical (How-To, Troubleshooting)
- Share TPM quotas across deployments
- Cache common prompts

---

## Agent Orchestration with .NET

### Architecture

```csharp
// Agent Service Interface
public interface IAgentService
{
    Task<AgentResponse> ProcessAsync(AgentRequest request);
}

// Router Service
public class RouterAgentService : IAgentService
{
    private readonly IAzureOpenAIClient _openAIClient;
    
    public async Task<ArticleType> ClassifyAsync(string userInput)
    {
        var response = await _openAIClient.GetChatCompletionsAsync(
            deployment: "gpt-4-router",
            systemPrompt: RouterSystemPrompt,
            userMessage: userInput
        );
        
        return ParseClassification(response);
    }
}

// Agent Factory
public class AgentFactory
{
    private readonly Dictionary<ArticleType, IAgentService> _agents;
    
    public IAgentService GetAgent(ArticleType type)
    {
        return _agents[type];
    }
}

// Orchestration Service
public class ArticleCreationOrchestrator
{
    private readonly RouterAgentService _router;
    private readonly AgentFactory _agentFactory;
    private readonly EvaluationAgentService _evaluator;
    private readonly RelationshipAgentService _relationshipAgent;
    
    public async Task<ArticleCreationResult> CreateArticleAsync(string userInput)
    {
        // Step 1: Classify
        var articleType = await _router.ClassifyAsync(userInput);
        
        // Step 2: Get specialized agent
        var agent = _agentFactory.GetAgent(articleType);
        
        // Step 3: Generate article
        var article = await agent.ProcessAsync(userInput);
        
        // Step 4: Evaluate quality
        var evaluation = await _evaluator.EvaluateAsync(article);
        
        // Step 5: Find related articles
        var relatedArticles = await _relationshipAgent.FindRelatedAsync(article);
        
        return new ArticleCreationResult
        {
            Article = article,
            Evaluation = evaluation,
            RelatedArticles = relatedArticles,
            ArticleType = articleType
        };
    }
}
```

---

## Prompt Management Strategy

### Storage Options

**Option 1: Azure App Configuration** ✅ Recommended
- Centralized prompt storage
- Feature flags for A/B testing
- Versioning and rollback
- Dynamic updates (no deployment needed)
- Key Vault integration for sensitive prompts

**Option 2: Database (Azure SQL)**
- Store in `AgentPrompts` table
- Version control
- Audit trail
- Can be managed via admin UI

**Option 3: Code (Constants/Config Files)**
- Simple for initial development
- Version controlled with code
- Requires deployment to update

**Recommendation**: Start with Azure App Configuration for flexibility

```csharp
public class PromptService
{
    private readonly IConfigurationClient _configClient;
    private readonly IMemoryCache _cache;
    
    public async Task<string> GetPromptAsync(string agentName, string version = "latest")
    {
        var cacheKey = $"prompt:{agentName}:{version}";
        
        if (_cache.TryGetValue(cacheKey, out string cachedPrompt))
            return cachedPrompt;
        
        var prompt = await _configClient.GetConfigurationSettingAsync(
            $"Agent:Prompts:{agentName}:{version}"
        );
        
        _cache.Set(cacheKey, prompt.Value, TimeSpan.FromHours(1));
        
        return prompt.Value;
    }
    
    public async Task UpdatePromptAsync(string agentName, string newPrompt)
    {
        // Update in App Configuration
        // Clear cache
        // Log change
    }
}
```

---

## Prompt Versioning & A/B Testing

```csharp
public class PromptVersion
{
    public string AgentName { get; set; }
    public string Version { get; set; }
    public string Prompt { get; set; }
    public bool IsActive { get; set; }
    public double TrafficPercentage { get; set; } // For A/B testing
    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; }
}

// Usage
var promptVersion = GetPromptForUser(userId, agentName);
// 80% get v1, 20% get v2
```

---

## Testing & Evaluation Strategy

### 1. **Unit Tests for Each Agent**

Create test suites with known inputs and expected outputs:

```csharp
[Fact]
public async Task HowToAgent_Should_Generate_Structured_Steps()
{
    var input = "To reset password, click forgot password, enter email, check inbox, click link, enter new password";
    
    var result = await _howToAgent.ProcessAsync(input);
    
    Assert.NotNull(result.Steps);
    Assert.True(result.Steps.Count >= 5);
    Assert.All(result.Steps, step => Assert.NotEmpty(step.Action));
}
```

### 2. **Golden Dataset**

Create a curated set of examples for each article type:

```
golden_dataset/
├── how_to/
│   ├── example_1_input.txt
│   ├── example_1_expected_output.json
│   ├── example_2_input.txt
│   └── example_2_expected_output.json
├── troubleshooting/
│   ├── example_1_input.txt
│   └── example_1_expected_output.json
└── ...
```

### 3. **Automated Evaluation**

Run agents against golden dataset and measure:
- Structural correctness (JSON schema validation)
- Completeness (required fields present)
- Quality scores from Evaluation Agent
- Human review for 10% of outputs

### 4. **Prompt Iteration**

```
1. Create baseline prompt → Test on golden dataset → Measure quality
2. Iterate prompt → Test → Compare to baseline
3. If improvement > 10%, promote to staging
4. A/B test in production (80/20 split)
5. Monitor quality metrics for 1 week
6. Promote winner to 100%
```

---

## Agent Monitoring & Observability

### Key Metrics to Track

```csharp
public class AgentMetrics
{
    public string AgentName { get; set; }
    public DateTime Timestamp { get; set; }
    
    // Performance
    public int TokensUsed { get; set; }
    public double LatencyMs { get; set; }
    public decimal Cost { get; set; }
    
    // Quality
    public double EvaluationScore { get; set; }
    public int UserThumbsUp { get; set; }
    public int UserThumbsDown { get; set; }
    
    // Errors
    public int TimeoutErrors { get; set; }
    public int ValidationErrors { get; set; }
}
```

**Dashboard in Application Insights**:
- Agent success rate
- Average quality scores
- Token usage and cost per agent
- Latency percentiles (p50, p95, p99)
- Error rates and types

---

## Cost Estimation for Agent Architecture

### GPT-4 Pricing (as of Jan 2025)
- Input: $0.03 per 1K tokens
- Output: $0.06 per 1K tokens

### GPT-4 Turbo Pricing
- Input: $0.01 per 1K tokens
- Output: $0.03 per 1K tokens

### Embedding Pricing
- $0.0001 per 1K tokens

### Estimated Monthly Cost (500 articles created)

| Agent Type | Avg Tokens In | Avg Tokens Out | Calls/Month | Cost |
|------------|---------------|----------------|-------------|------|
| Router | 500 | 100 | 500 | $4.50 |
| How-To | 2000 | 1500 | 150 | $16.50 |
| Troubleshooting | 2000 | 1500 | 150 | $16.50 |
| Release Note | 2000 | 1000 | 100 | $9.00 |
| FAQ | 1000 | 800 | 50 | $2.90 |
| Overview | 1500 | 1000 | 50 | $3.75 |
| Evaluator | 2000 | 500 | 500 | $21.00 |
| Embeddings | 500 | - | 1000 | $0.05 |
| **TOTAL** | | | | **~$74/month** |

**Note**: This is well within your budget and allows for 3-5x growth

---

## Implementation Roadmap

### Phase 1: Core Agent Framework (Week 1-2)
- [ ] Azure OpenAI service setup
- [ ] Basic router agent
- [ ] How-To agent (primary use case)
- [ ] Troubleshooting agent
- [ ] Simple orchestration
- [ ] Basic evaluation

### Phase 2: Specialized Agents (Week 3-4)
- [ ] Release Note agent
- [ ] FAQ agent
- [ ] Product Overview agent
- [ ] Relationship agent with embeddings

### Phase 3: Quality & Optimization (Week 5-6)
- [ ] Advanced evaluation agent
- [ ] Prompt management system
- [ ] A/B testing framework
- [ ] Golden dataset creation
- [ ] Monitoring and metrics

---

## Web UI Integration Points

### User Workflow

```
1. User lands on "Create Article" page
2. User pastes content OR uploads document OR enters manually
3. UI shows: "Analyzing your content..." (Router agent classifying)
4. UI updates: "This looks like a [How-To] article. Is that correct?"
   [Yes, create How-To] [No, let me choose] [Cancel]
5. If user confirms or chooses type, specialized agent runs
6. UI shows progress: "Extracting information... Structuring content..."
7. Article preview shown with Evaluation scores
8. User can edit/refine
9. User saves draft or submits for review
```

### UI Components Needed

1. **Article Type Selector** (for manual override)
   - Radio buttons or cards for each type
   - Show example of what each type produces

2. **Agent Progress Indicator**
   - Shows which agent is running
   - Shows steps in process
   - Estimated time remaining

3. **Article Preview with Evaluation**
   - Side-by-side: Generated vs Editable
   - Quality score visualization
   - Issues and suggestions highlighted

4. **Related Articles Sidebar**
   - Shows articles found by Relationship agent
   - Allows user to confirm relationships

---

## Next Steps

Now that we have the agent architecture designed, which would you like to focus on next?

1. **Start implementing the Router + How-To agents** (most critical path)
2. **Design the UI mockups** for the article creation workflow
3. **Setup Azure AI Foundry** and create the first agent deployments
4. **Create the golden dataset** for testing agents
5. **Build the prompt management system**

I recommend: **Option 1 + 3 together** - Set up Azure AI Foundry and implement the first two agents with basic prompts, then iterate based on real results.

Should I proceed with creating:
- Bicep templates for Azure OpenAI setup
- C# agent service implementations
- Initial prompt templates
- Test harness for agent evaluation

Let me know what you'd like to tackle first!

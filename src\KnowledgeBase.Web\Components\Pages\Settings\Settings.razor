@page "/settings"
@using KnowledgeBase.Web.Services
@inject ISnackbar Snackbar
@inject IDialogService DialogService
@inject KnowledgeBaseApiService ApiService

<PageTitle>Settings - Knowledge Base</PageTitle>

<MudText Typo="Typo.h4" Class="mb-4">Settings</MudText>

<MudTabs Elevation="0" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-4">
    @* General Settings *@
    <MudTabPanel Text="General" Icon="@Icons.Material.Filled.Settings">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">General Settings</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack Spacing="4">
                    <MudTextField @bind-Value="_settings.ApplicationName" Label="Application Name"
                                  HelperText="Name displayed in the header and browser title"
                                  Variant="Variant.Outlined" />

                    <MudTextField @bind-Value="_settings.SupportEmail" Label="Support Email"
                                  InputType="InputType.Email"
                                  HelperText="Email address for user support inquiries"
                                  Variant="Variant.Outlined" />

                    <MudSelect T="string" @bind-Value="_settings.DefaultLanguage" Label="Default Language" Variant="Variant.Outlined">
                        <MudSelectItem T="string" Value="@("en-US")">English (US)</MudSelectItem>
                        <MudSelectItem T="string" Value="@("en-GB")">English (UK)</MudSelectItem>
                        <MudSelectItem T="string" Value="@("es")">Spanish</MudSelectItem>
                        <MudSelectItem T="string" Value="@("fr")">French</MudSelectItem>
                        <MudSelectItem T="string" Value="@("de")">German</MudSelectItem>
                    </MudSelect>

                    <MudSelect T="string" @bind-Value="_settings.Timezone" Label="Timezone" Variant="Variant.Outlined">
                        <MudSelectItem T="string" Value="@("UTC")">UTC</MudSelectItem>
                        <MudSelectItem T="string" Value="@("America/New_York")">Eastern Time (US)</MudSelectItem>
                        <MudSelectItem T="string" Value="@("America/Chicago")">Central Time (US)</MudSelectItem>
                        <MudSelectItem T="string" Value="@("America/Los_Angeles")">Pacific Time (US)</MudSelectItem>
                        <MudSelectItem T="string" Value="@("Europe/London")">London</MudSelectItem>
                        <MudSelectItem T="string" Value="@("Europe/Paris")">Paris</MudSelectItem>
                    </MudSelect>

                    <MudNumericField T="int" @bind-Value="_settings.SessionTimeoutMinutes" Label="Session Timeout (minutes)"
                                     Min="5" Max="480" Variant="Variant.Outlined"
                                     HelperText="How long before an inactive session expires" />
                </MudStack>
            </MudCardContent>
        </MudCard>
    </MudTabPanel>

    @* Article Settings *@
    <MudTabPanel Text="Articles" Icon="@Icons.Material.Filled.Article">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Article Settings</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack Spacing="4">
                    <MudSwitch @bind-Value="_settings.RequireReviewBeforePublish" Label="Require Review Before Publishing"
                               Color="Color.Primary" T="bool" />

                    <MudSwitch @bind-Value="_settings.EnableVersionHistory" Label="Enable Version History"
                               Color="Color.Primary" T="bool" />

                    <MudSwitch @bind-Value="_settings.AutoSaveDrafts" Label="Auto-Save Drafts"
                               Color="Color.Primary" T="bool" />

                    <MudNumericField T="int" @bind-Value="_settings.AutoSaveIntervalSeconds" Label="Auto-Save Interval (seconds)"
                                     Min="10" Max="300" Variant="Variant.Outlined"
                                     Disabled="@(!_settings.AutoSaveDrafts)" />

                    <MudDivider Class="my-2" />

                    <MudText Typo="Typo.subtitle1">Default Article Properties</MudText>

                    <MudSelect T="ArticleType" @bind-Value="_settings.DefaultArticleType" Label="Default Article Type" Variant="Variant.Outlined">
                        @foreach (var type in Enum.GetValues<ArticleType>())
                        {
                            <MudSelectItem T="ArticleType" Value="@type">@type</MudSelectItem>
                        }
                    </MudSelect>

                    <MudSelect T="Difficulty" @bind-Value="_settings.DefaultDifficulty" Label="Default Difficulty" Variant="Variant.Outlined">
                        @foreach (var diff in Enum.GetValues<Difficulty>())
                        {
                            <MudSelectItem T="Difficulty" Value="@diff">@diff</MudSelectItem>
                        }
                    </MudSelect>

                    <MudTextField @bind-Value="_settings.DefaultTags" Label="Default Tags"
                                  HelperText="Comma-separated default tags for new articles"
                                  Variant="Variant.Outlined" />
                </MudStack>
            </MudCardContent>
        </MudCard>
    </MudTabPanel>

    @* AI Settings *@
    <MudTabPanel Text="AI Configuration" Icon="@Icons.Material.Filled.AutoAwesome">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">AI Configuration</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack Spacing="4">
                    <MudSwitch @bind-Value="_settings.EnableAIGeneration" Label="Enable AI Article Generation"
                               Color="Color.Primary" T="bool" />

                    <MudSwitch @bind-Value="_settings.EnableAIEvaluation" Label="Enable AI Quality Evaluation"
                               Color="Color.Primary" T="bool" />

                    <MudSwitch @bind-Value="_settings.EnableAISuggestions" Label="Enable AI Improvement Suggestions"
                               Color="Color.Primary" T="bool" />

                    <MudDivider Class="my-2" />

                    <MudText Typo="Typo.subtitle1">Azure OpenAI Settings</MudText>

                    <MudTextField @bind-Value="_settings.AzureOpenAIEndpoint" Label="Azure OpenAI Endpoint"
                                  Variant="Variant.Outlined" Placeholder="https://your-resource.openai.azure.com/"
                                  HelperText="Your Azure OpenAI resource endpoint" />

                    <MudTextField @bind-Value="_settings.AzureOpenAIDeployment" Label="Model Deployment Name"
                                  Variant="Variant.Outlined" Placeholder="gpt-4o"
                                  HelperText="Name of your GPT model deployment" />

                    <MudTextField @bind-Value="_settings.AzureOpenAIApiKey" Label="API Key"
                                  Variant="Variant.Outlined" InputType="InputType.Password"
                                  HelperText="Your Azure OpenAI API key" />

                    <MudAlert Severity="Severity.Info" Dense="true">
                        API credentials are stored securely and encrypted at rest.
                    </MudAlert>

                    <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="@TestAIConnection"
                               StartIcon="@Icons.Material.Filled.NetworkCheck" Disabled="@_testingConnection">
                        @if (_testingConnection)
                        {
                            <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                        }
                        Test Connection
                    </MudButton>
                </MudStack>
            </MudCardContent>
        </MudCard>
    </MudTabPanel>

    @* Search Settings *@
    <MudTabPanel Text="Search" Icon="@Icons.Material.Filled.Search">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Search Configuration</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack Spacing="4">
                    <MudText Typo="Typo.subtitle1">Azure AI Search Settings</MudText>

                    <MudTextField @bind-Value="_settings.AzureSearchEndpoint" Label="Azure AI Search Endpoint"
                                  Variant="Variant.Outlined" Placeholder="https://your-search.search.windows.net"
                                  HelperText="Your Azure AI Search service endpoint" />

                    <MudTextField @bind-Value="_settings.AzureSearchIndexName" Label="Index Name"
                                  Variant="Variant.Outlined" Placeholder="knowledgebase-articles"
                                  HelperText="Name of the search index" />

                    <MudTextField @bind-Value="_settings.AzureSearchApiKey" Label="Admin API Key"
                                  Variant="Variant.Outlined" InputType="InputType.Password"
                                  HelperText="Your Azure AI Search admin key" />

                    <MudDivider Class="my-2" />

                    <MudText Typo="Typo.subtitle1">Search Behavior</MudText>

                    <MudSwitch @bind-Value="_settings.EnableSemanticSearch" Label="Enable Semantic Search"
                               Color="Color.Primary" T="bool" />

                    <MudSwitch @bind-Value="_settings.EnableSearchHighlighting" Label="Enable Search Highlighting"
                               Color="Color.Primary" T="bool" />

                    <MudNumericField T="int" @bind-Value="_settings.DefaultSearchPageSize" Label="Default Results Per Page"
                                     Min="5" Max="100" Variant="Variant.Outlined" />

                    <MudStack Row="true" Spacing="2">
                        <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="@TestSearchConnection"
                                   StartIcon="@Icons.Material.Filled.NetworkCheck" Disabled="@_testingSearch">
                            @if (_testingSearch)
                            {
                                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                            }
                            Test Connection
                        </MudButton>
                        <MudButton Variant="Variant.Outlined" Color="Color.Secondary" OnClick="@ReindexArticles"
                                   StartIcon="@Icons.Material.Filled.Refresh" Disabled="@_reindexing">
                            @if (_reindexing)
                            {
                                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                            }
                            Reindex All Articles
                        </MudButton>
                    </MudStack>
                </MudStack>
            </MudCardContent>
        </MudCard>
    </MudTabPanel>

    @* Notifications *@
    <MudTabPanel Text="Notifications" Icon="@Icons.Material.Filled.Notifications">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Notification Settings</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack Spacing="4">
                    <MudSwitch @bind-Value="_settings.EmailNotificationsEnabled" Label="Enable Email Notifications"
                               Color="Color.Primary" T="bool" />

                    <MudText Typo="Typo.subtitle2" Class="mt-2">Notify on:</MudText>

                    <MudSwitch @bind-Value="_settings.NotifyOnNewArticle" Label="New Article Created"
                               Color="Color.Secondary" Disabled="@(!_settings.EmailNotificationsEnabled)" T="bool" />

                    <MudSwitch @bind-Value="_settings.NotifyOnArticlePublished" Label="Article Published"
                               Color="Color.Secondary" Disabled="@(!_settings.EmailNotificationsEnabled)" T="bool" />

                    <MudSwitch @bind-Value="_settings.NotifyOnReviewRequired" Label="Review Required"
                               Color="Color.Secondary" Disabled="@(!_settings.EmailNotificationsEnabled)" T="bool" />

                    <MudSwitch @bind-Value="_settings.NotifyOnFeedbackReceived" Label="Feedback Received"
                               Color="Color.Secondary" Disabled="@(!_settings.EmailNotificationsEnabled)" T="bool" />

                    <MudDivider Class="my-2" />

                    <MudTextField @bind-Value="_settings.NotificationEmailRecipients" Label="Notification Recipients"
                                  HelperText="Comma-separated email addresses"
                                  Variant="Variant.Outlined" Lines="2" />
                </MudStack>
            </MudCardContent>
        </MudCard>
    </MudTabPanel>

    @* User Preferences *@
    <MudTabPanel Text="Preferences" Icon="@Icons.Material.Filled.Person">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">User Preferences</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack Spacing="4">
                    <MudText Typo="Typo.subtitle1">Display</MudText>

                    <MudSelect T="string" @bind-Value="_settings.Theme" Label="Theme" Variant="Variant.Outlined">
                        <MudSelectItem T="string" Value="@("light")">Light</MudSelectItem>
                        <MudSelectItem T="string" Value="@("dark")">Dark</MudSelectItem>
                        <MudSelectItem T="string" Value="@("system")">System Default</MudSelectItem>
                    </MudSelect>

                    <MudSelect T="string" @bind-Value="_settings.DateFormat" Label="Date Format" Variant="Variant.Outlined">
                        <MudSelectItem T="string" Value="@("MM/dd/yyyy")">MM/dd/yyyy (US)</MudSelectItem>
                        <MudSelectItem T="string" Value="@("dd/MM/yyyy")">dd/MM/yyyy (UK)</MudSelectItem>
                        <MudSelectItem T="string" Value="@("yyyy-MM-dd")">yyyy-MM-dd (ISO)</MudSelectItem>
                    </MudSelect>

                    <MudNumericField T="int" @bind-Value="_settings.ItemsPerPage" Label="Default Items Per Page"
                                     Min="10" Max="100" Variant="Variant.Outlined" />

                    <MudDivider Class="my-2" />

                    <MudText Typo="Typo.subtitle1">Editor</MudText>

                    <MudSwitch @bind-Value="_settings.ShowLineNumbers" Label="Show Line Numbers in Editor"
                               Color="Color.Primary" T="bool" />

                    <MudSwitch @bind-Value="_settings.EnableSpellCheck" Label="Enable Spell Check"
                               Color="Color.Primary" T="bool" />

                    <MudSwitch @bind-Value="_settings.CompactView" Label="Compact View Mode"
                               Color="Color.Primary" T="bool" />
                </MudStack>
            </MudCardContent>
        </MudCard>
    </MudTabPanel>
</MudTabs>

@* Save Button *@
<MudPaper Class="pa-4 mt-4" Elevation="0">
    <MudStack Row="true" Justify="Justify.FlexEnd" Spacing="2">
        <MudButton Variant="Variant.Outlined" Color="Color.Default" OnClick="@ResetToDefaults">
            Reset to Defaults
        </MudButton>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@SaveSettings"
                   StartIcon="@Icons.Material.Filled.Save" Disabled="@_saving">
            @if (_saving)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
            }
            Save Settings
        </MudButton>
    </MudStack>
</MudPaper>

@code {
    private bool _saving = false;
    private bool _testingConnection = false;
    private bool _testingSearch = false;
    private bool _reindexing = false;

    private SettingsModel _settings = new();

    protected override void OnInitialized()
    {
        LoadSettings();
    }

    private void LoadSettings()
    {
        // TODO: Load settings from API
        _settings = new SettingsModel
        {
            ApplicationName = "MeshworksKB",
            SupportEmail = "<EMAIL>",
            DefaultLanguage = "en-US",
            Timezone = "UTC",
            SessionTimeoutMinutes = 60,

            RequireReviewBeforePublish = true,
            EnableVersionHistory = true,
            AutoSaveDrafts = true,
            AutoSaveIntervalSeconds = 30,
            DefaultArticleType = ArticleType.HowTo,
            DefaultDifficulty = Difficulty.Beginner,
            DefaultTags = "",

            EnableAIGeneration = true,
            EnableAIEvaluation = true,
            EnableAISuggestions = true,
            AzureOpenAIEndpoint = "",
            AzureOpenAIDeployment = "gpt-4o",
            AzureOpenAIApiKey = "",

            AzureSearchEndpoint = "",
            AzureSearchIndexName = "knowledgebase-articles",
            AzureSearchApiKey = "",
            EnableSemanticSearch = true,
            EnableSearchHighlighting = true,
            DefaultSearchPageSize = 20,

            EmailNotificationsEnabled = true,
            NotifyOnNewArticle = true,
            NotifyOnArticlePublished = true,
            NotifyOnReviewRequired = true,
            NotifyOnFeedbackReceived = false,
            NotificationEmailRecipients = "",

            Theme = "system",
            DateFormat = "MM/dd/yyyy",
            ItemsPerPage = 25,
            ShowLineNumbers = true,
            EnableSpellCheck = true,
            CompactView = false
        };
    }

    private async Task SaveSettings()
    {
        _saving = true;
        StateHasChanged();

        try
        {
            // TODO: Save settings to API
            await Task.Delay(500); // Simulate API call
            Snackbar.Add("Settings saved successfully", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error saving settings: {ex.Message}", Severity.Error);
        }

        _saving = false;
        StateHasChanged();
    }

    private async Task ResetToDefaults()
    {
        var result = await DialogService.ShowMessageBox(
            "Reset Settings",
            "Are you sure you want to reset all settings to their default values?",
            yesText: "Reset", cancelText: "Cancel");

        if (result == true)
        {
            LoadSettings();
            Snackbar.Add("Settings reset to defaults", Severity.Info);
        }
    }

    private async Task TestAIConnection()
    {
        _testingConnection = true;
        StateHasChanged();

        try
        {
            // TODO: Test Azure OpenAI connection
            await Task.Delay(1500); // Simulate API call

            if (string.IsNullOrEmpty(_settings.AzureOpenAIEndpoint) || string.IsNullOrEmpty(_settings.AzureOpenAIApiKey))
            {
                Snackbar.Add("Please provide endpoint and API key", Severity.Warning);
            }
            else
            {
                Snackbar.Add("AI connection successful", Severity.Success);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Connection failed: {ex.Message}", Severity.Error);
        }

        _testingConnection = false;
        StateHasChanged();
    }

    private async Task TestSearchConnection()
    {
        _testingSearch = true;
        StateHasChanged();

        try
        {
            // TODO: Test Azure Search connection
            await Task.Delay(1500); // Simulate API call

            if (string.IsNullOrEmpty(_settings.AzureSearchEndpoint) || string.IsNullOrEmpty(_settings.AzureSearchApiKey))
            {
                Snackbar.Add("Please provide endpoint and API key", Severity.Warning);
            }
            else
            {
                Snackbar.Add("Search connection successful", Severity.Success);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Connection failed: {ex.Message}", Severity.Error);
        }

        _testingSearch = false;
        StateHasChanged();
    }

    private async Task ReindexArticles()
    {
        var result = await DialogService.ShowMessageBox(
            "Reindex Articles",
            "This will rebuild the entire search index. This may take several minutes depending on the number of articles. Continue?",
            yesText: "Reindex", cancelText: "Cancel");

        if (result != true) return;

        _reindexing = true;
        StateHasChanged();

        try
        {
            var reindexResult = await ApiService.ReindexArticlesAsync();
            if (reindexResult != null)
            {
                Snackbar.Add(reindexResult.Message, Severity.Success);
            }
            else
            {
                Snackbar.Add("Reindex failed - no response from server", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Reindex failed: {ex.Message}", Severity.Error);
        }

        _reindexing = false;
        StateHasChanged();
    }

    public class SettingsModel
    {
        // General
        public string ApplicationName { get; set; } = "";
        public string SupportEmail { get; set; } = "";
        public string DefaultLanguage { get; set; } = "en-US";
        public string Timezone { get; set; } = "UTC";
        public int SessionTimeoutMinutes { get; set; } = 60;

        // Articles
        public bool RequireReviewBeforePublish { get; set; } = true;
        public bool EnableVersionHistory { get; set; } = true;
        public bool AutoSaveDrafts { get; set; } = true;
        public int AutoSaveIntervalSeconds { get; set; } = 30;
        public ArticleType DefaultArticleType { get; set; } = ArticleType.HowTo;
        public Difficulty DefaultDifficulty { get; set; } = Difficulty.Beginner;
        public string DefaultTags { get; set; } = "";

        // AI
        public bool EnableAIGeneration { get; set; } = true;
        public bool EnableAIEvaluation { get; set; } = true;
        public bool EnableAISuggestions { get; set; } = true;
        public string AzureOpenAIEndpoint { get; set; } = "";
        public string AzureOpenAIDeployment { get; set; } = "";
        public string AzureOpenAIApiKey { get; set; } = "";

        // Search
        public string AzureSearchEndpoint { get; set; } = "";
        public string AzureSearchIndexName { get; set; } = "";
        public string AzureSearchApiKey { get; set; } = "";
        public bool EnableSemanticSearch { get; set; } = true;
        public bool EnableSearchHighlighting { get; set; } = true;
        public int DefaultSearchPageSize { get; set; } = 20;

        // Notifications
        public bool EmailNotificationsEnabled { get; set; } = true;
        public bool NotifyOnNewArticle { get; set; } = true;
        public bool NotifyOnArticlePublished { get; set; } = true;
        public bool NotifyOnReviewRequired { get; set; } = true;
        public bool NotifyOnFeedbackReceived { get; set; } = false;
        public string NotificationEmailRecipients { get; set; } = "";

        // Preferences
        public string Theme { get; set; } = "system";
        public string DateFormat { get; set; } = "MM/dd/yyyy";
        public int ItemsPerPage { get; set; } = 25;
        public bool ShowLineNumbers { get; set; } = true;
        public bool EnableSpellCheck { get; set; } = true;
        public bool CompactView { get; set; } = false;
    }
}

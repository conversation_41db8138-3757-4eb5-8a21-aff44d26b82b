-- Knowledge Base Platform Database Indexes
-- Version: 1.0.0
-- Created: 2026-01-29

-- =============================================
-- Articles Indexes
-- =============================================

-- Index for filtering by status (most common filter)
CREATE NONCLUSTERED INDEX IX_Articles_Status
ON Articles(Status)
WHERE IsDeleted = 0;

-- Index for filtering by module
CREATE NONCLUSTERED INDEX IX_Articles_ModuleId
ON Articles(ModuleId)
WHERE IsDeleted = 0;

-- Index for filtering by article type
CREATE NONCLUSTERED INDEX IX_Articles_ArticleType
ON Articles(ArticleType)
WHERE IsDeleted = 0;

-- Index for filtering by category
CREATE NONCLUSTERED INDEX IX_Articles_Category
ON Articles(Category)
WHERE IsDeleted = 0;

-- Index for sorting by date
CREATE NONCLUSTERED INDEX IX_Articles_CreatedDate
ON Articles(CreatedDate DESC)
WHERE IsDeleted = 0;

-- Index for sorting by last modified
CREATE NONCLUSTERED INDEX IX_Articles_LastModifiedDate
ON Articles(LastModifiedDate DESC)
WHERE IsDeleted = 0;

-- Composite index for common query patterns
CREATE NONCLUSTERED INDEX IX_Articles_Status_ModuleId_Type
ON Articles(Status, ModuleId, ArticleType)
INCLUDE (Title, Summary, CreatedDate, ViewCount, HelpfulCount, NotHelpfulCount)
WHERE IsDeleted = 0;

-- Index for user's articles
CREATE NONCLUSTERED INDEX IX_Articles_CreatedByUserId
ON Articles(CreatedByUserId)
WHERE IsDeleted = 0;

-- =============================================
-- Modules Indexes
-- =============================================

CREATE NONCLUSTERED INDEX IX_Modules_ParentModuleId
ON Modules(ParentModuleId)
WHERE IsActive = 1;

CREATE NONCLUSTERED INDEX IX_Modules_SMEUserId
ON Modules(SMEUserId);

-- =============================================
-- Users Indexes
-- =============================================

CREATE NONCLUSTERED INDEX IX_Users_Role
ON Users(Role)
WHERE IsActive = 1;

-- =============================================
-- Related Articles Indexes
-- =============================================

CREATE NONCLUSTERED INDEX IX_RelatedArticles_RelatedArticleId
ON RelatedArticles(RelatedArticleId);

-- =============================================
-- Article Versions Indexes
-- =============================================

CREATE NONCLUSTERED INDEX IX_ArticleVersions_ArticleId
ON ArticleVersions(ArticleId, VersionNumber DESC);

-- =============================================
-- Article Reviews Indexes
-- =============================================

CREATE NONCLUSTERED INDEX IX_ArticleReviews_ArticleId
ON ArticleReviews(ArticleId, ReviewDate DESC);

CREATE NONCLUSTERED INDEX IX_ArticleReviews_ReviewerUserId
ON ArticleReviews(ReviewerUserId);

-- =============================================
-- Article Feedback Indexes
-- =============================================

CREATE NONCLUSTERED INDEX IX_ArticleFeedback_ArticleId
ON ArticleFeedback(ArticleId);

CREATE NONCLUSTERED INDEX IX_ArticleFeedback_UserId
ON ArticleFeedback(UserId)
WHERE UserId IS NOT NULL;

-- =============================================
-- Article Ticket References Indexes
-- =============================================

CREATE NONCLUSTERED INDEX IX_ArticleTicketReferences_ArticleId
ON ArticleTicketReferences(ArticleId);

-- =============================================
-- Audit Log Indexes
-- =============================================

CREATE NONCLUSTERED INDEX IX_AuditLog_EntityType_EntityId
ON AuditLog(EntityType, EntityId);

CREATE NONCLUSTERED INDEX IX_AuditLog_Timestamp
ON AuditLog(Timestamp DESC);

CREATE NONCLUSTERED INDEX IX_AuditLog_UserId
ON AuditLog(UserId)
WHERE UserId IS NOT NULL;

GO

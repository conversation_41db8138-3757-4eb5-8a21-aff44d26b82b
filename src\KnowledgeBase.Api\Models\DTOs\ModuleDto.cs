namespace KnowledgeBase.Api.Models.DTOs;

/// <summary>
/// Data transfer object for module display.
/// </summary>
public class ModuleDto
{
    public Guid ModuleId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? SMEName { get; set; }
    public Guid? SMEUserId { get; set; }
    public Guid? ParentModuleId { get; set; }
    public string? ParentModuleName { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public int ArticleCount { get; set; }
    public List<ModuleDto> ChildModules { get; set; } = new();
}

using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Enums;

namespace KnowledgeBase.Core.Interfaces;

/// <summary>
/// Service interface for article management operations.
/// </summary>
public interface IArticleService
{
    Task<Article?> GetByIdAsync(Guid articleId, CancellationToken cancellationToken = default);
    Task<(List<Article> Items, int TotalCount)> GetArticlesAsync(ArticleSearchCriteria criteria, CancellationToken cancellationToken = default);
    Task<Article> CreateAsync(Article article, Guid userId, CancellationToken cancellationToken = default);
    Task<Article> UpdateAsync(Article article, Guid userId, string? changeDescription = null, CancellationToken cancellationToken = default);
    Task<bool> DeleteAsync(Guid articleId, Guid userId, CancellationToken cancellationToken = default);
    Task<Article> PublishAsync(Guid articleId, Guid userId, string? changeDescription = null, CancellationToken cancellationToken = default);
    Task<Article> ArchiveAsync(Guid articleId, Guid userId, CancellationToken cancellationToken = default);
    Task SubmitFeedbackAsync(Guid articleId, Guid? userId, FeedbackType feedbackType, string? comments = null, string? sessionId = null, CancellationToken cancellationToken = default);
    Task IncrementViewCountAsync(Guid articleId, CancellationToken cancellationToken = default);
    Task<List<ArticleVersion>> GetVersionHistoryAsync(Guid articleId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Search criteria for filtering articles.
/// </summary>
public class ArticleSearchCriteria
{
    public string? Query { get; set; }
    public ArticleType? ArticleType { get; set; }
    public Guid? ModuleId { get; set; }
    public string? Category { get; set; }
    public ArticleStatus? Status { get; set; }
    public List<string>? Tags { get; set; }
    public Guid? CreatedByUserId { get; set; }
    public string SortBy { get; set; } = "LastModifiedDate";
    public bool SortDescending { get; set; } = true;
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

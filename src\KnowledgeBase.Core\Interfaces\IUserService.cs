using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Enums;

namespace KnowledgeBase.Core.Interfaces;

/// <summary>
/// Service interface for user management operations.
/// </summary>
public interface IUserService
{
    Task<User?> GetByIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<(List<User> Items, int TotalCount)> GetUsersAsync(int page = 1, int pageSize = 20, bool includeInactive = false, CancellationToken cancellationToken = default);
    Task<User> CreateOrUpdateAsync(User user, CancellationToken cancellationToken = default);
    Task<User> UpdateRoleAsync(Guid userId, UserRole role, CancellationToken cancellationToken = default);
    Task<bool> SetActiveStatusAsync(Guid userId, bool isActive, CancellationToken cancellationToken = default);
    Task UpdateLastLoginAsync(Guid userId, CancellationToken cancellationToken = default);
}

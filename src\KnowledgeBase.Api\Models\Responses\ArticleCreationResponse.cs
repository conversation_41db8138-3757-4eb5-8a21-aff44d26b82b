using KnowledgeBase.Api.Models.DTOs;

namespace KnowledgeBase.Api.Models.Responses;

/// <summary>
/// Response from AI-powered article creation.
/// </summary>
public class ArticleCreationResponse
{
    public bool Success { get; set; }

    /// <summary>
    /// The generated article (if successful).
    /// </summary>
    public ArticleDto? Article { get; set; }

    /// <summary>
    /// AI classification result.
    /// </summary>
    public ArticleClassificationResult? Classification { get; set; }

    /// <summary>
    /// AI evaluation of the generated article.
    /// </summary>
    public ArticleEvaluationResult? Evaluation { get; set; }

    /// <summary>
    /// If classification confidence is low, this message asks for clarification.
    /// </summary>
    public string? ClarificationNeeded { get; set; }

    /// <summary>
    /// Related articles found by the AI.
    /// </summary>
    public List<RelatedArticleDto> SuggestedRelatedArticles { get; set; } = new();
}

/// <summary>
/// Result of AI content classification.
/// </summary>
public class ArticleClassificationResult
{
    public string ArticleType { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public string Reasoning { get; set; } = string.Empty;
}

/// <summary>
/// Result of AI article evaluation.
/// </summary>
public class ArticleEvaluationResult
{
    public decimal OverallScore { get; set; }
    public Dictionary<string, decimal> Scores { get; set; } = new();
    public List<string> Strengths { get; set; } = new();
    public List<ArticleIssue> Issues { get; set; } = new();
    public List<string> RecommendedChanges { get; set; } = new();
    public string ReadabilityLevel { get; set; } = string.Empty;
    public int EstimatedReadTimeMinutes { get; set; }
}

/// <summary>
/// An issue identified in the article by the AI evaluator.
/// </summary>
public class ArticleIssue
{
    public string Severity { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Suggestion { get; set; } = string.Empty;
}

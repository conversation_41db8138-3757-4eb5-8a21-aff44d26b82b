{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=KnowledgeBase;Trusted_Connection=True;MultipleActiveResultSets=true"}, "AzureOpenAI": {"Endpoint": "https://aifoundry-meshworkskb-d-resource.services.ai.azure.com", "ApiKey": "1xFecBqbNgggHkTTD9LptLIRUyRk4PmlWJNzp947dNXyfSLOXbAjJQQJ99CBACL93NaXJ3w3AAAAACOGOzxr", "DeploymentNames": {"Default": "gpt-4.1", "Router": "gpt-4.1", "HowTo": "gpt-4.1", "Troubleshooting": "gpt-4.1", "ReleaseNote": "gpt-4.1", "Faq": "gpt-4.1", "ProductOverview": "gpt-4.1", "BestPractice": "gpt-4.1", "Evaluator": "gpt-4.1", "Embedding": "text-embedding-3-small"}}, "AllowedOrigins": "https://localhost:5001", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*"}
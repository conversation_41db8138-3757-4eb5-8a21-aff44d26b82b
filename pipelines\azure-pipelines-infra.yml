# Azure Pipeline for Infrastructure Deployment
# Deploys Azure resources using Bicep templates
#
# PREREQUISITES:
# 1. Create a Service Connection named 'Azure-ServiceConnection' (Azure Resource Manager)
# 2. Add pipeline variable 'SqlAdminPassword' (mark as secret)
#    Go to: Pipeline > Edit > Variables > New variable

trigger:
  branches:
    include:
      - main
  paths:
    include:
      - infrastructure/**

pr:
  branches:
    include:
      - main
  paths:
    include:
      - infrastructure/**

parameters:
  - name: environment
    displayName: 'Environment'
    type: string
    default: 'dev'
    values:
      - dev
      - staging
      - prod

  - name: location
    displayName: 'Azure Region'
    type: string
    default: 'australiaeast'

variables:
  - name: azureServiceConnection
    value: 'Azure-ServiceConnection'
  - name: resourceGroupName
    value: 'rg-meshworkskb-${{ parameters.environment }}'
  - name: templateFile
    value: '$(Build.SourcesDirectory)/infrastructure/main.bicep'

stages:
  - stage: Validate
    displayName: 'Validate Infrastructure'
    jobs:
      - job: ValidateBicep
        displayName: 'Validate Bicep Templates'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - checkout: self

          - task: AzureCLI@2
            displayName: 'Validate Bicep Syntax'
            inputs:
              azureSubscription: $(azureServiceConnection)
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az bicep build --file $(templateFile)

          - task: AzureCLI@2
            displayName: 'What-If Deployment'
            inputs:
              azureSubscription: $(azureServiceConnection)
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az group create --name $(resourceGroupName) --location ${{ parameters.location }} --output none || true

                # Use placeholder for what-if if password not set
                SQL_PASSWORD="${SQLADMINPASSWORD:-TempPassword123!}"

                az deployment group what-if \
                  --resource-group $(resourceGroupName) \
                  --template-file $(templateFile) \
                  --parameters environment=${{ parameters.environment }} \
                  --parameters location=${{ parameters.location }} \
                  --parameters sqlAdminPassword="$SQL_PASSWORD"
            env:
              SQLADMINPASSWORD: $(SqlAdminPassword)

  - stage: Deploy
    displayName: 'Deploy Infrastructure'
    dependsOn: Validate
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
    jobs:
      - job: DeployInfrastructure
        displayName: 'Deploy Azure Resources'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - checkout: self

          - task: AzureCLI@2
            displayName: 'Create Resource Group'
            inputs:
              azureSubscription: $(azureServiceConnection)
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az group create \
                  --name $(resourceGroupName) \
                  --location ${{ parameters.location }} \
                  --tags Environment=${{ parameters.environment }} Project=MeshworksKB

          - task: AzureCLI@2
            displayName: 'Deploy Bicep Template'
            inputs:
              azureSubscription: $(azureServiceConnection)
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                if [ -z "$SQLADMINPASSWORD" ]; then
                  echo "##vso[task.logissue type=error]SqlAdminPassword variable is not set."
                  echo "Go to: Pipeline > Edit > Variables > New variable"
                  echo "Add 'SqlAdminPassword' and mark it as secret."
                  exit 1
                fi

                az deployment group create \
                  --resource-group $(resourceGroupName) \
                  --template-file $(templateFile) \
                  --parameters environment=${{ parameters.environment }} \
                  --parameters location=${{ parameters.location }} \
                  --parameters sqlAdminPassword="$SQLADMINPASSWORD" \
                  --name "infra-$(Build.BuildNumber)" \
                  --output json > deployment-output.json

                # Extract and display outputs
                API_URL=$(jq -r '.properties.outputs.apiAppUrl.value // empty' deployment-output.json)
                WEB_URL=$(jq -r '.properties.outputs.webAppUrl.value // empty' deployment-output.json)

                echo "##vso[task.setvariable variable=apiAppUrl]$API_URL"
                echo "##vso[task.setvariable variable=webAppUrl]$WEB_URL"

                echo "Deployment completed successfully!"
                echo "API URL: $API_URL"
                echo "Web URL: $WEB_URL"
            env:
              SQLADMINPASSWORD: $(SqlAdminPassword)

          - task: AzureCLI@2
            displayName: 'Configure SQL Firewall'
            inputs:
              azureSubscription: $(azureServiceConnection)
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                SQL_SERVER=$(az sql server list --resource-group $(resourceGroupName) --query "[0].name" -o tsv)

                if [ -n "$SQL_SERVER" ]; then
                  az sql server firewall-rule create \
                    --resource-group $(resourceGroupName) \
                    --server $SQL_SERVER \
                    --name AllowAzureServices \
                    --start-ip-address 0.0.0.0 \
                    --end-ip-address 0.0.0.0 || true
                  echo "SQL firewall configured to allow Azure services"
                fi

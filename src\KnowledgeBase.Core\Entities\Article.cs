using System.ComponentModel.DataAnnotations;
using KnowledgeBase.Core.Enums;

namespace KnowledgeBase.Core.Entities;

/// <summary>
/// Represents a knowledge base article.
/// </summary>
public class Article
{
    [Key]
    public Guid ArticleId { get; set; }

    public ArticleType ArticleType { get; set; }

    [Required]
    [MaxLength(200)]
    public string Title { get; set; } = string.Empty;

    [Required]
    [MaxLength(500)]
    public string Summary { get; set; } = string.Empty;

    /// <summary>
    /// JSON content structure that varies by article type.
    /// </summary>
    [Required]
    public string Content { get; set; } = "{}";

    // Categorization
    public Guid ModuleId { get; set; }

    [Required]
    [MaxLength(50)]
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// JSON array of tags.
    /// </summary>
    [MaxLength(500)]
    public string? Tags { get; set; }

    // Metadata
    public int? EstimatedTimeMinutes { get; set; }

    public Difficulty? Difficulty { get; set; }

    /// <summary>
    /// JSON array of version strings this article applies to.
    /// </summary>
    [MaxLength(200)]
    public string? AppliesTo { get; set; }

    // Status & Workflow
    public ArticleStatus Status { get; set; } = ArticleStatus.Draft;

    // Ownership & Dates
    public Guid CreatedByUserId { get; set; }
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    public Guid LastModifiedByUserId { get; set; }
    public DateTime LastModifiedDate { get; set; } = DateTime.UtcNow;

    public DateTime? LastReviewedDate { get; set; }
    public Guid? LastReviewedByUserId { get; set; }

    // Engagement Metrics
    public int ViewCount { get; set; }
    public int HelpfulCount { get; set; }
    public int NotHelpfulCount { get; set; }

    /// <summary>
    /// Calculated rating: HelpfulCount / (HelpfulCount + NotHelpfulCount)
    /// </summary>
    public decimal? Rating => (HelpfulCount + NotHelpfulCount) > 0
        ? (decimal)HelpfulCount / (HelpfulCount + NotHelpfulCount)
        : null;

    // Versioning
    public int Version { get; set; } = 1;

    // Soft Delete
    public bool IsDeleted { get; set; }
    public DateTime? DeletedDate { get; set; }
    public Guid? DeletedByUserId { get; set; }

    // Navigation properties
    public virtual Module Module { get; set; } = null!;
    public virtual User CreatedBy { get; set; } = null!;
    public virtual User LastModifiedBy { get; set; } = null!;
    public virtual User? LastReviewedBy { get; set; }
    public virtual User? DeletedBy { get; set; }

    public virtual ICollection<ArticleTicketReference> TicketReferences { get; set; } = new List<ArticleTicketReference>();
    public virtual ICollection<RelatedArticle> RelatedArticles { get; set; } = new List<RelatedArticle>();
    public virtual ICollection<RelatedArticle> RelatedTo { get; set; } = new List<RelatedArticle>();
    public virtual ICollection<ArticleVersion> Versions { get; set; } = new List<ArticleVersion>();
    public virtual ICollection<ArticleReview> Reviews { get; set; } = new List<ArticleReview>();
    public virtual ICollection<ArticleFeedback> Feedback { get; set; } = new List<ArticleFeedback>();
}

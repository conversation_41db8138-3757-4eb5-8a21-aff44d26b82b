@page "/modules"
@rendermode InteractiveServer
@using KnowledgeBase.Web.Services
@using KnowledgeBase.Web.Services.Models
@inject KnowledgeBaseApiService ApiService
@inject IDialogService DialogService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>Modules - Knowledge Base</PageTitle>

<MudText Typo="Typo.h4" Class="mb-4">Module Management</MudText>

@* Actions Bar *@
<MudPaper Class="pa-4 mb-4">
    <MudGrid>
        <MudItem xs="12" md="6">
            <MudTextField @bind-Value="_searchText" Label="Search Modules" Placeholder="Search by name..."
                          Adornment="Adornment.Start" AdornmentIcon="@Icons.Material.Filled.Search"
                          Immediate="true" DebounceInterval="300" OnDebounceIntervalElapsed="@OnSearchChanged" />
        </MudItem>
        <MudItem xs="12" md="3">
            <MudSwitch Value="_showInactive" ValueChanged="@((bool val) => OnShowInactiveChanged(val))"
                       Label="Show Inactive" Color="Color.Primary" Class="mt-2" T="bool" />
        </MudItem>
        <MudItem xs="12" md="3" Class="d-flex align-end justify-end">
            <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                       OnClick="OpenCreateDialog">
                Create Module
            </MudButton>
        </MudItem>
    </MudGrid>
</MudPaper>

@* Stats Cards *@
<MudGrid Class="mb-4">
    <MudItem xs="12" sm="4">
        <MudPaper Class="pa-4 d-flex align-center">
            <MudIcon Icon="@Icons.Material.Filled.Folder" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
            <div>
                <MudText Typo="Typo.h5">@_allModules.Count</MudText>
                <MudText Typo="Typo.body2" Color="Color.Secondary">Total Modules</MudText>
            </div>
        </MudPaper>
    </MudItem>
    <MudItem xs="12" sm="4">
        <MudPaper Class="pa-4 d-flex align-center">
            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Large" Class="mr-3" />
            <div>
                <MudText Typo="Typo.h5">@_allModules.Count(m => m.IsActive)</MudText>
                <MudText Typo="Typo.body2" Color="Color.Secondary">Active Modules</MudText>
            </div>
        </MudPaper>
    </MudItem>
    <MudItem xs="12" sm="4">
        <MudPaper Class="pa-4 d-flex align-center">
            <MudIcon Icon="@Icons.Material.Filled.Article" Color="Color.Info" Size="Size.Large" Class="mr-3" />
            <div>
                <MudText Typo="Typo.h5">@_allModules.Sum(m => m.ArticleCount)</MudText>
                <MudText Typo="Typo.body2" Color="Color.Secondary">Total Articles</MudText>
            </div>
        </MudPaper>
    </MudItem>
</MudGrid>

@* Modules Table *@
<MudTable Items="@_filteredModules" Hover="true" Striped="true" Loading="@_loading"
          LoadingProgressColor="Color.Primary" Dense="true" T="ModuleDto">
    <HeaderContent>
        <MudTh><MudTableSortLabel SortBy="new Func<ModuleDto, object>(x => x.DisplayOrder)">Order</MudTableSortLabel></MudTh>
        <MudTh><MudTableSortLabel SortBy="new Func<ModuleDto, object>(x => x.Name)" InitialDirection="SortDirection.Ascending">Name</MudTableSortLabel></MudTh>
        <MudTh>Description</MudTh>
        <MudTh><MudTableSortLabel SortBy="new Func<ModuleDto, object>(x => x.ParentModuleName ?? string.Empty)">Parent</MudTableSortLabel></MudTh>
        <MudTh><MudTableSortLabel SortBy="new Func<ModuleDto, object>(x => x.ArticleCount)">Articles</MudTableSortLabel></MudTh>
        <MudTh>SME</MudTh>
        <MudTh>Status</MudTh>
        <MudTh Style="width: 120px;">Actions</MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd DataLabel="Order">
            <MudText Typo="Typo.body2">@context.DisplayOrder</MudText>
        </MudTd>
        <MudTd DataLabel="Name">
            <div class="d-flex align-center">
                <MudIcon Icon="@Icons.Material.Filled.Folder" Size="Size.Small" Class="mr-2"
                         Color="@(context.IsActive ? Color.Primary : Color.Default)" />
                <MudText Typo="Typo.body1" Class="@(!context.IsActive ? "text-disabled" : "")">
                    @context.Name
                </MudText>
            </div>
        </MudTd>
        <MudTd DataLabel="Description">
            <MudText Typo="Typo.body2" Class="text-truncate" Style="max-width: 250px;">
                @(context.Description ?? "-")
            </MudText>
        </MudTd>
        <MudTd DataLabel="Parent">
            @if (!string.IsNullOrEmpty(context.ParentModuleName))
            {
                <MudChip T="string" Size="Size.Small" Variant="Variant.Outlined">@context.ParentModuleName</MudChip>
            }
            else
            {
                <MudText Typo="Typo.body2" Color="Color.Secondary">Root</MudText>
            }
        </MudTd>
        <MudTd DataLabel="Articles">
            <MudChip T="string" Size="Size.Small" Color="Color.Info">@context.ArticleCount</MudChip>
        </MudTd>
        <MudTd DataLabel="SME">
            @if (!string.IsNullOrEmpty(context.SMEName))
            {
                <MudTooltip Text="@context.SMEName">
                    <MudAvatar Size="Size.Small" Color="Color.Secondary">@context.SMEName[0]</MudAvatar>
                </MudTooltip>
            }
            else
            {
                <MudText Typo="Typo.body2" Color="Color.Secondary">-</MudText>
            }
        </MudTd>
        <MudTd DataLabel="Status">
            @if (context.IsActive)
            {
                <MudChip T="string" Size="Size.Small" Color="Color.Success" Icon="@Icons.Material.Filled.CheckCircle">Active</MudChip>
            }
            else
            {
                <MudChip T="string" Size="Size.Small" Color="Color.Default" Icon="@Icons.Material.Filled.Cancel">Inactive</MudChip>
            }
        </MudTd>
        <MudTd>
            <MudButtonGroup Size="Size.Small" Variant="Variant.Text">
                <MudIconButton Icon="@Icons.Material.Filled.Edit" Color="Color.Primary"
                               OnClick="@(() => OpenEditDialog(context))" />
                <MudIconButton Icon="@Icons.Material.Filled.Article" Color="Color.Info"
                               OnClick="@(() => ViewArticles(context))" />
                @if (context.IsActive)
                {
                    <MudIconButton Icon="@Icons.Material.Filled.Block" Color="Color.Warning"
                                   OnClick="@(() => ToggleStatus(context, false))" />
                }
                else
                {
                    <MudIconButton Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success"
                                   OnClick="@(() => ToggleStatus(context, true))" />
                }
            </MudButtonGroup>
        </MudTd>
    </RowTemplate>
    <NoRecordsContent>
        <MudText Class="pa-4">No modules found. Create your first module to get started.</MudText>
    </NoRecordsContent>
    <PagerContent>
        <MudTablePager PageSizeOptions="new int[] { 10, 25, 50 }" />
    </PagerContent>
</MudTable>

@* Create/Edit Dialog *@
<MudDialog @bind-Visible="_dialogVisible" Options="_dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@(_editingModule == null ? Icons.Material.Filled.Add : Icons.Material.Filled.Edit)" Class="mr-2" />
            @(_editingModule == null ? "Create Module" : "Edit Module")
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudForm @ref="_form" @bind-IsValid="_formValid">
            <MudGrid>
                <MudItem xs="12">
                    <MudTextField @bind-Value="_formModel.Name" Label="Module Name" Required="true"
                                  RequiredError="Name is required" MaxLength="100"
                                  Counter="100" Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="_formModel.Description" Label="Description"
                                  Lines="3" MaxLength="500" Counter="500" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect T="Guid?" @bind-Value="_formModel.ParentModuleId" Label="Parent Module" Clearable="true">
                        <MudSelectItem T="Guid?" Value="@((Guid?)null)">None (Root Module)</MudSelectItem>
                        @foreach (var module in _allModules.Where(m => m.ModuleId != _editingModule?.ModuleId))
                        {
                            <MudSelectItem T="Guid?" Value="@((Guid?)module.ModuleId)">@module.Name</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="_formModel.DisplayOrder" Label="Display Order"
                                     Min="0" Max="999" />
                </MudItem>
                @if (_editingModule != null)
                {
                    <MudItem xs="12">
                        <MudSwitch @bind-Value="_formModel.IsActive" Label="Active" Color="Color.Success" T="bool" />
                    </MudItem>
                }
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseDialog" Variant="Variant.Text">Cancel</MudButton>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="SaveModule"
                   Disabled="@(!_formValid || _saving)">
            @if (_saving)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
            }
            @(_editingModule == null ? "Create" : "Save")
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    private bool _loading = true;
    private bool _saving = false;
    private string _searchText = "";
    private bool _showInactive = false;

    private List<ModuleDto> _allModules = new();
    private List<ModuleDto> _filteredModules = new();

    // Dialog state
    private bool _dialogVisible = false;
    private DialogOptions _dialogOptions = new() { MaxWidth = MaxWidth.Small, FullWidth = true };
    private MudForm? _form;
    private bool _formValid = false;
    private ModuleDto? _editingModule = null;
    private ModuleFormModel _formModel = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadModules();
    }

    private async Task LoadModules()
    {
        _loading = true;
        StateHasChanged();

        try
        {
            _allModules = await ApiService.GetModulesAsync(includeInactive: true);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading modules: {ex.Message}", Severity.Error);
        }

        ApplyFilters();
        _loading = false;
        StateHasChanged();
    }

    private void ApplyFilters()
    {
        var filtered = _allModules.AsEnumerable();

        if (!_showInactive)
        {
            filtered = filtered.Where(m => m.IsActive);
        }

        if (!string.IsNullOrWhiteSpace(_searchText))
        {
            var search = _searchText.ToLower();
            filtered = filtered.Where(m =>
                m.Name.ToLower().Contains(search) ||
                (m.Description?.ToLower().Contains(search) ?? false));
        }

        _filteredModules = filtered.OrderBy(m => m.DisplayOrder).ThenBy(m => m.Name).ToList();
    }

    private void OnSearchChanged(string value)
    {
        _searchText = value;
        ApplyFilters();
    }

    private async Task OnShowInactiveChanged(bool value)
    {
        _showInactive = value;
        ApplyFilters();
        StateHasChanged();
    }

    private void OpenCreateDialog()
    {
        _editingModule = null;
        _formModel = new ModuleFormModel
        {
            DisplayOrder = _allModules.Count > 0 ? _allModules.Max(m => m.DisplayOrder) + 1 : 1,
            IsActive = true
        };
        _dialogVisible = true;
        StateHasChanged();
    }

    private void OpenEditDialog(ModuleDto module)
    {
        _editingModule = module;
        _formModel = new ModuleFormModel
        {
            Name = module.Name,
            Description = module.Description,
            ParentModuleId = module.ParentModuleId,
            DisplayOrder = module.DisplayOrder,
            IsActive = module.IsActive
        };
        _dialogVisible = true;
        StateHasChanged();
    }

    private void CloseDialog()
    {
        _dialogVisible = false;
        _editingModule = null;
        StateHasChanged();
    }

    private async Task SaveModule()
    {
        if (_form == null) return;

        await _form.Validate();
        if (!_formValid) return;

        _saving = true;
        StateHasChanged();

        try
        {
            if (_editingModule == null)
            {
                // Create new module
                var request = new CreateModuleRequest
                {
                    Name = _formModel.Name,
                    Description = _formModel.Description,
                    ParentModuleId = _formModel.ParentModuleId,
                    DisplayOrder = _formModel.DisplayOrder
                };

                var created = await ApiService.CreateModuleAsync(request);
                if (created != null)
                {
                    _allModules.Add(created);
                    Snackbar.Add($"Module '{created.Name}' created successfully", Severity.Success);
                }
                else
                {
                    Snackbar.Add("Failed to create module", Severity.Error);
                }
            }
            else
            {
                // Update existing module
                var request = new UpdateModuleRequest
                {
                    Name = _formModel.Name,
                    Description = _formModel.Description,
                    ParentModuleId = _formModel.ParentModuleId,
                    DisplayOrder = _formModel.DisplayOrder,
                    IsActive = _formModel.IsActive
                };

                var updated = await ApiService.UpdateModuleAsync(_editingModule.ModuleId, request);
                if (updated != null)
                {
                    // Update local copy
                    var index = _allModules.FindIndex(m => m.ModuleId == _editingModule.ModuleId);
                    if (index >= 0) _allModules[index] = updated;
                    Snackbar.Add($"Module '{updated.Name}' updated successfully", Severity.Success);
                }
                else
                {
                    Snackbar.Add("Failed to update module", Severity.Error);
                }
            }

            ApplyFilters();
            CloseDialog();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error saving module: {ex.Message}", Severity.Error);
        }

        _saving = false;
        StateHasChanged();
    }

    private async Task ToggleStatus(ModuleDto module, bool isActive)
    {
        try
        {
            var success = await ApiService.SetModuleStatusAsync(module.ModuleId, isActive);
            if (success)
            {
                module.IsActive = isActive;
                ApplyFilters();

                var statusText = isActive ? "activated" : "deactivated";
                Snackbar.Add($"Module '{module.Name}' {statusText}", Severity.Success);
            }
            else
            {
                Snackbar.Add("Failed to update module status", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error updating module status: {ex.Message}", Severity.Error);
        }
    }

    private void ViewArticles(ModuleDto module)
    {
        Navigation.NavigateTo($"/articles?moduleId={module.ModuleId}");
    }

    public class ModuleFormModel
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public Guid? ParentModuleId { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; } = true;
    }
}

using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Enums;
using KnowledgeBase.Core.Interfaces;
using KnowledgeBase.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace KnowledgeBase.Infrastructure.Services;

/// <summary>
/// Service implementation for user management operations.
/// </summary>
public class UserService : IUserService
{
    private readonly KnowledgeBaseContext _context;
    private readonly ILogger<UserService> _logger;

    public UserService(KnowledgeBaseContext context, ILogger<UserService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<User?> GetByIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.Users.FindAsync(new object[] { userId }, cancellationToken);
    }

    public async Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        return await _context.Users
            .FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower(), cancellationToken);
    }

    public async Task<(List<User> Items, int TotalCount)> GetUsersAsync(
        int page = 1,
        int pageSize = 20,
        bool includeInactive = false,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Users.AsQueryable();

        if (!includeInactive)
        {
            query = query.Where(u => u.IsActive);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var items = await query
            .OrderBy(u => u.Name)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<User> CreateOrUpdateAsync(User user, CancellationToken cancellationToken = default)
    {
        var existing = await GetByEmailAsync(user.Email, cancellationToken);

        if (existing != null)
        {
            existing.Name = user.Name;
            existing.LastLoginDate = DateTime.UtcNow;
            await _context.SaveChangesAsync(cancellationToken);
            return existing;
        }

        user.UserId = Guid.NewGuid();
        user.CreatedDate = DateTime.UtcNow;
        user.IsActive = true;

        _context.Users.Add(user);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Created user {UserId}: {Email}", user.UserId, user.Email);

        return user;
    }

    public async Task<User> UpdateRoleAsync(Guid userId, UserRole role, CancellationToken cancellationToken = default)
    {
        var user = await _context.Users.FindAsync(new object[] { userId }, cancellationToken);
        if (user == null)
        {
            throw new InvalidOperationException($"User {userId} not found.");
        }

        user.Role = role;
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Updated user {UserId} role to {Role}", userId, role);

        return user;
    }

    public async Task<bool> SetActiveStatusAsync(Guid userId, bool isActive, CancellationToken cancellationToken = default)
    {
        var user = await _context.Users.FindAsync(new object[] { userId }, cancellationToken);
        if (user == null)
        {
            return false;
        }

        user.IsActive = isActive;
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Set user {UserId} active status to {IsActive}", userId, isActive);

        return true;
    }

    public async Task UpdateLastLoginAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        await _context.Users
            .Where(u => u.UserId == userId)
            .ExecuteUpdateAsync(s => s.SetProperty(u => u.LastLoginDate, DateTime.UtcNow), cancellationToken);
    }
}

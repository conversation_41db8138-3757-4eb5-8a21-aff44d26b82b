using System.Text.Json;
using KnowledgeBase.Core.Enums;
using KnowledgeBase.Core.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace KnowledgeBase.Infrastructure.Services.Agents;

/// <summary>
/// Evaluation agent for assessing article quality.
/// </summary>
public class EvaluationAgentService : IEvaluationAgentService
{
    private readonly IAzureOpenAIClient _openAIClient;
    private readonly ILogger<EvaluationAgentService> _logger;
    private readonly string _deploymentName;

    public EvaluationAgentService(
        IAzureOpenAIClient openAIClient,
        IConfiguration configuration,
        ILogger<EvaluationAgentService> logger)
    {
        _openAIClient = openAIClient;
        _logger = logger;
        _deploymentName = configuration["AzureOpenAI:DeploymentNames:Evaluator"] ?? "gpt-4";
    }

    public async Task<EvaluationResult> EvaluateAsync(
        string title,
        string summary,
        string content,
        ArticleType articleType,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Evaluating article: {Title}", title);

        var userMessage = $@"Please evaluate this {articleType} article:

Title: {title}

Summary: {summary}

Content:
{content}";

        var response = await _openAIClient.GetChatCompletionAsync(
            _deploymentName,
            AgentPrompts.EvaluationAgent,
            userMessage,
            temperature: 0.3f,
            maxTokens: 2000,
            cancellationToken);

        if (!response.Success)
        {
            _logger.LogError("Evaluation agent failed: {Error}", response.ErrorMessage);
            return CreateDefaultEvaluation();
        }

        try
        {
            var json = ExtractJson(response.Content);
            using var doc = JsonDocument.Parse(json);
            var root = doc.RootElement;

            var result = new EvaluationResult
            {
                OverallScore = root.GetProperty("overall_score").GetDecimal(),
                ReadabilityLevel = root.TryGetProperty("readability_level", out var rl)
                    ? rl.GetString() ?? "intermediate"
                    : "intermediate",
                EstimatedReadTimeMinutes = root.TryGetProperty("estimated_read_time_minutes", out var rt)
                    ? rt.GetInt32()
                    : 5
            };

            // Parse scores
            if (root.TryGetProperty("scores", out var scores))
            {
                foreach (var score in scores.EnumerateObject())
                {
                    result.Scores[score.Name] = score.Value.GetDecimal();
                }
            }

            // Parse strengths
            if (root.TryGetProperty("strengths", out var strengths))
            {
                foreach (var item in strengths.EnumerateArray())
                {
                    var value = item.GetString();
                    if (!string.IsNullOrEmpty(value))
                        result.Strengths.Add(value);
                }
            }

            // Parse issues
            if (root.TryGetProperty("issues", out var issues))
            {
                foreach (var item in issues.EnumerateArray())
                {
                    result.Issues.Add(new EvaluationIssue
                    {
                        Severity = item.TryGetProperty("severity", out var sev) ? sev.GetString() ?? "minor" : "minor",
                        Category = item.TryGetProperty("category", out var cat) ? cat.GetString() ?? "" : "",
                        Description = item.TryGetProperty("description", out var desc) ? desc.GetString() ?? "" : "",
                        Suggestion = item.TryGetProperty("suggestion", out var sug) ? sug.GetString() ?? "" : ""
                    });
                }
            }

            // Parse recommended changes
            if (root.TryGetProperty("recommended_changes", out var changes))
            {
                foreach (var item in changes.EnumerateArray())
                {
                    var value = item.GetString();
                    if (!string.IsNullOrEmpty(value))
                        result.RecommendedChanges.Add(value);
                }
            }

            _logger.LogInformation("Evaluation complete. Score: {Score}/10", result.OverallScore);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to parse evaluation response");
            return CreateDefaultEvaluation();
        }
    }

    private static EvaluationResult CreateDefaultEvaluation()
    {
        return new EvaluationResult
        {
            OverallScore = 5,
            Scores = new Dictionary<string, decimal>
            {
                ["completeness"] = 5,
                ["clarity"] = 5,
                ["accuracy"] = 5,
                ["actionability"] = 5,
                ["structure"] = 5,
                ["seo"] = 5
            },
            Strengths = new List<string> { "Unable to evaluate" },
            Issues = new List<EvaluationIssue>
            {
                new() { Severity = "minor", Category = "system", Description = "Evaluation failed", Suggestion = "Please try again" }
            }
        };
    }

    private static string ExtractJson(string content)
    {
        content = content.Trim();
        if (content.StartsWith("```json"))
            content = content[7..];
        else if (content.StartsWith("```"))
            content = content[3..];
        if (content.EndsWith("```"))
            content = content[..^3];
        return content.Trim();
    }
}

@page "/analytics"
@rendermode InteractiveServer

<PageTitle>Analytics - Knowledge Base</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium" Class="mt-8">
    <MudPaper Class="pa-8 text-center" Elevation="2">
        <MudIcon Icon="@Icons.Material.Filled.Analytics" Size="Size.Large" Color="Color.Primary"
                 Style="font-size: 80px;" Class="mb-4" />

        <MudText Typo="Typo.h4" Class="mb-2">Analytics Dashboard</MudText>

        <MudChip T="string" Color="Color.Warning" Size="Size.Large" Class="mb-4">
            Coming Soon
        </MudChip>

        <MudText Typo="Typo.body1" Color="Color.Secondary" Class="mb-4">
            We're working on a powerful analytics dashboard that will help you understand
            how your knowledge base content is performing.
        </MudText>

        <MudDivider Class="my-4" />

        <MudText Typo="Typo.h6" Class="mb-3">Planned Features</MudText>

        <MudGrid Justify="Justify.Center">
            <MudItem xs="12" sm="6" md="4">
                <MudPaper Class="pa-4" Elevation="0">
                    <MudIcon Icon="@Icons.Material.Filled.Visibility" Color="Color.Info" Class="mb-2" />
                    <MudText Typo="Typo.subtitle1">Article Views</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        Track which articles are most viewed
                    </MudText>
                </MudPaper>
            </MudItem>
            <MudItem xs="12" sm="6" md="4">
                <MudPaper Class="pa-4" Elevation="0">
                    <MudIcon Icon="@Icons.Material.Filled.Search" Color="Color.Info" Class="mb-2" />
                    <MudText Typo="Typo.subtitle1">Search Metrics</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        See what users are searching for
                    </MudText>
                </MudPaper>
            </MudItem>
            <MudItem xs="12" sm="6" md="4">
                <MudPaper Class="pa-4" Elevation="0">
                    <MudIcon Icon="@Icons.Material.Filled.ThumbUp" Color="Color.Info" Class="mb-2" />
                    <MudText Typo="Typo.subtitle1">User Feedback</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        Analyze helpful/not helpful ratings
                    </MudText>
                </MudPaper>
            </MudItem>
            <MudItem xs="12" sm="6" md="4">
                <MudPaper Class="pa-4" Elevation="0">
                    <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Color="Color.Info" Class="mb-2" />
                    <MudText Typo="Typo.subtitle1">Content Trends</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        Identify trending topics over time
                    </MudText>
                </MudPaper>
            </MudItem>
            <MudItem xs="12" sm="6" md="4">
                <MudPaper Class="pa-4" Elevation="0">
                    <MudIcon Icon="@Icons.Material.Filled.People" Color="Color.Info" Class="mb-2" />
                    <MudText Typo="Typo.subtitle1">Author Stats</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        Track contributions by author
                    </MudText>
                </MudPaper>
            </MudItem>
            <MudItem xs="12" sm="6" md="4">
                <MudPaper Class="pa-4" Elevation="0">
                    <MudIcon Icon="@Icons.Material.Filled.BugReport" Color="Color.Info" Class="mb-2" />
                    <MudText Typo="Typo.subtitle1">Content Gaps</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        Identify missing documentation
                    </MudText>
                </MudPaper>
            </MudItem>
        </MudGrid>

        <MudDivider Class="my-4" />

        <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/" StartIcon="@Icons.Material.Filled.Home">
            Return to Dashboard
        </MudButton>
    </MudPaper>
</MudContainer>

namespace KnowledgeBase.Core.Enums;

/// <summary>
/// User roles for authorization.
/// </summary>
public enum UserRole
{
    /// <summary>
    /// Read-only access to published articles.
    /// </summary>
    Guest,

    /// <summary>
    /// Can create and edit own articles.
    /// </summary>
    User,

    /// <summary>
    /// Can review and approve articles, manage modules.
    /// </summary>
    PowerUser,

    /// <summary>
    /// Full administrative access.
    /// </summary>
    Admin
}

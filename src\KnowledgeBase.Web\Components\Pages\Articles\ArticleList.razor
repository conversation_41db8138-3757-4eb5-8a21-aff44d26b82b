@page "/articles"
@page "/articles/drafts"
@page "/articles/review"
@rendermode InteractiveServer
@using KnowledgeBase.Web.Services
@using KnowledgeBase.Web.Services.Models
@inject KnowledgeBaseApiService ApiService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar

<PageTitle>Articles - Knowledge Base</PageTitle>

<MudText Typo="Typo.h4" Class="mb-4">@GetPageTitle()</MudText>

@* Filters *@
<MudPaper Class="pa-4 mb-4">
    <MudGrid>
        <MudItem xs="12" md="4">
            <MudTextField @bind-Value="_searchText" Label="Search" Placeholder="Search articles..."
                          Adornment="Adornment.Start" AdornmentIcon="@Icons.Material.Filled.Search"
                          Immediate="true" DebounceInterval="300" OnDebounceIntervalElapsed="@OnSearchChanged" />
        </MudItem>
        <MudItem xs="12" md="2">
            <MudSelect T="string" Value="_selectedType" ValueChanged="OnTypeChanged" Label="Article Type" Clearable="true">
                <MudSelectItem T="string" Value="@((string?)null)">All Types</MudSelectItem>
                @foreach (var type in ArticleTypes)
                {
                    <MudSelectItem T="string" Value="@type">@type</MudSelectItem>
                }
            </MudSelect>
        </MudItem>
        <MudItem xs="12" md="2">
            <MudSelect T="string" Value="_selectedStatus" ValueChanged="OnStatusChanged" Label="Status" Clearable="true">
                <MudSelectItem T="string" Value="@((string?)null)">All Statuses</MudSelectItem>
                @foreach (var status in ArticleStatuses)
                {
                    <MudSelectItem T="string" Value="@status">@status</MudSelectItem>
                }
            </MudSelect>
        </MudItem>
        <MudItem xs="12" md="2">
            <MudSelect T="string" Value="_sortBy" ValueChanged="OnSortChanged" Label="Sort By">
                <MudSelectItem T="string" Value="@("updated")">Recently Updated</MudSelectItem>
                <MudSelectItem T="string" Value="@("created")">Recently Created</MudSelectItem>
                <MudSelectItem T="string" Value="@("title")">Title (A-Z)</MudSelectItem>
                <MudSelectItem T="string" Value="@("views")">Most Viewed</MudSelectItem>
            </MudSelect>
        </MudItem>
        <MudItem xs="12" md="2" Class="d-flex align-end">
            <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                       Href="/articles/create" FullWidth="true">
                Create Article
            </MudButton>
        </MudItem>
    </MudGrid>
</MudPaper>

@* Results Count *@
<MudText Typo="Typo.body2" Class="mb-2">
    Showing @_articles.Count of @_totalCount articles
</MudText>

@* Articles Table *@
<MudTable Items="@_articles" Hover="true" Striped="true" Loading="@_loading" LoadingProgressColor="Color.Primary"
          SortLabel="Sort By" OnRowClick="@OnRowClicked" T="ArticleSummaryDto" RowClass="cursor-pointer">
    <ToolBarContent>
        <MudSpacer />
        <MudToggleIconButton @bind-Toggled="@_showTableView"
                             Icon="@Icons.Material.Filled.ViewList" ToggledIcon="@Icons.Material.Filled.GridView"
                             Color="Color.Default" ToggledColor="Color.Primary" />
    </ToolBarContent>
    <HeaderContent>
        <MudTh><MudTableSortLabel SortBy="new Func<ArticleSummaryDto, object>(x => x.Title)">Title</MudTableSortLabel></MudTh>
        <MudTh><MudTableSortLabel SortBy="new Func<ArticleSummaryDto, object>(x => x.ArticleType)">Type</MudTableSortLabel></MudTh>
        <MudTh><MudTableSortLabel SortBy="new Func<ArticleSummaryDto, object>(x => x.Status)">Status</MudTableSortLabel></MudTh>
        <MudTh><MudTableSortLabel SortBy="new Func<ArticleSummaryDto, object>(x => x.Category)">Category</MudTableSortLabel></MudTh>
        <MudTh><MudTableSortLabel SortBy="new Func<ArticleSummaryDto, object>(x => x.LastModifiedDate)">Updated</MudTableSortLabel></MudTh>
        <MudTh><MudTableSortLabel SortBy="new Func<ArticleSummaryDto, object>(x => x.ViewCount)">Views</MudTableSortLabel></MudTh>
        <MudTh Style="width: 100px;">Actions</MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd DataLabel="Title">
            <div class="d-flex flex-column">
                <MudText Typo="Typo.body1" Class="font-weight-medium">@context.Title</MudText>
                @if (!string.IsNullOrEmpty(context.Summary))
                {
                    <MudText Typo="Typo.caption" Color="Color.Secondary" Class="text-truncate" Style="max-width: 400px;">
                        @context.Summary
                    </MudText>
                }
            </div>
        </MudTd>
        <MudTd DataLabel="Type">
            <MudChip T="string" Size="Size.Small" Color="@GetArticleTypeColor(context.ArticleType)" Icon="@GetArticleTypeIcon(context.ArticleType)">
                @context.ArticleType
            </MudChip>
        </MudTd>
        <MudTd DataLabel="Status">
            <MudChip T="string" Size="Size.Small" Color="@GetStatusColor(context.Status)">
                @context.Status
            </MudChip>
        </MudTd>
        <MudTd DataLabel="Category">@(context.Category ?? "-")</MudTd>
        <MudTd DataLabel="Updated">@context.LastModifiedDate.ToLocalTime().ToString("MMM dd, yyyy")</MudTd>
        <MudTd DataLabel="Views">@context.ViewCount</MudTd>
        <MudTd>
            <MudMenu Icon="@Icons.Material.Filled.MoreVert" Size="Size.Small" AnchorOrigin="Origin.BottomLeft">
                <MudMenuItem Icon="@Icons.Material.Filled.Visibility" OnClick="@(() => ViewArticle(context))">View</MudMenuItem>
                <MudMenuItem Icon="@Icons.Material.Filled.Edit" OnClick="@(() => EditArticle(context))">Edit</MudMenuItem>
                @if (context.Status == "Draft")
                {
                    <MudMenuItem Icon="@Icons.Material.Filled.Send" OnClick="@(() => SubmitForReview(context))">Submit for Review</MudMenuItem>
                }
                @if (context.Status == "InReview")
                {
                    <MudMenuItem Icon="@Icons.Material.Filled.Publish" OnClick="@(() => PublishArticle(context))">Publish</MudMenuItem>
                }
                <MudDivider />
                <MudMenuItem Icon="@Icons.Material.Filled.Delete" IconColor="Color.Error" OnClick="@(() => DeleteArticle(context))">Delete</MudMenuItem>
            </MudMenu>
        </MudTd>
    </RowTemplate>
    <NoRecordsContent>
        <MudText>No articles found matching your criteria.</MudText>
    </NoRecordsContent>
    <PagerContent>
        <MudTablePager PageSizeOptions="new int[] { 10, 25, 50, 100 }" />
    </PagerContent>
</MudTable>

@code {
    private bool _loading = true;
    private bool _showTableView = true;
    private string _searchText = "";
    private string? _selectedType;
    private string? _selectedStatus;
    private string _sortBy = "updated";
    private Guid? _moduleId;

    private List<ArticleSummaryDto> _articles = new();
    private int _totalCount = 0;
    private int _currentPage = 1;
    private int _pageSize = 20;

    private static readonly string[] ArticleTypes = { "HowTo", "Troubleshooting", "ReleaseNote", "Faq", "ProductOverview", "BestPractice" };
    private static readonly string[] ArticleStatuses = { "Draft", "InReview", "Published", "Archived" };

    protected override async Task OnInitializedAsync()
    {
        // Parse URL to determine which filter to apply
        var uri = new Uri(Navigation.Uri);
        var path = uri.AbsolutePath.ToLower();

        if (path.Contains("/drafts"))
        {
            _selectedStatus = "Draft";
        }
        else if (path.Contains("/review"))
        {
            _selectedStatus = "InReview";
        }

        // Check for query parameters
        var query = System.Web.HttpUtility.ParseQueryString(uri.Query);
        var typeParam = query["type"];
        if (!string.IsNullOrEmpty(typeParam))
        {
            _selectedType = typeParam;
        }

        var moduleParam = query["moduleId"];
        if (!string.IsNullOrEmpty(moduleParam) && Guid.TryParse(moduleParam, out var moduleId))
        {
            _moduleId = moduleId;
        }

        await LoadArticles();
    }

    private async Task LoadArticles()
    {
        _loading = true;
        StateHasChanged();

        try
        {
            var response = await ApiService.GetArticlesAsync(
                page: _currentPage,
                pageSize: _pageSize,
                status: _selectedStatus,
                articleType: _selectedType,
                moduleId: _moduleId,
                sortBy: _sortBy,
                sortDescending: _sortBy != "title");

            _articles = response.Items;
            _totalCount = response.TotalCount;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading articles: {ex.Message}", Severity.Error);
            _articles = new();
        }

        _loading = false;
        StateHasChanged();
    }

    private async Task OnSearchChanged(string value)
    {
        _searchText = value;
        _currentPage = 1;
        await LoadArticles();
    }

    private async Task OnTypeChanged(string? value)
    {
        _selectedType = value;
        _currentPage = 1;
        await LoadArticles();
    }

    private async Task OnStatusChanged(string? value)
    {
        _selectedStatus = value;
        _currentPage = 1;
        await LoadArticles();
    }

    private async Task OnSortChanged(string value)
    {
        _sortBy = value;
        await LoadArticles();
    }

    private void OnRowClicked(TableRowClickEventArgs<ArticleSummaryDto> args)
    {
        Navigation.NavigateTo($"/articles/{args.Item.ArticleId}");
    }

    private void ViewArticle(ArticleSummaryDto article)
    {
        Navigation.NavigateTo($"/articles/{article.ArticleId}");
    }

    private void EditArticle(ArticleSummaryDto article)
    {
        Navigation.NavigateTo($"/articles/{article.ArticleId}/edit");
    }

    private async Task SubmitForReview(ArticleSummaryDto article)
    {
        var success = await ApiService.UpdateArticleStatusAsync(article.ArticleId, "InReview");
        if (success)
        {
            Snackbar.Add("Article submitted for review", Severity.Success);
            await LoadArticles();
        }
        else
        {
            Snackbar.Add("Failed to submit article for review", Severity.Error);
        }
    }

    private async Task PublishArticle(ArticleSummaryDto article)
    {
        var success = await ApiService.UpdateArticleStatusAsync(article.ArticleId, "Published");
        if (success)
        {
            Snackbar.Add("Article published successfully", Severity.Success);
            await LoadArticles();
        }
        else
        {
            Snackbar.Add("Failed to publish article", Severity.Error);
        }
    }

    private async Task DeleteArticle(ArticleSummaryDto article)
    {
        var success = await ApiService.DeleteArticleAsync(article.ArticleId);
        if (success)
        {
            Snackbar.Add("Article deleted", Severity.Warning);
            await LoadArticles();
        }
        else
        {
            Snackbar.Add("Failed to delete article", Severity.Error);
        }
    }

    private string GetPageTitle()
    {
        var uri = new Uri(Navigation.Uri);
        var path = uri.AbsolutePath.ToLower();

        if (path.Contains("/drafts")) return "My Drafts";
        if (path.Contains("/review")) return "Pending Review";

        if (!string.IsNullOrEmpty(_selectedType)) return $"{_selectedType} Articles";

        return "All Articles";
    }

    private string GetArticleTypeIcon(string type) => type switch
    {
        "HowTo" => Icons.Material.Filled.MenuBook,
        "Troubleshooting" => Icons.Material.Filled.BugReport,
        "ReleaseNote" => Icons.Material.Filled.NewReleases,
        "Faq" or "FAQ" => Icons.Material.Filled.QuestionAnswer,
        "ProductOverview" => Icons.Material.Filled.Inventory,
        "BestPractice" => Icons.Material.Filled.Star,
        _ => Icons.Material.Filled.Article
    };

    private Color GetArticleTypeColor(string type) => type switch
    {
        "HowTo" => Color.Primary,
        "Troubleshooting" => Color.Error,
        "ReleaseNote" => Color.Info,
        "Faq" or "FAQ" => Color.Secondary,
        "ProductOverview" => Color.Tertiary,
        "BestPractice" => Color.Warning,
        _ => Color.Default
    };

    private Color GetStatusColor(string status) => status switch
    {
        "Draft" => Color.Default,
        "InReview" => Color.Warning,
        "Published" => Color.Success,
        "Archived" => Color.Dark,
        _ => Color.Default
    };
}

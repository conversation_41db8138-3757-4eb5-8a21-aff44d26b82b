using KnowledgeBase.Api.Models.DTOs;
using KnowledgeBase.Api.Models.Requests;
using KnowledgeBase.Api.Models.Responses;
using KnowledgeBase.Core.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace KnowledgeBase.Api.Controllers;

/// <summary>
/// API endpoints for searching articles.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class SearchController : ControllerBase
{
    private readonly ISearchService _searchService;
    private readonly ILogger<SearchController> _logger;

    public SearchController(
        ISearchService searchService,
        ILogger<SearchController> logger)
    {
        _searchService = searchService;
        _logger = logger;
    }

    /// <summary>
    /// Full-text search for articles.
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResponse<ArticleSummaryDto>>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<PagedResponse<ArticleSummaryDto>>>> Search(
        [FromQuery] SearchRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Searching articles with query: {Query}", request.Query);

        var query = new SearchQuery
        {
            Query = request.Query ?? "",
            ArticleType = request.ArticleType,
            ModuleId = request.ModuleId,
            Category = request.Category,
            Status = request.Status ?? "published",
            Tags = string.IsNullOrEmpty(request.Tags) ? null : request.Tags.Split(',').ToList(),
            SortBy = request.SortBy ?? "relevance",
            SortDescending = request.SortDirection?.Equals("desc", StringComparison.OrdinalIgnoreCase) ?? true,
            Page = request.Page,
            PageSize = Math.Min(request.PageSize, 100)
        };

        var (items, totalCount) = await _searchService.SearchAsync(query, cancellationToken);

        var dtos = items.Select(r => new ArticleSummaryDto
        {
            ArticleId = r.ArticleId,
            Title = r.Title,
            Summary = r.Summary,
            ArticleType = r.ArticleType,
            ModuleName = r.ModuleName,
            Category = r.Category,
            Tags = r.Tags,
            LastModifiedDate = r.LastModifiedDate,
            ViewCount = r.ViewCount,
            Rating = r.Rating
        }).ToList();

        var response = PagedResponse<ArticleSummaryDto>.Create(
            dtos,
            totalCount,
            request.Page,
            request.PageSize
        );

        return Ok(ApiResponse<PagedResponse<ArticleSummaryDto>>.Ok(response));
    }

    /// <summary>
    /// Get search suggestions/autocomplete.
    /// </summary>
    [HttpGet("suggest")]
    [ProducesResponseType(typeof(ApiResponse<List<string>>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<List<string>>>> GetSuggestions(
        [FromQuery] string q,
        [FromQuery] int maxResults = 10,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(q))
            return Ok(ApiResponse<List<string>>.Ok(new List<string>()));

        _logger.LogInformation("Getting suggestions for: {Query}", q);

        var suggestions = await _searchService.GetSuggestionsAsync(q, maxResults, cancellationToken);
        return Ok(ApiResponse<List<string>>.Ok(suggestions));
    }

    /// <summary>
    /// Rebuild the search index (admin only).
    /// </summary>
    [HttpPost("reindex")]
    [ProducesResponseType(typeof(ApiResponse<ReindexResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ReindexResponse>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<ReindexResponse>>> ReindexAll(
        [FromServices] IArticleService articleService,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting full reindex of articles");

        try
        {
            // Ensure index exists
            await _searchService.EnsureIndexExistsAsync(cancellationToken);

            // Get all articles (use a large page size)
            var criteria = new ArticleSearchCriteria
            {
                PageSize = 10000 // Get all articles
            };
            var (articles, totalCount) = await articleService.GetArticlesAsync(criteria, cancellationToken);

            // Index all articles
            await _searchService.IndexArticlesAsync(articles, cancellationToken);

            _logger.LogInformation("Reindexed {Count} articles", totalCount);

            return Ok(ApiResponse<ReindexResponse>.Ok(new ReindexResponse
            {
                ArticlesIndexed = totalCount,
                Message = $"Successfully reindexed {totalCount} articles"
            }));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during reindex");
            return StatusCode(500, ApiResponse<ReindexResponse>.Fail($"Error during reindex: {ex.Message}"));
        }
    }
}

public class ReindexResponse
{
    public int ArticlesIndexed { get; set; }
    public string Message { get; set; } = string.Empty;
}

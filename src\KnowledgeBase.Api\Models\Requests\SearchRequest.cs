namespace KnowledgeBase.Api.Models.Requests;

/// <summary>
/// Request model for searching articles.
/// </summary>
public class SearchRequest
{
    /// <summary>
    /// Search query string.
    /// </summary>
    public string? Query { get; set; }

    /// <summary>
    /// Filter by article type.
    /// </summary>
    public string? ArticleType { get; set; }

    /// <summary>
    /// Filter by module.
    /// </summary>
    public Guid? ModuleId { get; set; }

    /// <summary>
    /// Filter by category.
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Filter by status. If not specified, returns all statuses.
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Filter by tags (comma-separated).
    /// </summary>
    public string? Tags { get; set; }

    /// <summary>
    /// Sort by field (date, rating, views).
    /// </summary>
    public string SortBy { get; set; } = "date";

    /// <summary>
    /// Sort direction (asc, desc).
    /// </summary>
    public string SortDirection { get; set; } = "desc";

    /// <summary>
    /// Page number (1-based).
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// Page size (default 20, max 100).
    /// </summary>
    public int PageSize { get; set; } = 20;
}

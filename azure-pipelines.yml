# Azure DevOps CI/CD Pipeline for MeshworksKB
# Builds, tests, and deploys to Azure App Service

trigger:
  branches:
    include:
      - main
      - develop
  paths:
    exclude:
      - '*.md'
      - 'docs/*'

pr:
  branches:
    include:
      - main
      - develop

variables:
  # Build configuration
  buildConfiguration: 'Release'
  dotnetVersion: '8.x'

  # Project paths
  apiProject: 'src/KnowledgeBase.Api/KnowledgeBase.Api.csproj'
  webProject: 'src/KnowledgeBase.Web/KnowledgeBase.Web.csproj'
  testProjects: 'tests/**/*.csproj'

  # Azure resources (set these in pipeline variables or variable groups)
  # azureSubscription: 'your-service-connection'
  # apiAppServiceName: 'meshworks-kb-api'
  # webAppServiceName: 'meshworks-kb-web'

stages:
  # ============================================
  # BUILD STAGE
  # ============================================
  - stage: Build
    displayName: 'Build & Test'
    jobs:
      - job: BuildAndTest
        displayName: 'Build and Test'
        pool:
          vmImage: 'windows-latest'

        steps:
          - task: UseDotNet@2
            displayName: 'Install .NET SDK'
            inputs:
              packageType: 'sdk'
              version: '$(dotnetVersion)'

          - task: DotNetCoreCLI@2
            displayName: 'Restore NuGet packages'
            inputs:
              command: 'restore'
              projects: '**/*.csproj'

          - task: DotNetCoreCLI@2
            displayName: 'Build solution'
            inputs:
              command: 'build'
              projects: '**/*.csproj'
              arguments: '--configuration $(buildConfiguration) --no-restore'

          - task: DotNetCoreCLI@2
            displayName: 'Run unit tests'
            inputs:
              command: 'test'
              projects: '$(testProjects)'
              arguments: '--configuration $(buildConfiguration) --no-build --collect:"XPlat Code Coverage" --results-directory $(Agent.TempDirectory)/TestResults'
            continueOnError: false

          - task: PublishCodeCoverageResults@2
            displayName: 'Publish code coverage'
            inputs:
              summaryFileLocation: '$(Agent.TempDirectory)/TestResults/**/coverage.cobertura.xml'
            condition: succeededOrFailed()

          # Publish API
          - task: DotNetCoreCLI@2
            displayName: 'Publish API'
            inputs:
              command: 'publish'
              publishWebProjects: false
              projects: '$(apiProject)'
              arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/api --no-build'
              zipAfterPublish: true

          # Publish Web
          - task: DotNetCoreCLI@2
            displayName: 'Publish Web'
            inputs:
              command: 'publish'
              publishWebProjects: false
              projects: '$(webProject)'
              arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/web --no-build'
              zipAfterPublish: true

          # Publish SQL scripts
          - task: CopyFiles@2
            displayName: 'Copy SQL scripts'
            inputs:
              SourceFolder: 'infrastructure/sql'
              Contents: '**/*'
              TargetFolder: '$(Build.ArtifactStagingDirectory)/sql'

          # Publish artifacts
          - task: PublishBuildArtifacts@1
            displayName: 'Publish API artifact'
            inputs:
              PathtoPublish: '$(Build.ArtifactStagingDirectory)/api'
              ArtifactName: 'api'

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Web artifact'
            inputs:
              PathtoPublish: '$(Build.ArtifactStagingDirectory)/web'
              ArtifactName: 'web'

          - task: PublishBuildArtifacts@1
            displayName: 'Publish SQL artifact'
            inputs:
              PathtoPublish: '$(Build.ArtifactStagingDirectory)/sql'
              ArtifactName: 'sql'

  # ============================================
  # DEPLOY TO DEV
  # ============================================
  - stage: DeployDev
    displayName: 'Deploy to Dev'
    dependsOn: Build
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
    variables:
      - group: 'meshworks-kb-dev'  # Variable group with dev settings
    jobs:
      - deployment: DeployApiDev
        displayName: 'Deploy API to Dev'
        environment: 'meshworks-kb-dev'
        pool:
          vmImage: 'windows-latest'
        strategy:
          runOnce:
            deploy:
              steps:
                - task: AzureWebApp@1
                  displayName: 'Deploy API to Azure App Service'
                  inputs:
                    azureSubscription: '$(azureSubscription)'
                    appType: 'webApp'
                    appName: '$(apiAppServiceName)'
                    package: '$(Pipeline.Workspace)/api/*.zip'
                    deploymentMethod: 'auto'

      - deployment: DeployWebDev
        displayName: 'Deploy Web to Dev'
        environment: 'meshworks-kb-dev'
        pool:
          vmImage: 'windows-latest'
        strategy:
          runOnce:
            deploy:
              steps:
                - task: AzureWebApp@1
                  displayName: 'Deploy Web to Azure App Service'
                  inputs:
                    azureSubscription: '$(azureSubscription)'
                    appType: 'webApp'
                    appName: '$(webAppServiceName)'
                    package: '$(Pipeline.Workspace)/web/*.zip'
                    deploymentMethod: 'auto'

  # ============================================
  # DEPLOY TO PRODUCTION
  # ============================================
  - stage: DeployProd
    displayName: 'Deploy to Production'
    dependsOn: Build
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
    variables:
      - group: 'meshworks-kb-prod'  # Variable group with prod settings
    jobs:
      - deployment: DeployApiProd
        displayName: 'Deploy API to Production'
        environment: 'meshworks-kb-prod'
        pool:
          vmImage: 'windows-latest'
        strategy:
          runOnce:
            deploy:
              steps:
                - task: AzureWebApp@1
                  displayName: 'Deploy API to Azure App Service'
                  inputs:
                    azureSubscription: '$(azureSubscription)'
                    appType: 'webApp'
                    appName: '$(apiAppServiceName)'
                    package: '$(Pipeline.Workspace)/api/*.zip'
                    deploymentMethod: 'auto'

      - deployment: DeployWebProd
        displayName: 'Deploy Web to Production'
        environment: 'meshworks-kb-prod'
        pool:
          vmImage: 'windows-latest'
        strategy:
          runOnce:
            deploy:
              steps:
                - task: AzureWebApp@1
                  displayName: 'Deploy Web to Azure App Service'
                  inputs:
                    azureSubscription: '$(azureSubscription)'
                    appType: 'webApp'
                    appName: '$(webAppServiceName)'
                    package: '$(Pipeline.Workspace)/web/*.zip'
                    deploymentMethod: 'auto'

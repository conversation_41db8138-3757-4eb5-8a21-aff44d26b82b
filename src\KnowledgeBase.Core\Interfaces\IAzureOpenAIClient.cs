namespace KnowledgeBase.Core.Interfaces;

/// <summary>
/// Client interface for Azure OpenAI operations.
/// </summary>
public interface IAzureOpenAIClient
{
    /// <summary>
    /// Get a chat completion from Azure OpenAI.
    /// </summary>
    Task<ChatCompletionResult> GetChatCompletionAsync(
        string deploymentName,
        string systemPrompt,
        string userMessage,
        float temperature = 0.7f,
        int maxTokens = 2000,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate embeddings for text.
    /// </summary>
    Task<float[]> GetEmbeddingsAsync(
        string text,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Result from a chat completion request.
/// </summary>
public class ChatCompletionResult
{
    public bool Success { get; set; }
    public string Content { get; set; } = string.Empty;
    public int PromptTokens { get; set; }
    public int CompletionTokens { get; set; }
    public int TotalTokens { get; set; }
    public string? ErrorMessage { get; set; }
    public string? FinishReason { get; set; }
}

using System.Net.Http.Json;
using System.Text.Json;
using KnowledgeBase.Web.Services.Models;

namespace KnowledgeBase.Web.Services;

/// <summary>
/// Service for communicating with the Knowledge Base API.
/// </summary>
public class KnowledgeBaseApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<KnowledgeBaseApiService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public KnowledgeBaseApiService(HttpClient httpClient, ILogger<KnowledgeBaseApiService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };
    }

    #region Modules

    public async Task<List<ModuleDto>> GetModulesAsync(bool includeInactive = false)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<List<ModuleDto>>>(
                $"api/modules?includeInactive={includeInactive}", _jsonOptions);
            return response?.Data ?? new List<ModuleDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching modules");
            return new List<ModuleDto>();
        }
    }

    public async Task<ModuleDto?> GetModuleAsync(Guid moduleId)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<ModuleDto>>(
                $"api/modules/{moduleId}", _jsonOptions);
            return response?.Data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching module {ModuleId}", moduleId);
            return null;
        }
    }

    public async Task<ModuleDto?> CreateModuleAsync(CreateModuleRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("api/modules", request);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<ModuleDto>>(_jsonOptions);
                return result?.Data;
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating module");
            return null;
        }
    }

    public async Task<ModuleDto?> UpdateModuleAsync(Guid moduleId, UpdateModuleRequest request)
    {
        try
        {
            var response = await _httpClient.PutAsJsonAsync($"api/modules/{moduleId}", request);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<ModuleDto>>(_jsonOptions);
                return result?.Data;
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating module {ModuleId}", moduleId);
            return null;
        }
    }

    public async Task<bool> SetModuleStatusAsync(Guid moduleId, bool isActive)
    {
        try
        {
            var response = await _httpClient.PatchAsJsonAsync(
                $"api/modules/{moduleId}/status",
                new { IsActive = isActive });
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting module status {ModuleId}", moduleId);
            return false;
        }
    }

    #endregion

    #region Articles

    public async Task<PagedResponse<ArticleSummaryDto>> GetArticlesAsync(
        int page = 1,
        int pageSize = 20,
        string? status = null,
        string? articleType = null,
        Guid? moduleId = null,
        string? sortBy = null,
        bool sortDescending = true)
    {
        try
        {
            var url = $"api/articles?page={page}&pageSize={pageSize}";
            if (!string.IsNullOrEmpty(status)) url += $"&status={status}";
            if (!string.IsNullOrEmpty(articleType)) url += $"&articleType={articleType}";
            if (moduleId.HasValue) url += $"&moduleId={moduleId}";
            if (!string.IsNullOrEmpty(sortBy)) url += $"&sortBy={sortBy}&sortDirection={(sortDescending ? "desc" : "asc")}";

            var response = await _httpClient.GetFromJsonAsync<ApiResponse<PagedResponse<ArticleSummaryDto>>>(
                url, _jsonOptions);
            return response?.Data ?? new PagedResponse<ArticleSummaryDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching articles");
            return new PagedResponse<ArticleSummaryDto>();
        }
    }

    public async Task<ArticleDto?> GetArticleAsync(Guid articleId)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<ArticleDto>>(
                $"api/articles/{articleId}", _jsonOptions);
            return response?.Data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching article {ArticleId}", articleId);
            return null;
        }
    }

    public async Task<ArticleDto?> CreateArticleAsync(CreateArticleRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("api/articles/save", request);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<ArticleDto>>(_jsonOptions);
                return result?.Data;
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating article");
            return null;
        }
    }

    public async Task<ArticleDto?> SaveArticleAsync(SaveArticleRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("api/articles/save", request);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<ArticleDto>>(_jsonOptions);
                return result?.Data;
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving article");
            return null;
        }
    }

    public async Task<ArticleDto?> UpdateArticleAsync(Guid articleId, UpdateArticleRequest request)
    {
        try
        {
            var response = await _httpClient.PutAsJsonAsync($"api/articles/{articleId}", request);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<ArticleDto>>(_jsonOptions);
                return result?.Data;
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating article {ArticleId}", articleId);
            return null;
        }
    }

    public async Task<bool> UpdateArticleStatusAsync(Guid articleId, string status)
    {
        try
        {
            var response = await _httpClient.PatchAsJsonAsync(
                $"api/articles/{articleId}/status",
                new { Status = status });
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating article status {ArticleId}", articleId);
            return false;
        }
    }

    public async Task<bool> DeleteArticleAsync(Guid articleId)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"api/articles/{articleId}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting article {ArticleId}", articleId);
            return false;
        }
    }

    public async Task<bool> RecordFeedbackAsync(Guid articleId, bool isHelpful, string? comments = null)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync(
                $"api/articles/{articleId}/feedback",
                new { FeedbackType = isHelpful ? "helpful" : "nothelpful", Comments = comments });
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording feedback for {ArticleId}", articleId);
            return false;
        }
    }

    public async Task<List<ArticleVersionDto>> GetArticleVersionsAsync(Guid articleId)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<List<ArticleVersionDto>>>(
                $"api/articles/{articleId}/versions", _jsonOptions);
            return response?.Data ?? new List<ArticleVersionDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching article versions for {ArticleId}", articleId);
            return new List<ArticleVersionDto>();
        }
    }

    #endregion

    #region Search

    public async Task<PagedResponse<SearchResultDto>> SearchAsync(
        string query,
        int page = 1,
        int pageSize = 20,
        string? articleType = null,
        Guid? moduleId = null,
        string? category = null,
        string? sortBy = "relevance")
    {
        try
        {
            var url = $"api/search?query={Uri.EscapeDataString(query)}&page={page}&pageSize={pageSize}";
            if (!string.IsNullOrEmpty(articleType)) url += $"&articleType={articleType}";
            if (moduleId.HasValue) url += $"&moduleId={moduleId}";
            if (!string.IsNullOrEmpty(category)) url += $"&category={category}";
            if (!string.IsNullOrEmpty(sortBy)) url += $"&sortBy={sortBy}";

            var response = await _httpClient.GetFromJsonAsync<ApiResponse<PagedResponse<SearchResultDto>>>(
                url, _jsonOptions);
            return response?.Data ?? new PagedResponse<SearchResultDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching articles");
            return new PagedResponse<SearchResultDto>();
        }
    }

    public async Task<List<string>> GetSuggestionsAsync(string query, int maxResults = 10)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<List<string>>>(
                $"api/search/suggest?q={Uri.EscapeDataString(query)}&maxResults={maxResults}", _jsonOptions);
            return response?.Data ?? new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching suggestions");
            return new List<string>();
        }
    }

    public async Task<ReindexResponse?> ReindexArticlesAsync()
    {
        try
        {
            var response = await _httpClient.PostAsync("api/search/reindex", null);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<ReindexResponse>>(_jsonOptions);
                return result?.Data;
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reindexing articles");
            return null;
        }
    }

    #endregion

    #region Dashboard

    public async Task<DashboardStats> GetDashboardStatsAsync()
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<DashboardStats>>(
                "api/dashboard/stats", _jsonOptions);
            return response?.Data ?? new DashboardStats();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching dashboard stats");
            return new DashboardStats();
        }
    }

    public async Task<List<ArticleSummaryDto>> GetRecentArticlesAsync(int count = 5)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<List<ArticleSummaryDto>>>(
                $"api/dashboard/recent?count={count}", _jsonOptions);
            return response?.Data ?? new List<ArticleSummaryDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching recent articles");
            return new List<ArticleSummaryDto>();
        }
    }

    #endregion

    #region AI Generation

    public async Task<GeneratedArticleResult?> GenerateArticleAsync(GenerateArticleRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("api/articles/generate", request);
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<GeneratedArticleResult>>(_jsonOptions);
                return result?.Data;
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating article");
            return null;
        }
    }

    #endregion
}

#region Request Models

public class CreateModuleRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public Guid? SMEUserId { get; set; }
    public Guid? ParentModuleId { get; set; }
    public int DisplayOrder { get; set; }
}

public class UpdateModuleRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public Guid? SMEUserId { get; set; }
    public Guid? ParentModuleId { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
}

public class CreateArticleRequest
{
    public string ArticleType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public string Content { get; set; } = "{}";
    public Guid ModuleId { get; set; }
    public string Category { get; set; } = string.Empty;
    public List<string>? Tags { get; set; }
    public int? EstimatedTimeMinutes { get; set; }
    public string? Difficulty { get; set; }
}

public class UpdateArticleRequest
{
    public string? Title { get; set; }
    public string? Summary { get; set; }
    public string? Content { get; set; }
    public Guid? ModuleId { get; set; }
    public string? Category { get; set; }
    public List<string>? Tags { get; set; }
    public int? EstimatedTimeMinutes { get; set; }
    public string? Difficulty { get; set; }
}

public class GenerateArticleRequest
{
    public string Content { get; set; } = string.Empty;
    public Guid ModuleId { get; set; }
    public string? ArticleType { get; set; }
}

public class SaveArticleRequest
{
    public string ArticleType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public object Content { get; set; } = new { };
    public Guid ModuleId { get; set; }
    public string Category { get; set; } = string.Empty;
    public List<string>? Tags { get; set; }
    public int? EstimatedTimeMinutes { get; set; }
    public string? Difficulty { get; set; }
    public List<string>? AppliesTo { get; set; }
}

public class GeneratedArticleResult
{
    public ArticleDto Article { get; set; } = new();
    public ClassificationResult? Classification { get; set; }
    public EvaluationResult? Evaluation { get; set; }
}

public class ClassificationResult
{
    public string ArticleType { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public string Reasoning { get; set; } = string.Empty;
}

public class EvaluationResult
{
    public decimal OverallScore { get; set; }
    public Dictionary<string, decimal> Scores { get; set; } = new();
    public List<string> Strengths { get; set; } = new();
    public List<string> RecommendedChanges { get; set; } = new();
}

#endregion

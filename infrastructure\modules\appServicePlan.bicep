// App Service Plan module

@description('Name of the App Service Plan')
param name string

@description('Location for the resource')
param location string

@description('App Service Plan SKU')
@allowed(['F1', 'B1', 'B2', 'S1', 'S2', 'P1v2', 'P2v2', 'P3v2'])
param sku string = 'B1'

var skuMapping = {
  F1: {
    name: 'F1'
    tier: 'Free'
    size: 'F1'
    family: 'F'
    capacity: 1
  }
  B1: {
    name: 'B1'
    tier: 'Basic'
    size: 'B1'
    family: 'B'
    capacity: 1
  }
  B2: {
    name: 'B2'
    tier: 'Basic'
    size: 'B2'
    family: 'B'
    capacity: 1
  }
  S1: {
    name: 'S1'
    tier: 'Standard'
    size: 'S1'
    family: 'S'
    capacity: 1
  }
  S2: {
    name: 'S2'
    tier: 'Standard'
    size: 'S2'
    family: 'S'
    capacity: 1
  }
  P1v2: {
    name: 'P1v2'
    tier: 'PremiumV2'
    size: 'P1v2'
    family: 'Pv2'
    capacity: 1
  }
  P2v2: {
    name: 'P2v2'
    tier: 'PremiumV2'
    size: 'P2v2'
    family: 'Pv2'
    capacity: 1
  }
  P3v2: {
    name: 'P3v2'
    tier: 'PremiumV2'
    size: 'P3v2'
    family: 'Pv2'
    capacity: 1
  }
}

resource appServicePlan 'Microsoft.Web/serverfarms@2023-01-01' = {
  name: name
  location: location
  sku: {
    name: skuMapping[sku].name
    tier: skuMapping[sku].tier
    size: skuMapping[sku].size
    family: skuMapping[sku].family
    capacity: skuMapping[sku].capacity
  }
  kind: 'linux'
  properties: {
    reserved: true // Linux
  }
}

output id string = appServicePlan.id
output name string = appServicePlan.name

using KnowledgeBase.Core.Enums;

namespace KnowledgeBase.Api.Models.DTOs;

/// <summary>
/// Data transfer object for article display.
/// </summary>
public class ArticleDto
{
    public Guid ArticleId { get; set; }
    public string ArticleType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public object? Content { get; set; }

    public Guid ModuleId { get; set; }
    public string ModuleName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();

    public int? EstimatedTimeMinutes { get; set; }
    public string? Difficulty { get; set; }
    public List<string> AppliesTo { get; set; } = new();

    public string Status { get; set; } = string.Empty;

    public string CreatedByName { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public string LastModifiedByName { get; set; } = string.Empty;
    public DateTime LastModifiedDate { get; set; }

    public int ViewCount { get; set; }
    public int HelpfulCount { get; set; }
    public int NotHelpfulCount { get; set; }
    public decimal? Rating { get; set; }

    public int Version { get; set; }

    public List<RelatedArticleDto> RelatedArticles { get; set; } = new();
    public List<TicketReferenceDto> TicketReferences { get; set; } = new();
}

/// <summary>
/// Lightweight article DTO for list views.
/// </summary>
public class ArticleSummaryDto
{
    public Guid ArticleId { get; set; }
    public string ArticleType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public string ModuleName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public DateTime LastModifiedDate { get; set; }
    public int ViewCount { get; set; }
    public decimal? Rating { get; set; }
}

public class RelatedArticleDto
{
    public Guid ArticleId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string RelationshipType { get; set; } = string.Empty;
    public decimal? RelevanceScore { get; set; }
}

public class TicketReferenceDto
{
    public string TicketId { get; set; } = string.Empty;
    public string TicketSystem { get; set; } = string.Empty;
    public string? TicketUrl { get; set; }
}

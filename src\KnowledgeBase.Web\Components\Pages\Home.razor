@page "/"
@rendermode InteractiveServer
@using KnowledgeBase.Web.Services
@using KnowledgeBase.Web.Services.Models
@inject KnowledgeBaseApiService ApiService
@inject ISnackbar Snackbar

<PageTitle>Dashboard - Knowledge Base</PageTitle>

<MudText Typo="Typo.h4" Class="mb-4">Dashboard</MudText>

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="mb-4" />
}

<MudGrid>
    @* Statistics Cards *@
    <MudItem xs="12" sm="6" md="3">
        <MudCard>
            <MudCardContent>
                <div class="d-flex align-center justify-space-between">
                    <div>
                        <MudText Typo="Typo.subtitle2" Color="Color.Secondary">Total Articles</MudText>
                        <MudText Typo="Typo.h4">@_stats.TotalArticles</MudText>
                    </div>
                    <MudIcon Icon="@Icons.Material.Filled.Article" Size="Size.Large" Color="Color.Primary" />
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudCard>
            <MudCardContent>
                <div class="d-flex align-center justify-space-between">
                    <div>
                        <MudText Typo="Typo.subtitle2" Color="Color.Secondary">Published</MudText>
                        <MudText Typo="Typo.h4">@_stats.PublishedArticles</MudText>
                    </div>
                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Large" Color="Color.Success" />
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudCard>
            <MudCardContent>
                <div class="d-flex align-center justify-space-between">
                    <div>
                        <MudText Typo="Typo.subtitle2" Color="Color.Secondary">Pending Review</MudText>
                        <MudText Typo="Typo.h4">@_stats.PendingReview</MudText>
                    </div>
                    <MudIcon Icon="@Icons.Material.Filled.RateReview" Size="Size.Large" Color="Color.Warning" />
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudCard>
            <MudCardContent>
                <div class="d-flex align-center justify-space-between">
                    <div>
                        <MudText Typo="Typo.subtitle2" Color="Color.Secondary">Total Views</MudText>
                        <MudText Typo="Typo.h4">@_stats.TotalViews.ToString("N0")</MudText>
                    </div>
                    <MudIcon Icon="@Icons.Material.Filled.Visibility" Size="Size.Large" Color="Color.Info" />
                </div>
            </MudCardContent>
        </MudCard>
    </MudItem>

    @* Quick Actions *@
    <MudItem xs="12" md="6">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Quick Actions</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack Spacing="2">
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                               Href="/articles/create" FullWidth="true">
                        Create New Article with AI
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Search"
                               Href="/articles" FullWidth="true">
                        Browse All Articles
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" Color="Color.Secondary" StartIcon="@Icons.Material.Filled.RateReview"
                               Href="/articles/review" FullWidth="true">
                        Review Pending Articles
                    </MudButton>
                </MudStack>
            </MudCardContent>
        </MudCard>
    </MudItem>

    @* Modules Overview *@
    <MudItem xs="12" md="6">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Modules</MudText>
                </CardHeaderContent>
                <CardHeaderActions>
                    <MudButton Variant="Variant.Text" Color="Color.Primary" Href="/modules">Manage</MudButton>
                </CardHeaderActions>
            </MudCardHeader>
            <MudCardContent>
                @if (_modules.Any())
                {
                    <MudStack Spacing="2">
                        @foreach (var module in _modules.Take(5))
                        {
                            <div class="d-flex align-center justify-space-between">
                                <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.Folder" Size="Size.Small" Color="Color.Primary" />
                                    <MudText>@module.Name</MudText>
                                </MudStack>
                                <MudChip T="string" Size="Size.Small" Color="Color.Info">@module.ArticleCount</MudChip>
                            </div>
                        }
                    </MudStack>
                }
                else
                {
                    <MudText Color="Color.Secondary">No modules found</MudText>
                }
            </MudCardContent>
        </MudCard>
    </MudItem>

    @* Recent Articles *@
    <MudItem xs="12">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Recent Articles</MudText>
                </CardHeaderContent>
                <CardHeaderActions>
                    <MudButton Variant="Variant.Text" Color="Color.Primary" Href="/articles">View All</MudButton>
                </CardHeaderActions>
            </MudCardHeader>
            <MudCardContent>
                <MudTable Items="@_recentArticles" Hover="true" Dense="true" Loading="@_loading" T="ArticleSummaryDto">
                    <HeaderContent>
                        <MudTh>Title</MudTh>
                        <MudTh>Type</MudTh>
                        <MudTh>Status</MudTh>
                        <MudTh>Updated</MudTh>
                        <MudTh>Views</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="Title">
                            <MudLink Href="@($"/articles/{context.ArticleId}")">@context.Title</MudLink>
                        </MudTd>
                        <MudTd DataLabel="Type">
                            <MudChip T="string" Size="Size.Small" Color="@GetArticleTypeColor(context.ArticleType)">
                                @context.ArticleType
                            </MudChip>
                        </MudTd>
                        <MudTd DataLabel="Status">
                            <MudChip T="string" Size="Size.Small" Color="@GetStatusColor(context.Status)">
                                @context.Status
                            </MudChip>
                        </MudTd>
                        <MudTd DataLabel="Updated">@context.LastModifiedDate.ToLocalTime().ToString("MMM dd, yyyy")</MudTd>
                        <MudTd DataLabel="Views">@context.ViewCount</MudTd>
                    </RowTemplate>
                    <NoRecordsContent>
                        <MudText>No articles found. <MudLink Href="/articles/create">Create your first article!</MudLink></MudText>
                    </NoRecordsContent>
                </MudTable>
            </MudCardContent>
        </MudCard>
    </MudItem>
</MudGrid>

@code {
    private bool _loading = true;
    private DashboardStats _stats = new();
    private List<ModuleDto> _modules = new();
    private List<ArticleSummaryDto> _recentArticles = new();

    protected override async Task OnInitializedAsync()
    {
        _loading = true;

        try
        {
            // Load data in parallel
            var statsTask = ApiService.GetDashboardStatsAsync();
            var modulesTask = ApiService.GetModulesAsync();
            var articlesTask = ApiService.GetRecentArticlesAsync(5);

            await Task.WhenAll(statsTask, modulesTask, articlesTask);

            _stats = await statsTask;
            _modules = await modulesTask;
            _recentArticles = await articlesTask;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading dashboard: {ex.Message}", Severity.Error);
        }

        _loading = false;
    }

    private Color GetArticleTypeColor(string type) => type switch
    {
        "HowTo" => Color.Primary,
        "Troubleshooting" => Color.Error,
        "ReleaseNote" => Color.Info,
        "Faq" or "FAQ" => Color.Secondary,
        "ProductOverview" => Color.Tertiary,
        "BestPractice" => Color.Warning,
        _ => Color.Default
    };

    private Color GetStatusColor(string status) => status switch
    {
        "Draft" => Color.Default,
        "InReview" => Color.Warning,
        "Published" => Color.Success,
        "Archived" => Color.Dark,
        _ => Color.Default
    };
}

namespace KnowledgeBase.Core.Enums;

/// <summary>
/// Status of an article in the publishing workflow.
/// </summary>
public enum ArticleStatus
{
    /// <summary>
    /// Article is in draft state and not visible to users.
    /// </summary>
    Draft,

    /// <summary>
    /// Article has been submitted for review by an SME.
    /// </summary>
    InReview,

    /// <summary>
    /// Article has been approved and is visible to users.
    /// </summary>
    Published,

    /// <summary>
    /// Article has been archived and is no longer actively displayed.
    /// </summary>
    Archived
}

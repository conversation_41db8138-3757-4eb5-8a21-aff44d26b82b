{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"environment": {"value": "dev"}, "location": {"value": "australiaeast"}, "baseName": {"value": "meshworkskb"}, "sqlAdminLogin": {"value": "sqladmin"}, "sqlAdminPassword": {"reference": {"keyVault": {"id": "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.KeyVault/vaults/{keyvault-name}"}, "secretName": "SqlAdminPassword"}}, "searchSku": {"value": "basic"}, "appServiceSku": {"value": "B1"}, "sqlDatabaseSku": {"value": "S0"}}}
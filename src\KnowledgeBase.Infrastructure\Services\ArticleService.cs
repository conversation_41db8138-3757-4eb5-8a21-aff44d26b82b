using System.Text.Json;
using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Enums;
using KnowledgeBase.Core.Interfaces;
using KnowledgeBase.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace KnowledgeBase.Infrastructure.Services;

/// <summary>
/// Service implementation for article management operations.
/// </summary>
public class ArticleService : IArticleService
{
    private readonly KnowledgeBaseContext _context;
    private readonly ISearchService _searchService;
    private readonly ILogger<ArticleService> _logger;

    public ArticleService(KnowledgeBaseContext context, ISearchService searchService, ILogger<ArticleService> logger)
    {
        _context = context;
        _searchService = searchService;
        _logger = logger;
    }

    public async Task<Article?> GetByIdAsync(Guid articleId, CancellationToken cancellationToken = default)
    {
        return await _context.Articles
            .Include(a => a.Module)
            .Include(a => a.CreatedBy)
            .Include(a => a.LastModifiedBy)
            .Include(a => a.TicketReferences)
            .Include(a => a.RelatedArticles)
                .ThenInclude(r => r.Related)
            .FirstOrDefaultAsync(a => a.ArticleId == articleId, cancellationToken);
    }

    public async Task<(List<Article> Items, int TotalCount)> GetArticlesAsync(
        ArticleSearchCriteria criteria,
        CancellationToken cancellationToken = default)
    {
        var query = _context.Articles
            .Include(a => a.Module)
            .Include(a => a.CreatedBy)
            .AsQueryable();

        // Apply filters
        if (criteria.ArticleType.HasValue)
        {
            query = query.Where(a => a.ArticleType == criteria.ArticleType.Value);
        }

        if (criteria.ModuleId.HasValue)
        {
            query = query.Where(a => a.ModuleId == criteria.ModuleId.Value);
        }

        if (!string.IsNullOrEmpty(criteria.Category))
        {
            query = query.Where(a => a.Category == criteria.Category);
        }

        if (criteria.Status.HasValue)
        {
            query = query.Where(a => a.Status == criteria.Status.Value);
        }

        if (criteria.CreatedByUserId.HasValue)
        {
            query = query.Where(a => a.CreatedByUserId == criteria.CreatedByUserId.Value);
        }

        if (criteria.Tags != null && criteria.Tags.Any())
        {
            // Filter by tags (JSON contains)
            foreach (var tag in criteria.Tags)
            {
                query = query.Where(a => a.Tags != null && a.Tags.Contains(tag));
            }
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        query = criteria.SortBy?.ToLower() switch
        {
            "title" => criteria.SortDescending
                ? query.OrderByDescending(a => a.Title)
                : query.OrderBy(a => a.Title),
            "createddate" => criteria.SortDescending
                ? query.OrderByDescending(a => a.CreatedDate)
                : query.OrderBy(a => a.CreatedDate),
            "viewcount" => criteria.SortDescending
                ? query.OrderByDescending(a => a.ViewCount)
                : query.OrderBy(a => a.ViewCount),
            "rating" => criteria.SortDescending
                ? query.OrderByDescending(a => a.HelpfulCount)
                : query.OrderBy(a => a.HelpfulCount),
            _ => criteria.SortDescending
                ? query.OrderByDescending(a => a.LastModifiedDate)
                : query.OrderBy(a => a.LastModifiedDate)
        };

        // Apply pagination
        var items = await query
            .Skip((criteria.Page - 1) * criteria.PageSize)
            .Take(criteria.PageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<Article> CreateAsync(Article article, Guid userId, CancellationToken cancellationToken = default)
    {
        article.ArticleId = Guid.NewGuid();
        article.CreatedByUserId = userId;
        article.LastModifiedByUserId = userId;
        article.CreatedDate = DateTime.UtcNow;
        article.LastModifiedDate = DateTime.UtcNow;
        article.Status = ArticleStatus.Draft;
        article.Version = 1;

        _context.Articles.Add(article);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Created article {ArticleId}: {Title}", article.ArticleId, article.Title);

        await IndexArticleSafelyAsync(article, cancellationToken);

        return article;
    }

    public async Task<Article> UpdateAsync(
        Article article,
        Guid userId,
        string? changeDescription = null,
        CancellationToken cancellationToken = default)
    {
        var existing = await _context.Articles.FindAsync(new object[] { article.ArticleId }, cancellationToken);
        if (existing == null)
        {
            throw new InvalidOperationException($"Article {article.ArticleId} not found.");
        }

        // Create version snapshot before update
        var version = new ArticleVersion
        {
            VersionId = Guid.NewGuid(),
            ArticleId = existing.ArticleId,
            VersionNumber = existing.Version,
            Title = existing.Title,
            Summary = existing.Summary,
            Content = existing.Content,
            ChangeDescription = changeDescription,
            ModifiedByUserId = userId,
            ModifiedDate = DateTime.UtcNow
        };
        _context.ArticleVersions.Add(version);

        // Update article
        existing.Title = article.Title;
        existing.Summary = article.Summary;
        existing.Content = article.Content;
        existing.ModuleId = article.ModuleId;
        existing.Category = article.Category;
        existing.Tags = article.Tags;
        existing.EstimatedTimeMinutes = article.EstimatedTimeMinutes;
        existing.Difficulty = article.Difficulty;
        existing.AppliesTo = article.AppliesTo;
        existing.LastModifiedByUserId = userId;
        existing.LastModifiedDate = DateTime.UtcNow;
        existing.Version++;

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Updated article {ArticleId} to version {Version}", existing.ArticleId, existing.Version);

        await IndexArticleSafelyAsync(existing, cancellationToken);

        return existing;
    }

    public async Task<bool> DeleteAsync(Guid articleId, Guid userId, CancellationToken cancellationToken = default)
    {
        var article = await _context.Articles.FindAsync(new object[] { articleId }, cancellationToken);
        if (article == null)
        {
            return false;
        }

        // Soft delete
        article.IsDeleted = true;
        article.DeletedDate = DateTime.UtcNow;
        article.DeletedByUserId = userId;

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Deleted article {ArticleId}", articleId);

        await RemoveFromIndexSafelyAsync(articleId, cancellationToken);

        return true;
    }

    public async Task<Article> PublishAsync(
        Guid articleId,
        Guid userId,
        string? changeDescription = null,
        CancellationToken cancellationToken = default)
    {
        var article = await _context.Articles.FindAsync(new object[] { articleId }, cancellationToken);
        if (article == null)
        {
            throw new InvalidOperationException($"Article {articleId} not found.");
        }

        if (article.Status == ArticleStatus.Published)
        {
            return article;
        }

        // Create version snapshot
        var version = new ArticleVersion
        {
            VersionId = Guid.NewGuid(),
            ArticleId = article.ArticleId,
            VersionNumber = article.Version,
            Title = article.Title,
            Summary = article.Summary,
            Content = article.Content,
            ChangeDescription = changeDescription ?? "Published",
            ModifiedByUserId = userId,
            ModifiedDate = DateTime.UtcNow
        };
        _context.ArticleVersions.Add(version);

        article.Status = ArticleStatus.Published;
        article.LastModifiedByUserId = userId;
        article.LastModifiedDate = DateTime.UtcNow;
        article.LastReviewedByUserId = userId;
        article.LastReviewedDate = DateTime.UtcNow;
        article.Version++;

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Published article {ArticleId}", articleId);

        await IndexArticleSafelyAsync(article, cancellationToken);

        return article;
    }

    public async Task<Article> ArchiveAsync(Guid articleId, Guid userId, CancellationToken cancellationToken = default)
    {
        var article = await _context.Articles.FindAsync(new object[] { articleId }, cancellationToken);
        if (article == null)
        {
            throw new InvalidOperationException($"Article {articleId} not found.");
        }

        article.Status = ArticleStatus.Archived;
        article.LastModifiedByUserId = userId;
        article.LastModifiedDate = DateTime.UtcNow;

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Archived article {ArticleId}", articleId);

        await RemoveFromIndexSafelyAsync(articleId, cancellationToken);

        return article;
    }

    public async Task SubmitFeedbackAsync(
        Guid articleId,
        Guid? userId,
        FeedbackType feedbackType,
        string? comments = null,
        string? sessionId = null,
        CancellationToken cancellationToken = default)
    {
        var article = await _context.Articles.FindAsync(new object[] { articleId }, cancellationToken);
        if (article == null)
        {
            throw new InvalidOperationException($"Article {articleId} not found.");
        }

        var feedback = new ArticleFeedback
        {
            FeedbackId = Guid.NewGuid(),
            ArticleId = articleId,
            UserId = userId,
            FeedbackType = feedbackType,
            Comments = comments,
            SessionId = sessionId,
            FeedbackDate = DateTime.UtcNow
        };

        _context.ArticleFeedback.Add(feedback);

        // Update article metrics
        if (feedbackType == FeedbackType.Helpful)
        {
            article.HelpfulCount++;
        }
        else
        {
            article.NotHelpfulCount++;
        }

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Submitted {FeedbackType} feedback for article {ArticleId}", feedbackType, articleId);
    }

    public async Task IncrementViewCountAsync(Guid articleId, CancellationToken cancellationToken = default)
    {
        await _context.Articles
            .Where(a => a.ArticleId == articleId)
            .ExecuteUpdateAsync(s => s.SetProperty(a => a.ViewCount, a => a.ViewCount + 1), cancellationToken);
    }

    public async Task<List<ArticleVersion>> GetVersionHistoryAsync(Guid articleId, CancellationToken cancellationToken = default)
    {
        return await _context.ArticleVersions
            .Include(v => v.ModifiedBy)
            .Where(v => v.ArticleId == articleId)
            .OrderByDescending(v => v.VersionNumber)
            .ToListAsync(cancellationToken);
    }

    private async Task IndexArticleSafelyAsync(Article article, CancellationToken cancellationToken)
    {
        try
        {
            await _searchService.IndexArticleAsync(article, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to index article {ArticleId} in search. Article was saved successfully.", article.ArticleId);
        }
    }

    private async Task RemoveFromIndexSafelyAsync(Guid articleId, CancellationToken cancellationToken)
    {
        try
        {
            await _searchService.RemoveFromIndexAsync(articleId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to remove article {ArticleId} from search index.", articleId);
        }
    }
}

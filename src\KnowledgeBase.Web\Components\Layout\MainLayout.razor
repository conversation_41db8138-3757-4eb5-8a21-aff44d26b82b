@inherits LayoutComponentBase

<MudThemeProvider @bind-IsDarkMode="@_isDarkMode" Theme="_theme" />
<MudPopoverProvider />
<MudDialogProvider />
<MudSnackbarProvider />

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@ToggleDrawer" />
        <MudText Typo="Typo.h5" Class="ml-3">Knowledge Base</MudText>
        <MudSpacer />
        <MudTextField @bind-Value="_searchText" Placeholder="Search articles..." Adornment="Adornment.Start"
                      AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium" Class="mt-0 mr-4"
                      Style="max-width: 400px;" Variant="Variant.Outlined" Margin="Margin.Dense"
                      OnKeyDown="@OnSearchKeyDown" />
        <MudIconButton Icon="@(_isDarkMode ? Icons.Material.Filled.LightMode : Icons.Material.Filled.DarkMode)"
                       Color="Color.Inherit" OnClick="@ToggleDarkMode" />
    </MudAppBar>

    <MudDrawer @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2">
        <NavMenu />
    </MudDrawer>

    <MudMainContent>
        <MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="py-4">
            @Body
        </MudContainer>
    </MudMainContent>
</MudLayout>

<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>

@code {
    private bool _drawerOpen = true;
    private bool _isDarkMode = false;
    private string _searchText = "";

    private MudTheme _theme = new()
    {
        PaletteLight = new PaletteLight
        {
            Primary = "#1976D2",
            Secondary = "#424242",
            AppbarBackground = "#1976D2",
            Background = "#F5F5F5",
            DrawerBackground = "#FFFFFF",
            DrawerText = "rgba(0,0,0,0.87)",
            Surface = "#FFFFFF"
        },
        PaletteDark = new PaletteDark
        {
            Primary = "#90CAF9",
            Secondary = "#CE93D8",
            AppbarBackground = "#1E1E1E",
            Background = "#121212",
            DrawerBackground = "#1E1E1E",
            DrawerText = "rgba(255,255,255,0.87)",
            Surface = "#1E1E1E"
        },
        LayoutProperties = new LayoutProperties()
        {
            DefaultBorderRadius = "4px"
        }
    };

    private void ToggleDrawer()
    {
        _drawerOpen = !_drawerOpen;
    }

    private void ToggleDarkMode()
    {
        _isDarkMode = !_isDarkMode;
    }

    private void OnSearchKeyDown(KeyboardEventArgs args)
    {
        if (args.Key == "Enter" && !string.IsNullOrWhiteSpace(_searchText))
        {
            // Navigate to search results page
            // Navigation.NavigateTo($"/search?q={Uri.EscapeDataString(_searchText)}");
        }
    }
}

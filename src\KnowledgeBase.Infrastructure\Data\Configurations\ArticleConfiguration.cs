using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KnowledgeBase.Infrastructure.Data.Configurations;

public class ArticleConfiguration : IEntityTypeConfiguration<Article>
{
    public void Configure(EntityTypeBuilder<Article> builder)
    {
        builder.ToTable("Articles");

        builder.HasKey(a => a.ArticleId);

        builder.Property(a => a.ArticleType)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(a => a.Title)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(a => a.Summary)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(a => a.Content)
            .IsRequired();

        builder.Property(a => a.Category)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(a => a.Tags)
            .HasMaxLength(500);

        builder.Property(a => a.Diffic<PERSON>y)
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(a => a.AppliesTo)
            .HasMaxLength(200);

        builder.Property(a => a.Status)
            .HasConversion<string>()
            .HasMaxLength(20)
            .IsRequired()
            .HasDefaultValue(ArticleStatus.Draft);

        builder.Property(a => a.CreatedDate)
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(a => a.LastModifiedDate)
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(a => a.ViewCount)
            .HasDefaultValue(0);

        builder.Property(a => a.HelpfulCount)
            .HasDefaultValue(0);

        builder.Property(a => a.NotHelpfulCount)
            .HasDefaultValue(0);

        builder.Property(a => a.Version)
            .HasDefaultValue(1);

        builder.Property(a => a.IsDeleted)
            .HasDefaultValue(false);

        // Ignore computed property
        builder.Ignore(a => a.Rating);

        // Relationships
        builder.HasOne(a => a.Module)
            .WithMany(m => m.Articles)
            .HasForeignKey(a => a.ModuleId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(a => a.CreatedBy)
            .WithMany(u => u.CreatedArticles)
            .HasForeignKey(a => a.CreatedByUserId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(a => a.LastModifiedBy)
            .WithMany(u => u.ModifiedArticles)
            .HasForeignKey(a => a.LastModifiedByUserId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(a => a.LastReviewedBy)
            .WithMany(u => u.ReviewedArticles)
            .HasForeignKey(a => a.LastReviewedByUserId)
            .OnDelete(DeleteBehavior.SetNull);

        // Query filter for soft delete
        builder.HasQueryFilter(a => !a.IsDeleted);

        // Indexes
        builder.HasIndex(a => a.Status);
        builder.HasIndex(a => a.ModuleId);
        builder.HasIndex(a => a.ArticleType);
        builder.HasIndex(a => a.Category);
        builder.HasIndex(a => a.CreatedDate);
        builder.HasIndex(a => a.LastModifiedDate);
        builder.HasIndex(a => new { a.Status, a.ModuleId, a.ArticleType });
    }
}

# Infrastructure Deployment Pipeline
# Deploys Azure resources using Bicep

trigger: none  # Manual trigger only

parameters:
  - name: environment
    displayName: 'Target Environment'
    type: string
    default: 'dev'
    values:
      - dev
      - staging
      - prod

  - name: deployDatabase
    displayName: 'Deploy Database Schema'
    type: boolean
    default: true

variables:
  - name: resourceGroupName
    value: 'rg-meshworkskb-${{ parameters.environment }}'
  - name: location
    value: 'eastus'
  - name: bicepFile
    value: 'infrastructure/azure/main.bicep'

stages:
  # ============================================
  # VALIDATE INFRASTRUCTURE
  # ============================================
  - stage: Validate
    displayName: 'Validate Infrastructure'
    jobs:
      - job: ValidateBicep
        displayName: 'Validate Bicep Template'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: AzureCLI@2
            displayName: 'Validate Bicep'
            inputs:
              azureSubscription: '$(azureSubscription)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az deployment group validate \
                  --resource-group $(resourceGroupName) \
                  --template-file $(bicepFile) \
                  --parameters environment=${{ parameters.environment }} \
                  --parameters sqlAdminLogin=sqladmin \
                  --parameters sqlAdminPassword=$(sqlAdminPassword)

  # ============================================
  # DEPLOY INFRASTRUCTURE
  # ============================================
  - stage: Deploy
    displayName: 'Deploy Infrastructure'
    dependsOn: Validate
    jobs:
      - deployment: DeployInfrastructure
        displayName: 'Deploy Azure Resources'
        environment: 'meshworks-kb-${{ parameters.environment }}-infra'
        pool:
          vmImage: 'ubuntu-latest'
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self

                - task: AzureCLI@2
                  displayName: 'Create Resource Group'
                  inputs:
                    azureSubscription: '$(azureSubscription)'
                    scriptType: 'bash'
                    scriptLocation: 'inlineScript'
                    inlineScript: |
                      az group create \
                        --name $(resourceGroupName) \
                        --location $(location) \
                        --tags environment=${{ parameters.environment }} application=MeshworksKB

                - task: AzureCLI@2
                  displayName: 'Deploy Bicep Template'
                  inputs:
                    azureSubscription: '$(azureSubscription)'
                    scriptType: 'bash'
                    scriptLocation: 'inlineScript'
                    inlineScript: |
                      az deployment group create \
                        --name meshworkskb-$(Build.BuildId) \
                        --resource-group $(resourceGroupName) \
                        --template-file $(bicepFile) \
                        --parameters environment=${{ parameters.environment }} \
                        --parameters sqlAdminLogin=sqladmin \
                        --parameters sqlAdminPassword=$(sqlAdminPassword) \
                        --parameters azureOpenAIEndpoint=$(azureOpenAIEndpoint) \
                        --parameters azureOpenAIKey=$(azureOpenAIKey)

                - task: AzureCLI@2
                  displayName: 'Get Search API Key'
                  inputs:
                    azureSubscription: '$(azureSubscription)'
                    scriptType: 'bash'
                    scriptLocation: 'inlineScript'
                    inlineScript: |
                      SEARCH_NAME="meshworkskb-${{ parameters.environment }}-search"
                      SEARCH_KEY=$(az search admin-key show \
                        --resource-group $(resourceGroupName) \
                        --service-name $SEARCH_NAME \
                        --query primaryKey -o tsv)
                      echo "##vso[task.setvariable variable=searchApiKey;isOutput=true;issecret=true]$SEARCH_KEY"
                  name: getSearchKey

  # ============================================
  # DEPLOY DATABASE SCHEMA
  # ============================================
  - stage: DeployDatabase
    displayName: 'Deploy Database Schema'
    dependsOn: Deploy
    condition: and(succeeded(), eq('${{ parameters.deployDatabase }}', 'true'))
    jobs:
      - deployment: DeploySchema
        displayName: 'Deploy SQL Schema'
        environment: 'meshworks-kb-${{ parameters.environment }}-db'
        pool:
          vmImage: 'windows-latest'
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self

                - task: SqlAzureDacpacDeployment@1
                  displayName: 'Deploy SQL Scripts'
                  inputs:
                    azureSubscription: '$(azureSubscription)'
                    AuthenticationType: 'server'
                    ServerName: 'meshworkskb-${{ parameters.environment }}-sql.database.windows.net'
                    DatabaseName: 'KnowledgeBaseDb'
                    SqlUsername: 'sqladmin'
                    SqlPassword: '$(sqlAdminPassword)'
                    deployType: 'SqlTask'
                    SqlFile: 'infrastructure/sql/schema/001_CreateTables.sql'

                - task: SqlAzureDacpacDeployment@1
                  displayName: 'Deploy Indexes'
                  inputs:
                    azureSubscription: '$(azureSubscription)'
                    AuthenticationType: 'server'
                    ServerName: 'meshworkskb-${{ parameters.environment }}-sql.database.windows.net'
                    DatabaseName: 'KnowledgeBaseDb'
                    SqlUsername: 'sqladmin'
                    SqlPassword: '$(sqlAdminPassword)'
                    deployType: 'SqlTask'
                    SqlFile: 'infrastructure/sql/schema/002_CreateIndexes.sql'

                - task: SqlAzureDacpacDeployment@1
                  displayName: 'Deploy Full-Text Search'
                  inputs:
                    azureSubscription: '$(azureSubscription)'
                    AuthenticationType: 'server'
                    ServerName: 'meshworkskb-${{ parameters.environment }}-sql.database.windows.net'
                    DatabaseName: 'KnowledgeBaseDb'
                    SqlUsername: 'sqladmin'
                    SqlPassword: '$(sqlAdminPassword)'
                    deployType: 'SqlTask'
                    SqlFile: 'infrastructure/sql/schema/003_FullTextSearch.sql'

                - task: SqlAzureDacpacDeployment@1
                  displayName: 'Deploy Seed Data'
                  inputs:
                    azureSubscription: '$(azureSubscription)'
                    AuthenticationType: 'server'
                    ServerName: 'meshworkskb-${{ parameters.environment }}-sql.database.windows.net'
                    DatabaseName: 'KnowledgeBaseDb'
                    SqlUsername: 'sqladmin'
                    SqlPassword: '$(sqlAdminPassword)'
                    deployType: 'SqlTask'
                    SqlFile: 'infrastructure/sql/seed/001_SeedData.sql'

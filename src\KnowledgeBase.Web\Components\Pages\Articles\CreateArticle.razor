@page "/articles/create"
@rendermode InteractiveServer
@using KnowledgeBase.Web.Services
@using KnowledgeBase.Web.Services.Models
@inject KnowledgeBaseApiService ApiService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>Create Article - Knowledge Base</PageTitle>

<MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-4">
    <MudText Typo="Typo.h4">Create New Article</MudText>
    <MudSwitch @bind-Value="_isManualMode" Label="Manual Creation" Color="Color.Primary" />
</MudStack>

@if (_isManualMode)
{
    <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
        <MudTabPanel Text="Article Details">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudCard>
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">
                                    <MudIcon Icon="@Icons.Material.Filled.Article" Class="mr-2" />
                                    Article Information
                                </MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudStack Spacing="3">
                                <MudTextField @bind-Value="_manualTitle" Label="Title" Required="true"
                                              RequiredError="Title is required" MaxLength="200" Counter="200"
                                              Variant="Variant.Outlined" />

                                <MudTextField @bind-Value="_manualSummary" Label="Summary" Required="true"
                                              RequiredError="Summary is required" Lines="3" MaxLength="500" Counter="500"
                                              HelperText="Brief description of the article content"
                                              Variant="Variant.Outlined" />

                                <MudSelect T="Guid" @bind-Value="_selectedModule" Label="Module" Required="true" Variant="Variant.Outlined"
                                           HelperText="Select the module this article belongs to">
                                    @foreach (var module in _modules)
                                    {
                                        <MudSelectItem T="Guid" Value="@module.ModuleId">@module.Name</MudSelectItem>
                                    }
                                </MudSelect>

                                <MudSelect T="string" @bind-Value="_selectedType" Label="Article Type" Required="true" Variant="Variant.Outlined"
                                           HelperText="Select the type of article">
                                    @foreach (var type in ArticleTypes)
                                    {
                                        <MudSelectItem T="string" Value="@type">
                                            @type - @GetTypeDescription(type)
                                        </MudSelectItem>
                                    }
                                </MudSelect>

                                <MudTextField @bind-Value="_manualCategory" Label="Category" Required="true"
                                              RequiredError="Category is required" MaxLength="50"
                                              Placeholder="e.g., configuration, troubleshooting, general"
                                              Variant="Variant.Outlined" />

                                <MudSelect T="string" @bind-Value="_manualDifficulty" Label="Difficulty" Variant="Variant.Outlined">
                                    <MudSelectItem T="string" Value="@("Beginner")">Beginner</MudSelectItem>
                                    <MudSelectItem T="string" Value="@("Intermediate")">Intermediate</MudSelectItem>
                                    <MudSelectItem T="string" Value="@("Advanced")">Advanced</MudSelectItem>
                                </MudSelect>

                                <MudNumericField T="int?" @bind-Value="_manualEstimatedTime" Label="Estimated Time (minutes)"
                                                 Variant="Variant.Outlined" Min="1" Max="120" />

                                <div>
                                    <MudText Typo="Typo.caption" Class="mb-1">Tags</MudText>
                                    <MudChipSet T="string" AllClosable="true" OnClose="@RemoveTag">
                                        @foreach (var tag in _userTags)
                                        {
                                            <MudChip T="string" Value="@tag" Size="Size.Small" Variant="Variant.Outlined"
                                                     Color="Color.Primary">@tag</MudChip>
                                        }
                                    </MudChipSet>
                                    <MudTextField @bind-Value="_tagInput" Label="Add Tag" Variant="Variant.Outlined"
                                                  Margin="Margin.Dense" Class="mt-1"
                                                  HelperText="Press Enter to add a tag"
                                                  OnKeyDown="@OnTagKeyDown"
                                                  Adornment="Adornment.End" AdornmentIcon="@Icons.Material.Filled.Add"
                                                  OnAdornmentClick="@AddTag" />
                                </div>
                            </MudStack>
                        </MudCardContent>
                    </MudCard>
                </MudItem>

                <MudItem xs="12" md="6">
                    <MudCard>
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">
                                    <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-2" />
                                    Content
                                </MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudTextField @bind-Value="_manualContent" Label="Article Content"
                                          Lines="20" Variant="Variant.Outlined"
                                          HelperText="Enter the article content in JSON format or plain text"
                                          Placeholder="Enter your article content here..." />
                        </MudCardContent>
                    </MudCard>

                    <MudPaper Class="pa-4 mt-4" Elevation="0">
                        <MudStack Row="true" Spacing="2" Justify="Justify.FlexEnd">
                            <MudButton Variant="Variant.Outlined" Color="Color.Default" OnClick="@Reset">
                                Clear All
                            </MudButton>
                            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@SaveManualArticle"
                                       StartIcon="@Icons.Material.Filled.Save"
                                       Disabled="@(string.IsNullOrWhiteSpace(_manualTitle) || string.IsNullOrWhiteSpace(_manualSummary) || _selectedModule == Guid.Empty || string.IsNullOrWhiteSpace(_selectedType) || string.IsNullOrWhiteSpace(_manualCategory))">
                                Save Article
                            </MudButton>
                        </MudStack>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        </MudTabPanel>
    </MudTabs>
}
else
{

<MudGrid>
    @* Input Section *@
    <MudItem xs="12" md="6">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">
                        <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-2" />
                        Input Content
                    </MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack Spacing="3">
                    <MudTextField @bind-Value="_userInput" Label="Content" Placeholder="Paste your notes, documentation, or describe what you want to write about..."
                                  Lines="15" Variant="Variant.Outlined" HelperText="Enter raw content and let AI structure it into a knowledge base article" />

                    <MudSelect T="Guid" @bind-Value="_selectedModule" Label="Module" Required="true" Variant="Variant.Outlined"
                               HelperText="Select the module this article belongs to">
                        @foreach (var module in _modules)
                        {
                            <MudSelectItem T="Guid" Value="@module.ModuleId">@module.Name</MudSelectItem>
                        }
                    </MudSelect>
                    @if (_loadingModules)
                    {
                        <MudProgressLinear Indeterminate="true" Color="Color.Primary" Size="Size.Small" />
                    }

                    <MudSelect T="string" @bind-Value="_selectedType" Label="Article Type (Optional)" Clearable="true" Variant="Variant.Outlined"
                               HelperText="Leave empty for AI to auto-detect the best article type">
                        @foreach (var type in ArticleTypes)
                        {
                            <MudSelectItem T="string" Value="@type">
                                @type - @GetTypeDescription(type)
                            </MudSelectItem>
                        }
                    </MudSelect>

                    @if (_selectedType == "ReleaseNote")
                    {
                        <MudTextField @bind-Value="_version" Label="Version" Placeholder="e.g., 2.1.0"
                                      Variant="Variant.Outlined" Required="true"
                                      RequiredError="Version is required for release notes"
                                      HelperText="Semantic version number for this release" />
                    }

                    <div>
                        <MudText Typo="Typo.caption" Class="mb-1">Tags</MudText>
                        <MudChipSet T="string" AllClosable="true" OnClose="@RemoveTag">
                            @foreach (var tag in _userTags)
                            {
                                <MudChip T="string" Value="@tag" Size="Size.Small" Variant="Variant.Outlined"
                                         Color="Color.Primary">@tag</MudChip>
                            }
                        </MudChipSet>
                        <MudTextField @bind-Value="_tagInput" Label="Add Tag" Variant="Variant.Outlined"
                                      Margin="Margin.Dense" Class="mt-1"
                                      HelperText="Press Enter to add a tag"
                                      OnKeyDown="@OnTagKeyDown"
                                      Adornment="Adornment.End" AdornmentIcon="@Icons.Material.Filled.Add"
                                      OnAdornmentClick="@AddTag" />
                    </div>

                    <MudButton Variant="Variant.Filled" Color="Color.Primary" Size="Size.Large"
                               StartIcon="@Icons.Material.Filled.AutoAwesome" OnClick="@GenerateArticle"
                               Disabled="@(string.IsNullOrWhiteSpace(_userInput) || _selectedModule == Guid.Empty || _isGenerating)"
                               FullWidth="true">
                        @if (_isGenerating)
                        {
                            <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                            <span>Generating...</span>
                        }
                        else
                        {
                            <span>Generate Article with AI</span>
                        }
                    </MudButton>
                </MudStack>
            </MudCardContent>
        </MudCard>

        @* AI Info Card *@
        <MudCard Class="mt-4">
            <MudCardContent>
                <MudText Typo="Typo.subtitle2" Color="Color.Primary" Class="mb-2">How it works</MudText>
                <MudList T="string" Dense="true">
                    <MudListItem Icon="@Icons.Material.Filled.LooksOne" IconColor="Color.Primary">
                        Paste your raw content, notes, or describe the topic
                    </MudListItem>
                    <MudListItem Icon="@Icons.Material.Filled.LooksTwo" IconColor="Color.Primary">
                        AI analyzes and classifies your content
                    </MudListItem>
                    <MudListItem Icon="@Icons.Material.Filled.Looks3" IconColor="Color.Primary">
                        Specialized agent generates structured article
                    </MudListItem>
                    <MudListItem Icon="@Icons.Material.Filled.Looks4" IconColor="Color.Primary">
                        Quality evaluation provides improvement suggestions
                    </MudListItem>
                </MudList>
            </MudCardContent>
        </MudCard>
    </MudItem>

    @* Preview Section *@
    <MudItem xs="12" md="6">
        @if (_generatedArticle != null)
        {
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">
                            <MudIcon Icon="@Icons.Material.Filled.Preview" Class="mr-2" />
                            Generated Article Preview
                        </MudText>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        <MudChip T="string" Color="@GetArticleTypeColor(_generatedArticle.ArticleType)" Size="Size.Small">
                            @_generatedArticle.ArticleType
                        </MudChip>
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Spacing="3">
                        <div>
                            <MudText Typo="Typo.caption" Color="Color.Secondary">Title</MudText>
                            <MudText Typo="Typo.h6">@_generatedArticle.Title</MudText>
                        </div>

                        <div>
                            <MudText Typo="Typo.caption" Color="Color.Secondary">Summary</MudText>
                            <MudText>@_generatedArticle.Summary</MudText>
                        </div>

                        @if (_classification != null)
                        {
                            <MudAlert Severity="Severity.Info" Dense="true">
                                <MudText Typo="Typo.body2">
                                    <strong>Classification:</strong> @_classification.ArticleType
                                    (Confidence: @_classification.Confidence.ToString("P0"))
                                </MudText>
                                <MudText Typo="Typo.caption">@_classification.Reasoning</MudText>
                            </MudAlert>
                        }

                        @if (_generatedArticle.Tags != null)
                        {
                            <div>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">Tags</MudText>
                                <MudChipSet T="string" ReadOnly="true">
                                    @foreach (var tag in GetTags())
                                    {
                                        <MudChip T="string" Size="Size.Small" Variant="Variant.Outlined">@tag</MudChip>
                                    }
                                </MudChipSet>
                            </div>
                        }

                        <MudDivider />

                        <div>
                            <MudText Typo="Typo.caption" Color="Color.Secondary">Content Preview</MudText>
                            <MudPaper Class="pa-3 mt-2" Elevation="0" Style="background-color: var(--mud-palette-background-grey); max-height: 300px; overflow-y: auto;">
                                <pre style="white-space: pre-wrap; font-family: inherit; margin: 0;">@FormatContentPreview()</pre>
                            </MudPaper>
                        </div>
                    </MudStack>
                </MudCardContent>
            </MudCard>

            @* Evaluation Results *@
            @if (_evaluation != null)
            {
                <MudCard Class="mt-4">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="@Icons.Material.Filled.Assessment" Class="mr-2" />
                                Quality Evaluation
                            </MudText>
                        </CardHeaderContent>
                        <CardHeaderActions>
                            <MudChip T="string" Color="@GetScoreColor(_evaluation.OverallScore)" Size="Size.Medium">
                                @_evaluation.OverallScore.ToString("F1") / 10
                            </MudChip>
                        </CardHeaderActions>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudStack Spacing="2">
                            @foreach (var score in _evaluation.Scores)
                            {
                                <div>
                                    <div class="d-flex justify-space-between mb-1">
                                        <MudText Typo="Typo.body2">@FormatScoreName(score.Key)</MudText>
                                        <MudText Typo="Typo.body2">@score.Value.ToString("F1")</MudText>
                                    </div>
                                    <MudProgressLinear Value="@((double)score.Value * 10)" Color="@GetScoreColor(score.Value)" />
                                </div>
                            }

                            @if (_evaluation.Strengths.Any())
                            {
                                <MudDivider Class="my-2" />
                                <MudText Typo="Typo.subtitle2" Color="Color.Success">Strengths</MudText>
                                <MudList T="string" Dense="true">
                                    @foreach (var strength in _evaluation.Strengths)
                                    {
                                        <MudListItem Icon="@Icons.Material.Filled.CheckCircle" IconColor="Color.Success">
                                            @strength
                                        </MudListItem>
                                    }
                                </MudList>
                            }

                            @if (_evaluation.RecommendedChanges.Any())
                            {
                                <MudDivider Class="my-2" />
                                <MudText Typo="Typo.subtitle2" Color="Color.Warning">Recommended Improvements</MudText>
                                <MudList T="string" Dense="true">
                                    @foreach (var change in _evaluation.RecommendedChanges)
                                    {
                                        <MudListItem Icon="@Icons.Material.Filled.Lightbulb" IconColor="Color.Warning">
                                            @change
                                        </MudListItem>
                                    }
                                </MudList>
                            }
                        </MudStack>
                    </MudCardContent>
                </MudCard>
            }

            @* Action Buttons *@
            <MudPaper Class="pa-4 mt-4" Elevation="0">
                <MudStack Row="true" Spacing="2" Justify="Justify.FlexEnd">
                    <MudButton Variant="Variant.Outlined" Color="Color.Default" OnClick="@Reset">
                        Start Over
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="@RegenerateArticle"
                               Disabled="@_isGenerating">
                        Regenerate
                    </MudButton>
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@SaveAsDraft"
                               StartIcon="@Icons.Material.Filled.Save">
                        Save as Draft
                    </MudButton>
                    <MudButton Variant="Variant.Filled" Color="Color.Success" OnClick="@SubmitForReview"
                               StartIcon="@Icons.Material.Filled.Send">
                        Submit for Review
                    </MudButton>
                </MudStack>
            </MudPaper>
        }
        else
        {
            <MudCard Style="min-height: 400px;" Class="d-flex align-center justify-center">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="@Icons.Material.Filled.AutoAwesome" Size="Size.Large" Color="Color.Primary" Class="mb-4" />
                    <MudText Typo="Typo.h6" Color="Color.Secondary">No preview yet</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        Enter your content and click "Generate Article with AI" to see the preview
                    </MudText>
                </MudCardContent>
            </MudCard>
        }
    </MudItem>
</MudGrid>
}

@code {
    private string _userInput = "";
    private Guid _selectedModule = Guid.Empty;
    private string? _selectedType;
    private string _version = "";
    private string _tagInput = "";
    private List<string> _userTags = new();
    private bool _isGenerating = false;
    private bool _isManualMode = false;
    private bool _loadingModules = true;

    // Manual mode fields
    private string _manualTitle = "";
    private string _manualSummary = "";
    private string _manualContent = "";
    private string _manualCategory = "";
    private string? _manualDifficulty;
    private int? _manualEstimatedTime;

    private ArticleDto? _generatedArticle;
    private ClassificationResult? _classification;
    private EvaluationResult? _evaluation;

    private List<ModuleDto> _modules = new();
    private static readonly string[] ArticleTypes = { "HowTo", "Troubleshooting", "ReleaseNote", "Faq", "ProductOverview", "BestPractice" };

    protected override async Task OnInitializedAsync()
    {
        _loadingModules = true;
        try
        {
            _modules = await ApiService.GetModulesAsync();
            if (_modules.Any())
            {
                _selectedModule = _modules.First().ModuleId;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error loading modules: {ex.Message}", Severity.Error);
        }
        _loadingModules = false;
    }

    private async Task GenerateArticle()
    {
        if (string.IsNullOrWhiteSpace(_userInput) || _selectedModule == Guid.Empty)
            return;

        _isGenerating = true;
        StateHasChanged();

        try
        {
            var request = new GenerateArticleRequest
            {
                Content = _userInput,
                ModuleId = _selectedModule,
                ArticleType = _selectedType
            };

            var result = await ApiService.GenerateArticleAsync(request);

            if (result != null)
            {
                _generatedArticle = result.Article;
                _classification = result.Classification;
                _evaluation = result.Evaluation;
                Snackbar.Add("Article generated successfully!", Severity.Success);
            }
            else
            {
                Snackbar.Add("Failed to generate article", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error generating article: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isGenerating = false;
            StateHasChanged();
        }
    }

    private async Task RegenerateArticle()
    {
        await GenerateArticle();
    }

    private void Reset()
    {
        _userInput = "";
        _selectedType = null;
        _version = "";
        _tagInput = "";
        _userTags.Clear();
        _generatedArticle = null;
        _classification = null;
        _evaluation = null;

        // Reset manual mode fields
        _manualTitle = "";
        _manualSummary = "";
        _manualContent = "";
        _manualCategory = "";
        _manualDifficulty = null;
        _manualEstimatedTime = null;
    }

    private async Task SaveManualArticle()
    {
        if (string.IsNullOrWhiteSpace(_manualTitle) ||
            string.IsNullOrWhiteSpace(_manualSummary) ||
            _selectedModule == Guid.Empty ||
            string.IsNullOrWhiteSpace(_selectedType) ||
            string.IsNullOrWhiteSpace(_manualCategory))
        {
            Snackbar.Add("Please fill in all required fields", Severity.Warning);
            return;
        }

        try
        {
            // Parse content as JSON if possible, otherwise wrap in simple structure
            object contentObject;
            if (!string.IsNullOrWhiteSpace(_manualContent))
            {
                try
                {
                    contentObject = System.Text.Json.JsonSerializer.Deserialize<object>(_manualContent);
                }
                catch
                {
                    // If not valid JSON, create a simple structure
                    contentObject = new { text = _manualContent };
                }
            }
            else
            {
                contentObject = new { };
            }

            var request = new SaveArticleRequest
            {
                ArticleType = _selectedType,
                Title = _manualTitle,
                Summary = _manualSummary,
                Content = contentObject,
                ModuleId = _selectedModule,
                Category = _manualCategory,
                Tags = _userTags.Any() ? _userTags : null,
                EstimatedTimeMinutes = _manualEstimatedTime,
                Difficulty = _manualDifficulty
            };

            var created = await ApiService.SaveArticleAsync(request);
            if (created != null)
            {
                Snackbar.Add("Article saved successfully", Severity.Success);
                Navigation.NavigateTo("/articles/drafts");
            }
            else
            {
                Snackbar.Add("Failed to save article", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error saving article: {ex.Message}", Severity.Error);
        }
    }

    private void AddTag()
    {
        var tag = _tagInput.Trim();
        if (!string.IsNullOrEmpty(tag) && !_userTags.Contains(tag, StringComparer.OrdinalIgnoreCase))
        {
            _userTags.Add(tag);
        }
        _tagInput = "";
    }

    private void RemoveTag(MudChip<string> chip)
    {
        if (chip.Value != null)
        {
            _userTags.Remove(chip.Value);
        }
    }

    private void OnTagKeyDown(KeyboardEventArgs args)
    {
        if (args.Key == "Enter")
        {
            AddTag();
        }
    }

    private async Task SaveAsDraft()
    {
        if (_generatedArticle == null) return;

        var request = new CreateArticleRequest
        {
            ArticleType = _generatedArticle.ArticleType,
            Title = _generatedArticle.Title,
            Summary = _generatedArticle.Summary,
            Content = _generatedArticle.Content != null ? System.Text.Json.JsonSerializer.Serialize(_generatedArticle.Content) : "{}",
            ModuleId = _selectedModule,
            Category = _generatedArticle.Category ?? "general",
            Tags = GetTags(),
            EstimatedTimeMinutes = _generatedArticle.EstimatedTimeMinutes,
            Difficulty = _generatedArticle.Difficulty
        };

        var created = await ApiService.CreateArticleAsync(request);
        if (created != null)
        {
            Snackbar.Add("Article saved as draft", Severity.Success);
            Navigation.NavigateTo("/articles/drafts");
        }
        else
        {
            Snackbar.Add("Failed to save article", Severity.Error);
        }
    }

    private async Task SubmitForReview()
    {
        if (_generatedArticle == null) return;

        var request = new CreateArticleRequest
        {
            ArticleType = _generatedArticle.ArticleType,
            Title = _generatedArticle.Title,
            Summary = _generatedArticle.Summary,
            Content = _generatedArticle.Content != null ? System.Text.Json.JsonSerializer.Serialize(_generatedArticle.Content) : "{}",
            ModuleId = _selectedModule,
            Category = _generatedArticle.Category ?? "general",
            Tags = GetTags(),
            EstimatedTimeMinutes = _generatedArticle.EstimatedTimeMinutes,
            Difficulty = _generatedArticle.Difficulty
        };

        var created = await ApiService.CreateArticleAsync(request);
        if (created != null)
        {
            var success = await ApiService.UpdateArticleStatusAsync(created.ArticleId, "InReview");
            if (success)
            {
                Snackbar.Add("Article submitted for review", Severity.Success);
                Navigation.NavigateTo("/articles");
            }
            else
            {
                Snackbar.Add("Article saved but failed to submit for review", Severity.Warning);
            }
        }
        else
        {
            Snackbar.Add("Failed to save article", Severity.Error);
        }
    }

    private string GetTypeDescription(string type) => type switch
    {
        "HowTo" => "Step-by-step guides",
        "Troubleshooting" => "Problem resolution",
        "ReleaseNote" => "Version updates",
        "Faq" => "Common questions",
        "ProductOverview" => "Feature descriptions",
        "BestPractice" => "Recommended approaches",
        _ => ""
    };

    private List<string> GetTags()
    {
        var tags = new List<string>();

        // Add AI-generated tags
        if (_generatedArticle?.Tags != null && _generatedArticle.Tags.Any())
        {
            tags.AddRange(_generatedArticle.Tags);
        }

        // Add user-defined tags
        foreach (var tag in _userTags)
        {
            if (!tags.Contains(tag, StringComparer.OrdinalIgnoreCase))
                tags.Add(tag);
        }

        // Add version tag for release notes
        if (!string.IsNullOrWhiteSpace(_version))
        {
            var versionTag = $"v{_version}";
            if (!tags.Contains(versionTag, StringComparer.OrdinalIgnoreCase))
                tags.Add(versionTag);
        }

        return tags;
    }

    private string FormatContentPreview()
    {
        if (_generatedArticle?.Content == null)
            return "No content available";

        try
        {
            var options = new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
            return System.Text.Json.JsonSerializer.Serialize(_generatedArticle.Content, options);
        }
        catch
        {
            return _generatedArticle.Content?.ToString() ?? "No content available";
        }
    }

    private string FormatScoreName(string key) => key switch
    {
        "completeness" => "Completeness",
        "clarity" => "Clarity",
        "accuracy" => "Accuracy",
        "actionability" => "Actionability",
        "structure" => "Structure",
        "seo" => "SEO",
        _ => key
    };

    private Color GetArticleTypeColor(string type) => type switch
    {
        "HowTo" => Color.Primary,
        "Troubleshooting" => Color.Error,
        "ReleaseNote" => Color.Info,
        "Faq" or "FAQ" => Color.Secondary,
        "ProductOverview" => Color.Tertiary,
        "BestPractice" => Color.Warning,
        _ => Color.Default
    };

    private Color GetScoreColor(decimal score) => score switch
    {
        >= 8 => Color.Success,
        >= 6 => Color.Warning,
        _ => Color.Error
    };
}

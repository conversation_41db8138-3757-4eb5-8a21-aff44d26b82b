using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KnowledgeBase.Infrastructure.Data.Configurations;

public class ArticleReviewConfiguration : IEntityTypeConfiguration<ArticleReview>
{
    public void Configure(EntityTypeBuilder<ArticleReview> builder)
    {
        builder.ToTable("ArticleReviews");

        builder.HasKey(r => r.ReviewId);

        builder.Property(r => r.ReviewDate)
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(r => r.Status)
            .HasConversion<string>()
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(r => r.Comments)
            .HasMaxLength(2000);

        builder.HasOne(r => r.Article)
            .WithMany(a => a.Reviews)
            .HasForeignKey(r => r.ArticleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(r => r.Reviewer)
            .WithMany(u => u.Reviews)
            .HasForeignKey(r => r.ReviewerUserId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasIndex(r => new { r.ArticleId, r.ReviewDate });
    }
}

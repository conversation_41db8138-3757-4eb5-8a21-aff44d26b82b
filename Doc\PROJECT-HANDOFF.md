# Knowledge Base Platform - Project Handoff for Coding Agent

## Project Overview

**Project Name**: AI-Powered Knowledge Base Platform  
**Target Platform**: Azure (PaaS services)  
**Tech Stack**: .NET Core 8, Blazor, Azure SQL, Azure OpenAI, Azure AI Search  
**Budget**: ~$400 USD/month Azure costs (flexible)  
**Timeline**: 5 phases over ~23 weeks  
**Deployment**: Azure DevOps (CI/CD), Bicep (IaC)

---

## Executive Summary

Building a knowledge base platform that uses specialized AI agents to create, categorize, and structure articles from multiple input sources (paste, documents, Teams/Slack, SQL/CSV). The platform serves companies that provide products/services and need to maintain comprehensive knowledge repositories for customers and internal teams.

**Core Innovation**: Multiple specialized AI agents (Router, How-To, Troubleshooting, Release Note, FAQ, Product Overview, Best Practice, Evaluation, Relationship) instead of one generic agent, enabling optimized quality per article type.

---

## Current Status

✅ **COMPLETED:**
- Project scope and requirements definition
- 5-phase delivery plan with timeline and cost estimates
- Agent architecture strategy (8 specialized agents)
- Data model and article type definitions (6 article types)
- UI design direction and mockups started

🎯 **NEXT PHASE - PHASE 1 (Weeks 1-6):**
Build foundation with web paste tool and basic AI-powered article creation

---

## Phase 1 Deliverables (IMMEDIATE FOCUS)

### What to Build

1. **Azure Infrastructure** (Bicep templates)
   - Resource Group
   - Azure App Service (2x B1 Basic) - API + Blazor UI
   - Azure SQL Database (Basic 5 DTU)
   - Azure OpenAI Service (GPT-4 deployments)
   - Azure Key Vault
   - Application Insights
   - Managed Identities

2. **Database Schema** (Azure SQL)
   - Articles table with full-text search
   - Modules table with SME assignments
   - ArticleTicketReferences (many-to-many)
   - RelatedArticles (many-to-many)
   - ArticleVersions (history)
   - ArticleReviews (workflow)
   - ArticleFeedback (ratings)
   - Users table

3. **Backend API** (.NET Core 8 Web API)
   - Article CRUD endpoints
   - User management endpoints
   - Search endpoints
   - AI agent orchestration service
   - Router Agent (classify content type)
   - How-To Agent (create step-by-step articles)
   - Troubleshooting Agent (create problem-solution articles)
   - OpenAI service wrapper
   - Repository pattern with EF Core

4. **Frontend** (Blazor Server)
   - Authentication (Azure AD)
   - Article creation workflow page
   - Article list/grid page
   - Article detail view page
   - Simple search interface
   - Rating component

5. **DevOps** (Azure DevOps)
   - Git repository setup
   - CI/CD pipelines for API and Blazor
   - Bicep deployment pipeline
   - Dev/Staging/Prod environments

---

## Technical Architecture

### Solution Structure

```
KnowledgeBase/
├── src/
│   ├── KnowledgeBase.Api/              # .NET Core 8 Web API
│   │   ├── Controllers/
│   │   │   ├── ArticlesController.cs
│   │   │   ├── SearchController.cs
│   │   │   ├── UsersController.cs
│   │   │   └── ModulesController.cs
│   │   ├── Services/
│   │   │   ├── Agents/
│   │   │   │   ├── IAgentService.cs
│   │   │   │   ├── RouterAgentService.cs
│   │   │   │   ├── HowToAgentService.cs
│   │   │   │   ├── TroubleshootingAgentService.cs
│   │   │   │   ├── EvaluationAgentService.cs
│   │   │   │   └── AgentFactory.cs
│   │   │   ├── ArticleOrchestrator.cs
│   │   │   ├── ArticleService.cs
│   │   │   ├── SearchService.cs
│   │   │   └── UserService.cs
│   │   ├── Infrastructure/
│   │   │   ├── Data/
│   │   │   │   ├── KnowledgeBaseContext.cs
│   │   │   │   └── Repositories/
│   │   │   ├── AzureOpenAI/
│   │   │   │   └── AzureOpenAIClient.cs
│   │   │   └── Configuration/
│   │   ├── Models/
│   │   │   ├── Entities/
│   │   │   │   ├── Article.cs
│   │   │   │   ├── Module.cs
│   │   │   │   ├── User.cs
│   │   │   │   └── ...
│   │   │   └── DTOs/
│   │   │       ├── ArticleDto.cs
│   │   │       ├── CreateArticleRequest.cs
│   │   │       └── ...
│   │   └── Program.cs
│   │
│   └── KnowledgeBase.Web/              # Blazor Server
│       ├── Pages/
│       │   ├── Index.razor
│       │   ├── Articles/
│       │   │   ├── Create.razor
│       │   │   ├── List.razor
│       │   │   ├── Detail.razor
│       │   │   └── Edit.razor
│       │   ├── Search.razor
│       │   └── Admin/
│       ├── Components/
│       │   ├── ArticleTypeCard.razor
│       │   ├── WorkflowSteps.razor
│       │   ├── RatingComponent.razor
│       │   └── ...
│       ├── Services/
│       │   └── ApiClient.cs
│       ├── wwwroot/
│       │   ├── css/
│       │   │   └── app.css
│       │   └── js/
│       └── Program.cs
│
├── infrastructure/
│   ├── bicep/
│   │   ├── main.bicep                  # Main template
│   │   ├── modules/
│   │   │   ├── appService.bicep
│   │   │   ├── sqlDatabase.bicep
│   │   │   ├── openai.bicep
│   │   │   ├── keyVault.bicep
│   │   │   └── monitoring.bicep
│   │   └── parameters/
│   │       ├── dev.parameters.json
│   │       ├── staging.parameters.json
│   │       └── prod.parameters.json
│   │
│   └── sql/
│       ├── schema/
│       │   ├── 001_CreateTables.sql
│       │   ├── 002_CreateIndexes.sql
│       │   └── 003_CreateFullTextSearch.sql
│       └── seed/
│           └── 001_SeedData.sql
│
├── tests/
│   ├── KnowledgeBase.Api.Tests/
│   │   ├── Controllers/
│   │   ├── Services/
│   │   └── Agents/
│   └── KnowledgeBase.Integration.Tests/
│
├── docs/
│   ├── architecture/
│   │   ├── agent-architecture.md       # From our planning session
│   │   ├── data-model.md               # From our planning session
│   │   └── ui-design.md
│   ├── api/
│   │   └── swagger.json
│   └── deployment/
│       └── deployment-guide.md
│
├── .github/ or azure-pipelines/
│   ├── workflows/ or pipelines/
│   │   ├── api-build.yml
│   │   ├── web-build.yml
│   │   ├── infrastructure-deploy.yml
│   │   └── database-deploy.yml
│
├── .gitignore
├── README.md
└── KnowledgeBase.sln
```

---

## Key Implementation Details

### 1. Azure OpenAI Agent Architecture

**Router Agent** - Classifies content type
```csharp
public class RouterAgentService : IAgentService
{
    private readonly IAzureOpenAIClient _openAIClient;
    private const string ROUTER_SYSTEM_PROMPT = @"
        You are a content classification expert. Analyze user input and determine 
        the type of knowledge article they want to create.

        Article Types:
        - how_to: Step-by-step instructions for completing a task
        - troubleshooting: Problem diagnosis and solutions
        - release_note: Product changes, updates, new features
        - faq: Common questions and answers
        - product_overview: Product descriptions and capabilities
        - best_practice: Recommended approaches and patterns

        Return JSON:
        {
          ""article_type"": ""..."",
          ""confidence"": 0.0-1.0,
          ""reasoning"": ""brief explanation""
        }

        If confidence < 0.7, ask user for clarification.
    ";

    public async Task<ArticleTypeClassification> ClassifyAsync(string userInput)
    {
        var response = await _openAIClient.GetChatCompletionsAsync(
            deployment: "gpt-4-router",
            systemPrompt: ROUTER_SYSTEM_PROMPT,
            userMessage: userInput,
            temperature: 0.3
        );
        
        return JsonSerializer.Deserialize<ArticleTypeClassification>(response);
    }
}
```

**How-To Agent** - Creates step-by-step articles
```csharp
public class HowToAgentService : IAgentService
{
    private const string HOWTO_SYSTEM_PROMPT = @"
        You are an expert technical writer specializing in how-to guides.

        Your goal: Create clear, actionable step-by-step instructions that users 
        can follow to accomplish a specific task.

        Guidelines:
        1. Start with prerequisites (what users need before starting)
        2. Break down into numbered steps (aim for 5-10 steps)
        3. Each step should be a single, clear action
        4. Include expected outcomes (""You should see..."")
        5. Add warnings/cautions where needed
        6. End with validation (""To verify it worked..."")
        7. Suggest related tasks (""Next, you might want to..."")

        Format Output as JSON with this structure:
        {
          ""title"": ""How to [Action] [Object]"",
          ""summary"": ""Brief 1-2 sentence description"",
          ""prerequisites"": [""item1"", ""item2""],
          ""steps"": [
            {
              ""number"": 1,
              ""action"": ""Clear instruction"",
              ""expected_outcome"": ""What user should see"",
              ""screenshot_needed"": true/false,
              ""warning"": ""Optional warning""
            }
          ],
          ""validation"": ""How to verify success"",
          ""next_steps"": [""topic1"", ""topic2""],
          ""estimated_time_minutes"": 10,
          ""difficulty"": ""beginner|intermediate|advanced"",
          ""tags"": [""tag1"", ""tag2""]
        }
    ";

    public async Task<HowToArticle> GenerateAsync(string userInput)
    {
        var response = await _openAIClient.GetChatCompletionsAsync(
            deployment: "gpt-4-howto",
            systemPrompt: HOWTO_SYSTEM_PROMPT,
            userMessage: userInput,
            temperature: 0.5,
            maxTokens: 2000
        );
        
        return JsonSerializer.Deserialize<HowToArticle>(response);
    }
}
```

**Article Orchestrator** - Coordinates the workflow
```csharp
public class ArticleOrchestrator
{
    private readonly RouterAgentService _router;
    private readonly AgentFactory _agentFactory;
    private readonly EvaluationAgentService _evaluator;
    private readonly IArticleRepository _articleRepository;
    
    public async Task<ArticleCreationResult> CreateArticleAsync(
        string userInput, 
        Guid userId,
        ArticleType? manualOverride = null)
    {
        // Step 1: Classify (or use manual override)
        var classification = manualOverride.HasValue 
            ? new ArticleTypeClassification { ArticleType = manualOverride.Value, Confidence = 1.0 }
            : await _router.ClassifyAsync(userInput);
        
        if (classification.Confidence < 0.7 && !manualOverride.HasValue)
        {
            return ArticleCreationResult.NeedsClarity(classification.Reasoning);
        }
        
        // Step 2: Get specialized agent
        var agent = _agentFactory.GetAgent(classification.ArticleType);
        
        // Step 3: Generate article
        var article = await agent.GenerateAsync(userInput);
        article.ArticleType = classification.ArticleType;
        article.CreatedByUserId = userId;
        article.Status = ArticleStatus.Draft;
        
        // Step 4: Evaluate quality
        var evaluation = await _evaluator.EvaluateAsync(article);
        
        // Step 5: Save as draft
        var savedArticle = await _articleRepository.CreateAsync(article);
        
        return new ArticleCreationResult
        {
            Article = savedArticle,
            Classification = classification,
            Evaluation = evaluation,
            Success = true
        };
    }
}
```

### 2. Database Schema (Critical Tables)

```sql
-- Articles Table (Core entity)
CREATE TABLE Articles (
    ArticleId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ArticleType VARCHAR(50) NOT NULL, -- 'how_to', 'troubleshooting', 'release_note', etc.
    Title NVARCHAR(200) NOT NULL,
    Summary NVARCHAR(500) NOT NULL,
    Content NVARCHAR(MAX) NOT NULL, -- JSON structure varies by type
    
    ModuleId UNIQUEIDENTIFIER NOT NULL,
    Category VARCHAR(50) NOT NULL,
    Tags NVARCHAR(500), -- JSON array
    
    Status VARCHAR(20) NOT NULL DEFAULT 'draft', -- 'draft', 'in_review', 'published', 'archived'
    
    CreatedByUserId UNIQUEIDENTIFIER NOT NULL,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastModifiedByUserId UNIQUEIDENTIFIER NOT NULL,
    LastModifiedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastReviewedDate DATETIME2 NULL,
    LastReviewedByUserId UNIQUEIDENTIFIER NULL,
    
    ViewCount INT NOT NULL DEFAULT 0,
    HelpfulCount INT NOT NULL DEFAULT 0,
    NotHelpfulCount INT NOT NULL DEFAULT 0,
    Rating AS (CASE WHEN (HelpfulCount + NotHelpfulCount) > 0 
                    THEN CAST(HelpfulCount AS DECIMAL(5,2)) / (HelpfulCount + NotHelpfulCount) 
                    ELSE NULL END) PERSISTED,
    
    Version INT NOT NULL DEFAULT 1,
    
    CONSTRAINT FK_Articles_Module FOREIGN KEY (ModuleId) REFERENCES Modules(ModuleId),
    CONSTRAINT FK_Articles_CreatedBy FOREIGN KEY (CreatedByUserId) REFERENCES Users(UserId)
);

-- Full-text search index
CREATE FULLTEXT INDEX ON Articles(Title, Summary, Content) 
KEY INDEX PK_Articles
WITH STOPLIST = SYSTEM;

-- Modules Table
CREATE TABLE Modules (
    ModuleId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500) NULL,
    SMEUserId UNIQUEIDENTIFIER NULL, -- Subject Matter Expert
    IsActive BIT NOT NULL DEFAULT 1,
    
    CONSTRAINT FK_Modules_SME FOREIGN KEY (SMEUserId) REFERENCES Users(UserId)
);

-- Users Table
CREATE TABLE Users (
    UserId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Email NVARCHAR(255) NOT NULL UNIQUE,
    Name NVARCHAR(200) NOT NULL,
    Role VARCHAR(20) NOT NULL, -- 'guest', 'user', 'power_user', 'admin'
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);
```

### 3. Blazor UI Component Example

**Article Creation Page (Create.razor)**
```razor
@page "/articles/create"
@using KnowledgeBase.Web.Models
@inject ApiClient Api
@inject NavigationManager Navigation

<PageTitle>Create Article</PageTitle>

<div class="page-header">
    <h1 class="page-title">Create New Article</h1>
    <p class="page-subtitle">AI-powered article creation from any source</p>
</div>

<WorkflowSteps CurrentStep="@currentStep" />

@if (currentStep == 1)
{
    <div class="content-section">
        <h3>Input Your Content</h3>
        
        <div class="input-tabs">
            <button class="@(inputMethod == "paste" ? "active" : "")" 
                    @onclick="() => inputMethod = \"paste\"">
                Paste Text
            </button>
            <button class="@(inputMethod == "upload" ? "active" : "")" 
                    @onclick="() => inputMethod = \"upload\"">
                Upload File
            </button>
        </div>

        @if (inputMethod == "paste")
        {
            <textarea @bind="userInput" 
                      placeholder="Paste your content here..."
                      rows="15"></textarea>
        }
        else
        {
            <InputFile OnChange="HandleFileUpload" />
        }

        <div class="action-bar">
            <button class="btn btn-primary" @onclick="ProcessContent">
                Process with AI
            </button>
        </div>
    </div>
}
else if (currentStep == 2)
{
    <div class="ai-section">
        <h3>🤖 AI is analyzing your content...</h3>
        <ProgressBar Value="@aiProgress" />
    </div>
}
else if (currentStep == 3)
{
    <ArticleTypeSelector 
        Classification="@classification" 
        OnTypeSelected="HandleTypeSelection" />
}
else if (currentStep == 4)
{
    <ArticleEditor 
        Article="@generatedArticle" 
        Evaluation="@evaluation"
        OnSave="SaveArticle"
        OnPublish="PublishArticle" />
}

@code {
    private int currentStep = 1;
    private string inputMethod = "paste";
    private string userInput = "";
    private ArticleTypeClassification classification;
    private Article generatedArticle;
    private ArticleEvaluation evaluation;
    private int aiProgress = 0;

    private async Task ProcessContent()
    {
        currentStep = 2;
        StateHasChanged();

        // Simulate AI processing with progress
        for (int i = 0; i <= 100; i += 10)
        {
            aiProgress = i;
            StateHasChanged();
            await Task.Delay(200);
        }

        // Call API to process content
        var result = await Api.CreateArticleAsync(new CreateArticleRequest
        {
            UserInput = userInput,
            UserId = Guid.Parse("user-id-from-auth")
        });

        classification = result.Classification;
        generatedArticle = result.Article;
        evaluation = result.Evaluation;

        currentStep = 3;
        StateHasChanged();
    }

    private void HandleTypeSelection(ArticleType selectedType)
    {
        generatedArticle.ArticleType = selectedType;
        currentStep = 4;
    }

    private async Task SaveArticle()
    {
        await Api.SaveArticleAsync(generatedArticle);
        Navigation.NavigateTo("/articles");
    }

    private async Task PublishArticle()
    {
        generatedArticle.Status = ArticleStatus.InReview;
        await Api.SaveArticleAsync(generatedArticle);
        Navigation.NavigateTo("/articles");
    }
}
```

### 4. Bicep Infrastructure Template

**main.bicep**
```bicep
targetScope = 'subscription'

@description('Environment name (dev, staging, prod)')
param environmentName string = 'dev'

@description('Azure region for resources')
param location string = 'australiaeast'

@description('Resource naming prefix')
param resourcePrefix string = 'kb'

// Resource Group
resource rg 'Microsoft.Resources/resourceGroups@2021-04-01' = {
  name: '${resourcePrefix}-${environmentName}-rg'
  location: location
}

// App Service Plan
module appServicePlan 'modules/appService.bicep' = {
  name: 'appServicePlan'
  scope: rg
  params: {
    name: '${resourcePrefix}-${environmentName}-asp'
    location: location
    sku: 'B1'
  }
}

// SQL Database
module sqlDatabase 'modules/sqlDatabase.bicep' = {
  name: 'sqlDatabase'
  scope: rg
  params: {
    serverName: '${resourcePrefix}-${environmentName}-sql'
    databaseName: 'KnowledgeBase'
    location: location
    administratorLogin: 'sqladmin'
    administratorPassword: keyVault.outputs.sqlPassword
  }
}

// Azure OpenAI
module openAI 'modules/openai.bicep' = {
  name: 'openAI'
  scope: rg
  params: {
    name: '${resourcePrefix}-${environmentName}-openai'
    location: 'australiaeast'
    deployments: [
      {
        name: 'gpt-4-router'
        model: 'gpt-4'
        version: '0613'
        capacity: 10
      }
      {
        name: 'gpt-4-howto'
        model: 'gpt-4'
        version: '0613'
        capacity: 20
      }
      {
        name: 'gpt-4-troubleshooting'
        model: 'gpt-4'
        version: '0613'
        capacity: 20
      }
    ]
  }
}

// Key Vault
module keyVault 'modules/keyVault.bicep' = {
  name: 'keyVault'
  scope: rg
  params: {
    name: '${resourcePrefix}-${environmentName}-kv'
    location: location
  }
}

// Application Insights
module appInsights 'modules/monitoring.bicep' = {
  name: 'appInsights'
  scope: rg
  params: {
    name: '${resourcePrefix}-${environmentName}-ai'
    location: location
  }
}

output resourceGroupName string = rg.name
output sqlServerName string = sqlDatabase.outputs.serverName
output openAIEndpoint string = openAI.outputs.endpoint
```

---

## API Endpoints to Implement

### Articles API

```
POST   /api/articles                    # Create article (triggers AI)
GET    /api/articles                    # List articles (paginated, filtered)
GET    /api/articles/{id}               # Get article details
PUT    /api/articles/{id}               # Update article
DELETE /api/articles/{id}               # Soft delete article
POST   /api/articles/{id}/publish       # Publish article
POST   /api/articles/{id}/feedback      # Submit rating/feedback
GET    /api/articles/{id}/versions      # Get version history
```

### AI Agent API

```
POST   /api/agents/classify             # Classify content type
POST   /api/agents/generate             # Generate article with specified agent
POST   /api/agents/evaluate             # Evaluate article quality
POST   /api/agents/find-related         # Find related articles
```

### Search API

```
GET    /api/search?q={query}            # Full-text search
GET    /api/search/suggest?q={query}    # Search suggestions
```

### Modules API

```
GET    /api/modules                     # List modules
POST   /api/modules                     # Create module (admin)
PUT    /api/modules/{id}                # Update module (admin)
```

### Users API

```
GET    /api/users/me                    # Get current user
GET    /api/users                       # List users (admin)
PUT    /api/users/{id}/role             # Update user role (admin)
```

---

## Environment Variables / Configuration

```json
{
  "AzureOpenAI": {
    "Endpoint": "https://kb-dev-openai.openai.azure.com/",
    "ApiKey": "from-keyvault",
    "DeploymentNames": {
      "Router": "gpt-4-router",
      "HowTo": "gpt-4-howto",
      "Troubleshooting": "gpt-4-troubleshooting",
      "Evaluator": "gpt-4-evaluator"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "from-keyvault"
  },
  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/",
    "TenantId": "from-keyvault",
    "ClientId": "from-keyvault"
  },
  "ApplicationInsights": {
    "InstrumentationKey": "from-keyvault"
  }
}
```

---

## Testing Strategy

### Unit Tests
- Agent service logic
- Repository methods
- Business logic services
- API controller actions

### Integration Tests
- End-to-end article creation flow
- Database operations
- Azure OpenAI integration
- Authentication/authorization

### Test Data
- Sample articles for each type
- Test users with different roles
- Test modules

---

## Deployment Pipeline (Azure DevOps)

### Build Pipeline (azure-pipelines-build.yml)
```yaml
trigger:
  branches:
    include:
    - main
    - develop

pool:
  vmImage: 'ubuntu-latest'

variables:
  buildConfiguration: 'Release'

stages:
- stage: Build
  jobs:
  - job: BuildAPI
    steps:
    - task: UseDotNet@2
      inputs:
        version: '8.x'
    
    - task: DotNetCoreCLI@2
      displayName: 'Restore'
      inputs:
        command: 'restore'
        projects: '**/*.csproj'
    
    - task: DotNetCoreCLI@2
      displayName: 'Build'
      inputs:
        command: 'build'
        projects: '**/KnowledgeBase.Api.csproj'
        arguments: '--configuration $(buildConfiguration)'
    
    - task: DotNetCoreCLI@2
      displayName: 'Test'
      inputs:
        command: 'test'
        projects: '**/*Tests.csproj'
        arguments: '--configuration $(buildConfiguration) --collect:"XPlat Code Coverage"'
    
    - task: DotNetCoreCLI@2
      displayName: 'Publish'
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: '**/KnowledgeBase.Api.csproj'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/api'
    
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)/api'
        artifactName: 'api'

  - job: BuildWeb
    steps:
    - task: DotNetCoreCLI@2
      displayName: 'Publish Blazor'
      inputs:
        command: 'publish'
        projects: '**/KnowledgeBase.Web.csproj'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/web'
    
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)/web'
        artifactName: 'web'

- stage: Infrastructure
  jobs:
  - job: DeployBicep
    steps:
    - task: AzureCLI@2
      displayName: 'Deploy Infrastructure'
      inputs:
        azureSubscription: 'Azure-Subscription'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          az deployment sub create \
            --location australiaeast \
            --template-file infrastructure/bicep/main.bicep \
            --parameters @infrastructure/bicep/parameters/dev.parameters.json
```

### Release Pipeline
- Deploy to Dev automatically on commit to develop
- Deploy to Staging on PR merge to main
- Deploy to Prod with manual approval

---

## Key Decision Log

1. **Multiple Specialized Agents vs Single Agent**: DECISION: Multiple agents for better quality per type
2. **UI Framework**: DECISION: Blazor Server (stay in .NET ecosystem)
3. **Component Library**: DECISION: MudBlazor (free, MIT license, good for side project)
4. **Database**: DECISION: Azure SQL (team's strength) + Azure AI Search for semantic search
5. **Prompt Storage**: DECISION: Azure App Configuration for flexibility

---

## Success Criteria (Phase 1)

- [ ] User can paste content and create a How-To article
- [ ] Router agent correctly classifies content with >80% accuracy
- [ ] How-To agent generates structured articles with steps
- [ ] Articles saved to database with proper schema
- [ ] User can view and rate articles
- [ ] Basic search works (SQL full-text)
- [ ] CI/CD pipeline deploys to Azure
- [ ] Infrastructure fully defined in Bicep
- [ ] All code has >70% test coverage

---

## Getting Started Checklist

### For Coding Agent (Claude Code or similar):

1. **Setup Azure Environment**
   - [ ] Create Azure subscription
   - [ ] Setup Azure DevOps organization
   - [ ] Create service principal for deployments
   - [ ] Configure secrets in Azure DevOps

2. **Initialize Solution**
   - [ ] Create .NET solution structure
   - [ ] Setup Git repository
   - [ ] Create .gitignore
   - [ ] Add README.md

3. **Build Infrastructure**
   - [ ] Write Bicep templates
   - [ ] Deploy to dev environment
   - [ ] Verify all resources created

4. **Database Setup**
   - [ ] Create schema scripts
   - [ ] Run migrations
   - [ ] Seed test data

5. **Implement Core API**
   - [ ] Setup EF Core with models
   - [ ] Implement repositories
   - [ ] Create Azure OpenAI client
   - [ ] Implement Router Agent
   - [ ] Implement How-To Agent
   - [ ] Implement Article Orchestrator
   - [ ] Create API controllers
   - [ ] Add authentication

6. **Build Blazor UI**
   - [ ] Setup Blazor project
   - [ ] Add MudBlazor
   - [ ] Create layout
   - [ ] Build article creation page
   - [ ] Build article list page
   - [ ] Build article detail page
   - [ ] Add authentication

7. **Testing**
   - [ ] Write unit tests
   - [ ] Write integration tests
   - [ ] Test end-to-end workflow

8. **DevOps**
   - [ ] Setup build pipelines
   - [ ] Setup release pipelines
   - [ ] Configure environments
   - [ ] Test deployments

---

## Important Files Reference

All planning artifacts are in `/home/<USER>/` directory:
- `knowledge-base-platform-scope.md` - Full project scope and phased plan
- `agent-architecture-strategy.md` - Detailed agent design and prompts
- `article-types-and-data-model.md` - Article schemas and examples
- `ui-component-library-comparison.md` - UI framework decision

---

## Next Immediate Actions

1. **Start with Infrastructure**: Get Azure resources deployed first
2. **Database Schema**: Create and test tables
3. **Router + How-To Agents**: Core functionality
4. **Simple UI**: Just enough to test the workflow
5. **Iterate**: Get feedback and improve

---

## Questions for Clarification

Before starting implementation, please confirm:

1. Azure subscription details and access
2. Azure DevOps organization name
3. Preferred Azure region (australiaeast?)
4. Domain name for the application (if any)
5. Azure AD tenant for authentication
6. Any company naming conventions to follow
7. Git repository URL

---

## Contact / Support

- Project Owner: [Name]
- Tech Lead: [Name]
- Azure Admin: [Name]

---

**VERSION**: 1.0  
**LAST UPDATED**: January 29, 2026  
**STATUS**: Ready for Implementation - Phase 1

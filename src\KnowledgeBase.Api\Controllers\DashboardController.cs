using System.Text.Json;
using KnowledgeBase.Api.Models.DTOs;
using KnowledgeBase.Api.Models.Responses;
using KnowledgeBase.Core.Enums;
using KnowledgeBase.Core.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace KnowledgeBase.Api.Controllers;

/// <summary>
/// API endpoints for dashboard statistics and summaries.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class DashboardController : ControllerBase
{
    private readonly IArticleService _articleService;
    private readonly IModuleService _moduleService;
    private readonly ILogger<DashboardController> _logger;

    public DashboardController(
        IArticleService articleService,
        IModuleService moduleService,
        ILogger<DashboardController> logger)
    {
        _articleService = articleService;
        _moduleService = moduleService;
        _logger = logger;
    }

    /// <summary>
    /// Get dashboard statistics.
    /// </summary>
    [HttpGet("stats")]
    [ProducesResponseType(typeof(ApiResponse<DashboardStatsDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<DashboardStatsDto>>> GetStats(CancellationToken cancellationToken)
    {
        var allArticles = await _articleService.GetArticlesAsync(
            new ArticleSearchCriteria { PageSize = 1 }, cancellationToken);

        var publishedArticles = await _articleService.GetArticlesAsync(
            new ArticleSearchCriteria { Status = ArticleStatus.Published, PageSize = 1 }, cancellationToken);

        var draftArticles = await _articleService.GetArticlesAsync(
            new ArticleSearchCriteria { Status = ArticleStatus.Draft, PageSize = 1 }, cancellationToken);

        var inReviewArticles = await _articleService.GetArticlesAsync(
            new ArticleSearchCriteria { Status = ArticleStatus.InReview, PageSize = 1 }, cancellationToken);

        var modules = await _moduleService.GetAllAsync(false, cancellationToken);

        // Get a larger page to sum up view counts
        var articlesForViews = await _articleService.GetArticlesAsync(
            new ArticleSearchCriteria { PageSize = 10000 }, cancellationToken);

        var stats = new DashboardStatsDto
        {
            TotalArticles = allArticles.TotalCount,
            PublishedArticles = publishedArticles.TotalCount,
            DraftArticles = draftArticles.TotalCount,
            PendingReview = inReviewArticles.TotalCount,
            TotalModules = modules.Count,
            TotalViews = articlesForViews.Items.Sum(a => a.ViewCount)
        };

        return Ok(ApiResponse<DashboardStatsDto>.Ok(stats));
    }

    /// <summary>
    /// Get recent articles for dashboard.
    /// </summary>
    [HttpGet("recent")]
    [ProducesResponseType(typeof(ApiResponse<List<ArticleSummaryDto>>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<List<ArticleSummaryDto>>>> GetRecentArticles(
        [FromQuery] int count = 5,
        CancellationToken cancellationToken = default)
    {
        var criteria = new ArticleSearchCriteria
        {
            SortBy = "LastModifiedDate",
            SortDescending = true,
            PageSize = Math.Min(count, 20)
        };

        var (articles, _) = await _articleService.GetArticlesAsync(criteria, cancellationToken);

        var dtos = articles.Select(a => new ArticleSummaryDto
        {
            ArticleId = a.ArticleId,
            Title = a.Title,
            Summary = a.Summary,
            ArticleType = a.ArticleType.ToString(),
            Status = a.Status.ToString(),
            ModuleName = a.Module?.Name ?? string.Empty,
            Category = a.Category,
            Tags = TryParseStringArray(a.Tags),
            ViewCount = a.ViewCount,
            CreatedDate = a.CreatedDate,
            LastModifiedDate = a.LastModifiedDate,
            Rating = a.Rating
        }).ToList();

        return Ok(ApiResponse<List<ArticleSummaryDto>>.Ok(dtos));
    }

    private static List<string> TryParseStringArray(string? json)
    {
        if (string.IsNullOrEmpty(json)) return new List<string>();
        try { return JsonSerializer.Deserialize<List<string>>(json) ?? new List<string>(); }
        catch { return new List<string>(); }
    }
}

/// <summary>
/// Dashboard statistics DTO.
/// </summary>
public class DashboardStatsDto
{
    public int TotalArticles { get; set; }
    public int PublishedArticles { get; set; }
    public int DraftArticles { get; set; }
    public int PendingReview { get; set; }
    public int TotalModules { get; set; }
    public int TotalViews { get; set; }
}

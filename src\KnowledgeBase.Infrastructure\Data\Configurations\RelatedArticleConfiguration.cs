using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KnowledgeBase.Infrastructure.Data.Configurations;

public class RelatedArticleConfiguration : IEntityTypeConfiguration<RelatedArticle>
{
    public void Configure(EntityTypeBuilder<RelatedArticle> builder)
    {
        builder.ToTable("RelatedArticles");

        builder.Has<PERSON>ey(r => r.Id);

        builder.Property(r => r.RelationshipType)
            .HasConversion<string>()
            .HasMaxLength(50)
            .HasDefaultValue(RelationshipType.Related);

        builder.Property(r => r.RelevanceScore)
            .HasPrecision(3, 2);

        builder.Property(r => r.CreatedDate)
            .HasDefaultValueSql("GETUTCDATE()");

        builder.HasOne(r => r.Article)
            .WithMany(a => a.RelatedArticles)
            .HasForeignKey(r => r.ArticleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(r => r.Related)
            .WithMany(a => a.RelatedTo)
            .HasForeignKey(r => r.RelatedArticleId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasIndex(r => new { r.ArticleId, r.RelatedArticleId })
            .IsUnique();

        // Prevent self-referencing
        builder.HasCheckConstraint("CK_RelatedArticles_NotSelf", "[ArticleId] != [RelatedArticleId]");
    }
}

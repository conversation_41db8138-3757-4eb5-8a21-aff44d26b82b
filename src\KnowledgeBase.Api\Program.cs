using KnowledgeBase.Core.Interfaces;
using KnowledgeBase.Infrastructure;
using KnowledgeBase.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;

var builder = WebApplication.CreateBuilder(args);

// Add infrastructure services (includes DbContext, repositories, AI agents)
if (builder.Environment.IsDevelopment() && string.IsNullOrEmpty(builder.Configuration["AzureOpenAI:Endpoint"]))
{
    // Use mock AI in development if Azure OpenAI is not configured
    builder.Services.AddInfrastructureWithMockAI(builder.Configuration);
}
else
{
    builder.Services.AddInfrastructure(builder.Configuration);
}

// Controllers with JSON options
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
        options.JsonSerializerOptions.WriteIndented = true;
    });

// CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowBlazorApp", policy =>
    {
        var origins = builder.Configuration.GetValue<string>("AllowedOrigins") ?? "https://localhost:5001";
        policy.WithOrigins(origins.Split(','))
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials();
    });
});

// Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Knowledge Base API",
        Version = "v1",
        Description = "AI-powered Knowledge Base Platform API"
    });
});

// Health checks
builder.Services.AddHealthChecks()
    .AddDbContextCheck<KnowledgeBaseContext>("database");

var app = builder.Build();

// Initialize database and search index
using (var scope = app.Services.CreateScope())
{
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

    // Apply pending migrations and seed data
    try
    {
        var db = scope.ServiceProvider.GetRequiredService<KnowledgeBaseContext>();
        await db.Database.MigrateAsync();
        logger.LogInformation("Database migrations applied successfully");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Failed to apply database migrations");
    }

    // Ensure search index exists
    try
    {
        var searchService = scope.ServiceProvider.GetRequiredService<ISearchService>();
        await searchService.EnsureIndexExistsAsync();
        logger.LogInformation("Search index initialized");
    }
    catch (Exception ex)
    {
        logger.LogWarning(ex, "Failed to initialize search index. Search may not work until index is created.");
    }
}

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(options =>
    {
        options.SwaggerEndpoint("/swagger/v1/swagger.json", "Knowledge Base API v1");
        options.RoutePrefix = "swagger";
    });

    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/error");
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseCors("AllowBlazorApp");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

app.Run();

@inject NavigationManager Navigation

<MudNavMenu>
    <MudText Typo="Typo.h6" Class="px-4 py-3">Navigation</MudText>
    <MudDivider Class="mb-2" />

    <MudNavLink Href="/" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Dashboard">
        Dashboard
    </MudNavLink>

    <MudNavGroup Title="Articles" Icon="@Icons.Material.Filled.Article" Expanded="true">
        <MudNavLink Href="/articles" Icon="@Icons.Material.Filled.List">
            All Articles
        </MudNavLink>
        <MudNavLink Href="/articles/create" Icon="@Icons.Material.Filled.Add">
            Create Article
        </MudNavLink>
        <MudNavLink Href="/articles/drafts" Icon="@Icons.Material.Filled.Edit">
            My Drafts
        </MudNavLink>
        <MudNavLink Href="/articles/review" Icon="@Icons.Material.Filled.RateReview">
            Pending Review
        </MudNavLink>
    </MudNavGroup>

    <MudNavGroup Title="By Type" Icon="@Icons.Material.Filled.Category">
        <MudNavLink Href="/articles?type=HowTo" Icon="@Icons.Material.Filled.MenuBook">
            How-To Guides
        </MudNavLink>
        <MudNavLink Href="/articles?type=Troubleshooting" Icon="@Icons.Material.Filled.BugReport">
            Troubleshooting
        </MudNavLink>
        <MudNavLink Href="/articles?type=ReleaseNote" Icon="@Icons.Material.Filled.NewReleases">
            Release Notes
        </MudNavLink>
        <MudNavLink Href="/articles?type=Faq" Icon="@Icons.Material.Filled.QuestionAnswer">
            FAQ
        </MudNavLink>
        <MudNavLink Href="/articles?type=ProductOverview" Icon="@Icons.Material.Filled.Inventory">
            Product Overviews
        </MudNavLink>
        <MudNavLink Href="/articles?type=BestPractice" Icon="@Icons.Material.Filled.Star">
            Best Practices
        </MudNavLink>
    </MudNavGroup>

    <MudNavLink Href="/modules" Icon="@Icons.Material.Filled.Folder">
        Modules
    </MudNavLink>

    <MudDivider Class="my-2" />

    <MudNavLink Href="/analytics" Icon="@Icons.Material.Filled.Analytics">
        Analytics
    </MudNavLink>

    <MudNavLink Href="/settings" Icon="@Icons.Material.Filled.Settings">
        Settings
    </MudNavLink>
</MudNavMenu>


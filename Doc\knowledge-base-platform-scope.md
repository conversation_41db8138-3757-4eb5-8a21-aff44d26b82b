# Knowledge Base Platform - Project Scope & Phased Delivery Plan

## Executive Summary

Building an AI-powered knowledge base platform for product and service companies using .NET Core, Blazor, and Azure services. The platform enables rapid knowledge article creation through AI agents, maintains a robust repository with semantic search, and supports multiple consumption patterns.

**Budget Target**: ~$400 USD/month (flexible based on value)
**Initial Users**: Up to 100 users, single country
**Tech Stack**: .NET Core 8+, Blazor, Azure SQL, Azure OpenAI, Azure AI Search, Bicep, Azure DevOps

---

## Core Data Model

### Knowledge Article Schema

```
Article:
- ArticleId (GUID, PK)
- Title (string, 200 chars)
- Summary (string, 500 chars)
- Content (rich text/markdown)
- Module (FK to Module table)
- TicketReferences (many-to-many, FK to Ticket table)
- Tags (array/many-to-many)
- Category (enum: Product, Service, Troubleshooting, HowTo, FAQ, etc.)
- Status (enum: Draft, InReview, Published, Archived)
- CreatedBy (FK to User)
- CreatedDate (datetime)
- LastModifiedBy (FK to User)
- LastModifiedDate (datetime)
- LastReviewedDate (datetime)
- ReviewedBy (FK to User - SME)
- Rating (decimal, 0-5)
- ViewCount (int)
- HelpfulCount (int)
- NotHelpfulCount (int)
- Version (int)
- RelatedArticles (many-to-many, self-referential)

Module:
- ModuleId (GUID, PK)
- Name (string)
- Description (string)
- SMEUserId (FK to User - Subject Matter Expert)
- IsActive (bool)

Ticket:
- TicketId (string, PK - external reference)
- TicketSystem (enum: Jira, Azure DevOps, etc.)
- Title (string)
- Description (string)

ArticleVersion:
- VersionId (GUID, PK)
- ArticleId (FK)
- VersionNumber (int)
- Content (full article snapshot)
- ModifiedBy (FK to User)
- ModifiedDate (datetime)
- ChangeDescription (string)

User:
- UserId (GUID, PK)
- Email (string)
- Name (string)
- Role (enum: Guest, User, PowerUser, Admin)
- IsActive (bool)
```

---

## Three Pillars

1. **Solid Data Generation**: AI-powered extraction and creation from multiple sources
2. **Solid Data Store**: Hybrid SQL + Vector store with versioning and relationships
3. **Solid Data Search**: Semantic search with relevance ranking and filtering

---

## Phased Delivery Plan

---

## **PHASE 1: Foundation & Web Paste Tool** 
**Duration**: 4-6 weeks  
**Goal**: Establish core platform with basic AI-powered article creation

### Requirements

**Functional:**
- User authentication and authorization (Azure AD B2C or Entra ID)
- Web-based paste tool for text input
- AI agent extracts and creates knowledge articles from pasted content
- Basic article CRUD operations
- Article status workflow: Draft → InReview → Published
- Basic search functionality (SQL full-text search)
- Simple rating mechanism (helpful/not helpful)

**Non-Functional:**
- Secure API with JWT authentication
- Response time < 3 seconds for article creation
- Support 10 concurrent users initially
- Audit logging for all data changes

### Functionality Delivered

1. **User Management**
   - User registration/login via Azure AD
   - Role-based access control (Admin, PowerUser, User, Guest)
   - User profile management

2. **Web Paste Tool**
   - Blazor page with rich text editor
   - Paste detection and formatting cleanup
   - Preview before submission
   - Submit to AI agent for processing

3. **AI Article Creation Agent**
   - Azure OpenAI integration (GPT-4)
   - Prompt engineering for article extraction
   - Extract: Title, Summary, Content, suggested Tags, Category
   - Return structured article data to user for review/edit

4. **Article Management**
   - Create, Read, Update, Delete articles
   - Draft saving
   - Submit for review (status change)
   - Simple approval by PowerUser/Admin
   - Article versioning (basic - save on publish)

5. **Basic Search**
   - SQL full-text search on Title, Summary, Content
   - Filter by Category, Module, Status
   - Sort by Date, Rating, Views

6. **Article Rating**
   - Thumbs up/down (helpful/not helpful)
   - View count tracking
   - Display rating summary

### Architecture Components

```
┌─────────────────────────────────────────────────────────────┐
│                         USER LAYER                           │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │          Blazor WebAssembly/Server App             │    │
│  │  - Authentication (Azure AD)                       │    │
│  │  - Paste Tool UI                                   │    │
│  │  - Article Management UI                           │    │
│  │  - Search UI                                       │    │
│  └────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                            │
                            │ HTTPS/REST
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                      API LAYER (Azure)                       │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │     Azure App Service (.NET Core 8 Web API)        │    │
│  │  - Controllers (Article, User, Search)             │    │
│  │  - Authentication Middleware                        │    │
│  │  - Authorization Policies                          │    │
│  │  - API Versioning                                  │    │
│  └────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                            │
                ┌───────────┴───────────┐
                ▼                       ▼
┌──────────────────────────┐  ┌──────────────────────────┐
│   BUSINESS LOGIC LAYER   │  │      AI AGENT LAYER      │
│                          │  │                          │
│  - Article Service       │  │  ┌────────────────────┐  │
│  - User Service          │  │  │ Azure OpenAI       │  │
│  - Search Service        │  │  │ - GPT-4            │  │
│  - Workflow Service      │  │  │ - Article Extract  │  │
│  - Rating Service        │  │  │   Agent            │  │
│  - Audit Service         │  │  └────────────────────┘  │
└──────────────────────────┘  └──────────────────────────┘
                │
                ▼
┌─────────────────────────────────────────────────────────────┐
│                     DATA LAYER (Azure)                       │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │              Azure SQL Database                     │    │
│  │  - Articles (with full-text search)                │    │
│  │  - Users                                           │    │
│  │  - Modules                                         │    │
│  │  - Tickets                                         │    │
│  │  - ArticleVersions                                 │    │
│  │  - AuditLog                                        │    │
│  └────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

### Azure Resources (Phase 1)

| Resource | SKU/Tier | Purpose | Est. Monthly Cost |
|----------|----------|---------|-------------------|
| Azure App Service | B1 (Basic) | Web API hosting | $13 |
| Azure App Service | B1 (Basic) | Blazor app hosting | $13 |
| Azure SQL Database | Basic (5 DTU) | Primary data store | $5 |
| Azure OpenAI | Pay-as-you-go | GPT-4 for article extraction | $50-100* |
| Azure AD B2C/Entra | Free tier | Authentication | $0 |
| Azure Key Vault | Standard | Secrets management | $0.03 |
| Azure Application Insights | Basic | Monitoring & logging | $5-10 |

**Phase 1 Estimated Total**: ~$86-141/month  
*OpenAI cost assumes ~500 article extractions/month at ~2K tokens each

### Work Breakdown

**Infrastructure (Bicep):**
- [ ] Azure resource group setup
- [ ] App Service plans and apps
- [ ] Azure SQL database with firewall rules
- [ ] Azure OpenAI service provisioning
- [ ] Key Vault for secrets
- [ ] Application Insights
- [ ] Managed identities configuration

**Database (SQL):**
- [ ] Database schema creation scripts
- [ ] Initial seed data (test users, modules)
- [ ] Full-text search index setup
- [ ] Stored procedures for CRUD operations
- [ ] Migration scripts framework

**Backend API (.NET Core):**
- [ ] Project structure and solution setup
- [ ] Entity Framework Core models
- [ ] Repository pattern implementation
- [ ] Article API controller
- [ ] User API controller
- [ ] Search API controller
- [ ] Authentication middleware (JWT)
- [ ] Authorization policies by role
- [ ] OpenAI service integration
- [ ] Article extraction agent implementation
- [ ] Unit tests for services
- [ ] Integration tests for APIs

**Frontend (Blazor):**
- [ ] Blazor project setup (Server or WASM decision)
- [ ] Authentication components
- [ ] Layout and navigation
- [ ] Paste tool page/component
- [ ] Article list/grid component
- [ ] Article detail/view component
- [ ] Article edit component
- [ ] Search component
- [ ] Rating component
- [ ] User profile component

**DevOps (Azure DevOps):**
- [ ] Git repository setup
- [ ] Branch strategy (main, develop, feature/*)
- [ ] Build pipeline for API
- [ ] Build pipeline for Blazor app
- [ ] Release pipeline for API
- [ ] Release pipeline for Blazor app
- [ ] Bicep deployment pipeline
- [ ] Environment setup (Dev, Staging, Prod)

---

## **PHASE 2: Document Upload & Enhanced AI** 
**Duration**: 3-4 weeks  
**Goal**: Support document uploads and improve AI processing

### Requirements

**Functional:**
- Upload Word documents and PDFs
- AI agent extracts content from documents
- Image extraction and storage from documents
- Enhanced article relationship detection
- Module and SME management
- Article review workflow with SME assignment
- Improved search with filtering

**Non-Functional:**
- Support files up to 10MB
- Process documents within 30 seconds
- Store images efficiently in blob storage

### Functionality Delivered

1. **Document Upload**
   - Drag-and-drop file upload component
   - Support .docx, .pdf formats
   - Progress indicator during upload
   - File validation and sanitization

2. **Document Processing Agent**
   - Azure Document Intelligence (Form Recognizer) integration
   - Extract text from Word/PDF
   - Extract images from documents
   - Store images in Azure Blob Storage
   - Pass extracted content to article creation agent
   - Handle multi-page documents

3. **Article Relationship Agent**
   - New AI agent using embeddings
   - Generate embeddings for new articles (Azure OpenAI)
   - Find similar existing articles using vector similarity
   - Suggest related articles to user during creation
   - Auto-populate RelatedArticles field

4. **Module & SME Management**
   - Admin UI for creating/managing modules
   - Assign SME (Subject Matter Expert) to modules
   - SME dashboard showing articles pending review in their modules

5. **Enhanced Review Workflow**
   - Article submission routes to module SME
   - SME can approve, reject (with comments), or request changes
   - Email notifications for review requests
   - Review history tracking

6. **Image Management**
   - Display images inline in article content
   - Image library for reuse across articles
   - Thumbnail generation
   - Image metadata (source document, upload date)

### Architecture Components - Phase 2 Additions

```
┌─────────────────────────────────────────────────────────────┐
│                    NEW/UPDATED COMPONENTS                    │
│                                                              │
│  Blazor App:                                                │
│  - Document Upload Component                                │
│  - Module Management UI                                     │
│  - SME Dashboard                                            │
│  - Review Workflow UI                                       │
│  - Image Gallery Component                                  │
│                                                              │
│  API Layer:                                                 │
│  - Document Controller (upload, process)                    │
│  - Module Controller                                        │
│  - Review Controller                                        │
│  - Image Controller                                         │
│                                                              │
│  AI Agent Layer:                                            │
│  ┌────────────────────────────────────────────────────┐    │
│  │  Document Processing Agent                         │    │
│  │  - Azure Document Intelligence integration         │    │
│  │  - Text extraction                                 │    │
│  │  - Image extraction                                │    │
│  └────────────────────────────────────────────────────┘    │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │  Article Relationship Agent                        │    │
│  │  - Azure OpenAI Embeddings (text-embedding-ada-002)│    │
│  │  - Vector similarity search                        │    │
│  │  - Relationship suggestion engine                  │    │
│  └────────────────────────────────────────────────────┘    │
│                                                              │
│  Data Layer:                                                │
│  ┌────────────────────────────────────────────────────┐    │
│  │  Azure Blob Storage                                │    │
│  │  - Documents (uploaded files)                      │    │
│  │  - Images (extracted/uploaded)                     │    │
│  │  - Thumbnails                                      │    │
│  └────────────────────────────────────────────────────┘    │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │  Azure SQL - New Tables                           │    │
│  │  - Images (metadata)                               │    │
│  │  - Documents (upload history)                      │    │
│  │  - ReviewWorkflow (audit trail)                    │    │
│  │  - Notifications                                   │    │
│  └────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

### Azure Resources - Phase 2 Additions

| Resource | SKU/Tier | Purpose | Est. Monthly Cost |
|----------|----------|---------|-------------------|
| Azure Blob Storage | Standard LRS | Document and image storage | $5-10 |
| Azure Document Intelligence | S0 (Standard) | Document text/image extraction | $15-30* |
| Azure Communication Services | Pay-as-you-go | Email notifications | $1-5 |
| Azure OpenAI - Embeddings | Pay-as-you-go | Article embeddings for relationships | $10-20** |

**Phase 2 Additional Cost**: ~$31-65/month  
**Cumulative Total**: ~$117-206/month

*Assumes 200 document extractions/month  
**Assumes embedding generation for 1000 articles/updates

### Work Breakdown

**Infrastructure:**
- [ ] Azure Blob Storage account with containers
- [ ] Azure Document Intelligence service
- [ ] Azure Communication Services for email
- [ ] Update Bicep templates

**Database:**
- [ ] Images table
- [ ] Documents table
- [ ] ReviewWorkflow table
- [ ] Notifications table
- [ ] Update article schema for image references
- [ ] Migration scripts

**Backend API:**
- [ ] Document upload endpoint with streaming
- [ ] Document Intelligence integration service
- [ ] Image processing service (thumbnails)
- [ ] Blob Storage service wrapper
- [ ] Article embedding service (OpenAI)
- [ ] Relationship detection service
- [ ] Module management endpoints
- [ ] Review workflow endpoints
- [ ] Notification service (email)
- [ ] Background job for document processing (Hangfire or Azure Functions)
- [ ] Unit and integration tests

**Frontend:**
- [ ] File upload component (drag-drop)
- [ ] Document processing status page
- [ ] Module management pages
- [ ] SME dashboard
- [ ] Review workflow UI
- [ ] Image gallery component
- [ ] Notification center component

**DevOps:**
- [ ] Update pipelines for new services
- [ ] Add integration tests for document processing
- [ ] Performance testing for file uploads

---

## **PHASE 3: Teams/Slack Integration & Vector Search** 
**Duration**: 4-5 weeks  
**Goal**: Automated knowledge capture from conversations and semantic search

### Requirements

**Functional:**
- Teams/Slack bot integration
- Capture and process conversation threads
- AI agent creates articles from conversations
- Azure AI Search with vector capabilities
- Semantic search with ranking
- Conversation-based article suggestions
- Bulk article review and curation

**Non-Functional:**
- Real-time conversation monitoring
- Process conversation threads within 1 minute
- Semantic search response < 2 seconds
- Support 1000+ articles in search index

### Functionality Delivered

1. **Teams Integration**
   - Bot registration in Microsoft Teams
   - Listen to channels for specific keywords/hashtags
   - Capture conversation threads
   - User command to create article from thread
   - Post article link back to Teams channel

2. **Slack Integration**
   - Slack bot installation
   - Monitor channels with specific keywords
   - Capture conversation threads
   - Slash command to create article from thread
   - Post article link back to Slack channel

3. **Conversation Processing Agent**
   - Extract key information from multi-turn conversations
   - Identify participants and summarize contributions
   - Create article with conversation context
   - Link to original conversation thread
   - Handle code snippets, images, and attachments

4. **Azure AI Search Integration**
   - Index all articles in Azure AI Search
   - Generate embeddings for vector search
   - Hybrid search (keyword + semantic)
   - Faceted search (by Module, Category, Tags)
   - Search result ranking and relevance tuning

5. **Advanced Search UI**
   - Natural language query support
   - Search suggestions and autocomplete
   - Faceted filtering sidebar
   - Search result highlighting
   - "More like this" feature
   - Search analytics and popular queries

6. **Article Curation Dashboard**
   - Identify stale articles (LastReviewedDate > 6 months)
   - Low-rated articles (Rating < 2.5)
   - Articles with no views
   - Bulk operations (archive, delete, reassign)
   - Article health metrics

### Architecture Components - Phase 3 Additions

```
┌─────────────────────────────────────────────────────────────┐
│                    INTEGRATION LAYER                         │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │         Teams Bot (Azure Bot Service)              │    │
│  │  - Bot Framework SDK                               │    │
│  │  - Message handler                                 │    │
│  │  - Command processor                               │    │
│  └────────────────────────────────────────────────────┘    │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │         Slack Bot (Slack API)                      │    │
│  │  - Slack Bolt SDK                                  │    │
│  │  - Event listener                                  │    │
│  │  - Command handler                                 │    │
│  └────────────────────────────────────────────────────┘    │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │    Conversation Processing Agent                   │    │
│  │  - Multi-turn conversation analyzer                │    │
│  │  - Context extraction                              │    │
│  │  - Participant attribution                         │    │
│  │  - Summary generation                              │    │
│  └────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                     SEARCH LAYER                             │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │         Azure AI Search (Cognitive Search)         │    │
│  │  - Vector search index                             │    │
│  │  - Semantic ranking                                │    │
│  │  - Faceted navigation                              │    │
│  │  - Search suggestions                              │    │
│  │  - Custom ranking profiles                         │    │
│  └────────────────────────────────────────────────────┘    │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │         Search Indexer Service                     │    │
│  │  - Real-time index updates                         │    │
│  │  - Bulk indexing jobs                              │    │
│  │  - Embedding generation                            │    │
│  └────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

### Azure Resources - Phase 3 Additions

| Resource | SKU/Tier | Purpose | Est. Monthly Cost |
|----------|----------|---------|-------------------|
| Azure AI Search | Basic | Semantic search with vector support | $75 |
| Azure Bot Service | S1 (Standard) | Teams bot hosting | $0.50/1000 msgs |
| Azure Functions | Consumption | Background processing | $5-10 |
| Azure Service Bus | Basic | Message queue for async processing | $0.05 |

**Phase 3 Additional Cost**: ~$80-86/month  
**Cumulative Total**: ~$197-292/month

### Work Breakdown

**Infrastructure:**
- [ ] Azure AI Search service
- [ ] Azure Bot Service for Teams
- [ ] Azure Functions for background jobs
- [ ] Azure Service Bus for message queuing
- [ ] Slack app registration and configuration
- [ ] Update Bicep templates

**Database:**
- [ ] ConversationThreads table
- [ ] SearchAnalytics table
- [ ] Migration scripts

**Backend API:**
- [ ] Teams bot implementation (Bot Framework)
- [ ] Slack bot implementation (Slack SDK)
- [ ] Conversation capture service
- [ ] Conversation processing agent
- [ ] Azure AI Search service wrapper
- [ ] Search indexer service (real-time + batch)
- [ ] Embedding generation for search
- [ ] Advanced search API endpoints
- [ ] Article curation service
- [ ] Background jobs for indexing
- [ ] Unit and integration tests

**Frontend:**
- [ ] Advanced search page with facets
- [ ] Search autocomplete component
- [ ] Article curation dashboard
- [ ] Search analytics page
- [ ] Conversation source indicator

**DevOps:**
- [ ] Teams bot deployment pipeline
- [ ] Slack bot deployment pipeline
- [ ] Azure Functions deployment
- [ ] Search index deployment automation

---

## **PHASE 4: SQL/CSV Import & Automation** 
**Duration**: 3-4 weeks  
**Goal**: Bulk data import and automation capabilities

### Requirements

**Functional:**
- Import articles from SQL databases
- Import articles from CSV files
- Field mapping UI for imports
- Scheduled import jobs
- MCP server for AI agent integration
- Export articles to various formats
- Release note generation automation
- Blog post generation

**Non-Functional:**
- Support importing 10,000+ records
- Import processing with progress tracking
- Data validation and error handling
- Scheduled jobs with monitoring

### Functionality Delivered

1. **SQL Database Import**
   - Connection string configuration (secure)
   - Table/view selection
   - Field mapping UI (drag-drop)
   - Data preview before import
   - Validation rules
   - Import scheduling (daily, weekly, monthly)
   - Import history and logs

2. **CSV File Import**
   - CSV file upload (large files supported)
   - Auto-detect column headers
   - Field mapping interface
   - Data type validation
   - Duplicate detection
   - Batch import with progress bar
   - Error handling and reporting

3. **MCP Server Implementation**
   - MCP protocol implementation
   - Expose knowledge base as MCP resource
   - Search endpoint for AI agents
   - Article retrieval endpoint
   - Create article endpoint
   - Authentication for MCP clients

4. **Export Capabilities**
   - Export articles to CSV
   - Export to Word document
   - Export to PDF
   - Export to Markdown
   - Filtered export (by date, module, category)
   - Scheduled export jobs

5. **Release Note Generator**
   - Select articles for release notes
   - Template-based formatting
   - Auto-categorize by module/category
   - Generate Word or PDF output
   - Version tracking for release notes

6. **Blog Post Generator**
   - Select articles for blog posts
   - AI-powered blog post generation
   - SEO optimization suggestions
   - Preview and edit before publish
   - Export to CMS-ready formats

7. **Automation Dashboard**
   - View scheduled jobs
   - Job execution history
   - Success/failure metrics
   - Job configuration management

### Architecture Components - Phase 4 Additions

```
┌─────────────────────────────────────────────────────────────┐
│                    IMPORT/EXPORT LAYER                       │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │         Import Service                             │    │
│  │  - SQL connector (Entity Framework)                │    │
│  │  - CSV parser                                      │    │
│  │  - Field mapper                                    │    │
│  │  - Data validator                                  │    │
│  │  - Batch processor                                 │    │
│  └────────────────────────────────────────────────────┘    │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │         Export Service                             │    │
│  │  - CSV exporter                                    │    │
│  │  - Word exporter (Open XML SDK)                    │    │
│  │  - PDF exporter                                    │    │
│  │  - Markdown exporter                               │    │
│  └────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                    AUTOMATION LAYER                          │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │     Azure Functions (Durable Functions)            │    │
│  │  - Scheduled import jobs                           │    │
│  │  - Scheduled export jobs                           │    │
│  │  - Long-running workflows                          │    │
│  └────────────────────────────────────────────────────┘    │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │     Release Note Generator Agent                   │    │
│  │  - Template engine                                 │    │
│  │  - Article aggregation                             │    │
│  │  - Formatting service                              │    │
│  └────────────────────────────────────────────────────┘    │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │     Blog Post Generator Agent                      │    │
│  │  - AI-powered content generation                   │    │
│  │  - SEO optimization                                │    │
│  │  - Template rendering                              │    │
│  └────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                    MCP SERVER LAYER                          │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │         MCP Server (.NET Implementation)           │    │
│  │  - Protocol handler                                │    │
│  │  - Resource endpoints (search, retrieve, create)   │    │
│  │  - Authentication/authorization                    │    │
│  │  - Rate limiting                                   │    │
│  └────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

### Azure Resources - Phase 4 Additions

| Resource | SKU/Tier | Purpose | Est. Monthly Cost |
|----------|----------|---------|-------------------|
| Azure Functions | Premium (EP1) | Durable functions for workflows | $75 |
| Azure Logic Apps | Consumption | Scheduled jobs orchestration | $10-15 |

**Phase 4 Additional Cost**: ~$85-90/month  
**Cumulative Total**: ~$282-382/month

### Work Breakdown

**Infrastructure:**
- [ ] Azure Functions Premium plan for Durable Functions
- [ ] Azure Logic Apps for scheduling
- [ ] Update Bicep templates

**Database:**
- [ ] ImportJobs table
- [ ] ExportJobs table
- [ ] JobHistory table
- [ ] ReleaseNotes table
- [ ] Migration scripts

**Backend API:**
- [ ] Import service (SQL, CSV)
- [ ] Field mapping engine
- [ ] Data validation service
- [ ] Export service (CSV, Word, PDF, Markdown)
- [ ] Azure Durable Functions for long-running imports
- [ ] MCP server implementation
- [ ] Release note generator
- [ ] Blog post generator (with AI)
- [ ] Job scheduler service
- [ ] Unit and integration tests

**Frontend:**
- [ ] Import wizard (step-by-step)
- [ ] Field mapping UI (drag-drop)
- [ ] Import job dashboard
- [ ] Export configuration page
- [ ] Release note builder
- [ ] Blog post builder
- [ ] Automation dashboard

**DevOps:**
- [ ] Azure Functions deployment
- [ ] MCP server deployment
- [ ] Scheduled job monitoring

---

## **PHASE 5: Advanced Features & Optimization** 
**Duration**: 3-4 weeks  
**Goal**: Polish, performance optimization, and advanced features

### Requirements

**Functional:**
- Advanced analytics and reporting
- AI-powered article recommendations
- Automated stale article alerts
- Multi-tenant support (for side project)
- API rate limiting and quotas
- Advanced admin dashboard
- Performance monitoring and optimization

**Non-Functional:**
- Page load time < 2 seconds
- API response time < 500ms (95th percentile)
- Support 500+ concurrent users
- 99.9% uptime SLA

### Functionality Delivered

1. **Analytics & Reporting**
   - Article usage metrics dashboard
   - User activity reports
   - Search analytics (popular queries, zero-result queries)
   - Module performance metrics
   - AI agent usage and cost tracking
   - Custom report builder
   - Export reports to Excel

2. **AI-Powered Recommendations**
   - Personalized article recommendations for users
   - "You might also like" suggestions
   - Trending articles
   - Articles needing attention (stale, low-rated)
   - Smart notifications for users

3. **Automated Article Health**
   - Scheduled job to identify stale articles
   - Email alerts to SMEs for articles needing review
   - Auto-archive articles with no views after 12 months
   - Quality score calculation
   - Health dashboard

4. **Multi-Tenant Support** (optional for side project)
   - Tenant isolation in database (TenantId column)
   - Tenant-specific configuration
   - Billing and usage tracking per tenant
   - Tenant admin portal

5. **Advanced Admin Dashboard**
   - System health monitoring
   - Azure cost tracking
   - User management (bulk operations)
   - Module and tag management
   - System configuration
   - Audit log viewer
   - Performance metrics

6. **Performance Optimization**
   - Database query optimization
   - Caching layer (Azure Cache for Redis)
   - CDN for static assets
   - Image optimization and lazy loading
   - API response compression
   - Connection pooling
   - Load testing and bottleneck identification

7. **API Management**
   - Azure API Management integration
   - Rate limiting per user/role
   - API versioning
   - Developer portal for API consumers
   - API analytics

### Architecture Components - Phase 5 Additions

```
┌─────────────────────────────────────────────────────────────┐
│                    CACHING & CDN LAYER                       │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │     Azure Cache for Redis                          │    │
│  │  - Article cache                                   │    │
│  │  - Search result cache                             │    │
│  │  - User session cache                              │    │
│  └────────────────────────────────────────────────────┘    │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │     Azure CDN                                      │    │
│  │  - Static assets (images, CSS, JS)                 │    │
│  │  - Global distribution                             │    │
│  └────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                  API MANAGEMENT LAYER                        │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │     Azure API Management                           │    │
│  │  - Rate limiting                                   │    │
│  │  - API versioning                                  │    │
│  │  - Authentication                                  │    │
│  │  - Developer portal                                │    │
│  │  - Analytics                                       │    │
│  └────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                    ANALYTICS LAYER                           │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │     Analytics Service                              │    │
│  │  - Usage tracking                                  │    │
│  │  - Performance metrics                             │    │
│  │  - Custom reports                                  │    │
│  │  - Recommendation engine                           │    │
│  └────────────────────────────────────────────────────┘    │
│                                                              │
│  ┌────────────────────────────────────────────────────┐    │
│  │     Azure Monitor / Application Insights           │    │
│  │  - Real-time monitoring                            │    │
│  │  - Alerting                                        │    │
│  │  - Distributed tracing                             │    │
│  │  - Custom metrics                                  │    │
│  └────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

### Azure Resources - Phase 5 Additions

| Resource | SKU/Tier | Purpose | Est. Monthly Cost |
|----------|----------|---------|-------------------|
| Azure Cache for Redis | Basic C0 (250 MB) | Caching layer | $16 |
| Azure CDN | Standard Microsoft | Static asset delivery | $10-20 |
| Azure API Management | Consumption | API gateway and management | $3.50/million calls |

**Phase 5 Additional Cost**: ~$29-39/month  
**Final Cumulative Total**: ~$311-421/month

### Work Breakdown

**Infrastructure:**
- [ ] Azure Cache for Redis
- [ ] Azure CDN
- [ ] Azure API Management
- [ ] Update Bicep templates with all optimizations

**Database:**
- [ ] Analytics tables (usage, search, recommendations)
- [ ] Multi-tenant schema updates (TenantId)
- [ ] Database indexing review and optimization
- [ ] Query performance tuning

**Backend API:**
- [ ] Caching service implementation
- [ ] Redis cache integration
- [ ] Analytics service
- [ ] Recommendation engine
- [ ] Article health monitoring service
- [ ] Multi-tenant middleware (if needed)
- [ ] API Management policies
- [ ] Performance profiling and optimization
- [ ] Load testing (JMeter or Azure Load Testing)

**Frontend:**
- [ ] Analytics dashboard
- [ ] Recommendation components
- [ ] Advanced admin dashboard
- [ ] Health monitoring dashboard
- [ ] Performance optimization (lazy loading, code splitting)
- [ ] CDN integration

**DevOps:**
- [ ] Production deployment with blue-green or canary
- [ ] Automated performance testing in pipeline
- [ ] Monitoring and alerting setup
- [ ] Disaster recovery plan and testing
- [ ] Documentation for operations

---

## Post-Launch: Continuous Improvement

### Potential Future Enhancements

1. **AI Agent Improvements**
   - Fine-tuning models on your specific content
   - Multi-language support
   - Voice-to-article (transcription integration)

2. **Integration Expansion**
   - Jira/Azure DevOps direct integration
   - ServiceNow integration
   - GitHub wiki integration
   - Confluence migration tool

3. **Advanced Features**
   - Mobile app (Xamarin or MAUI)
   - Offline mode for article viewing
   - Collaborative editing (real-time)
   - Article workflows (draft → review → approve → publish)
   - Custom taxonomy and metadata

4. **Enterprise Features**
   - SSO with multiple identity providers
   - Advanced security (data encryption, DLP)
   - Compliance reporting (SOC2, GDPR)
   - White-labeling for multi-tenant

---

## Success Metrics (KPIs)

**Phase 1:**
- 50+ articles created in first month
- <5 seconds average article creation time
- 80%+ user satisfaction with paste tool

**Phase 2:**
- 100+ articles from document uploads
- 90%+ accuracy in document extraction
- <2 rejected articles per 10 submitted

**Phase 3:**
- 200+ articles from Teams/Slack
- <2 seconds search response time
- 80%+ search result relevance (user feedback)

**Phase 4:**
- 1000+ articles imported from legacy sources
- 5+ automated release notes generated
- Zero data loss in imports

**Phase 5:**
- <500ms API response time (p95)
- 99.9% uptime
- <$400/month Azure costs (ongoing)

---

## Risk Management

| Risk | Impact | Mitigation |
|------|--------|------------|
| Azure OpenAI cost overrun | High | Implement token usage monitoring, caching, rate limiting |
| Poor AI extraction quality | Medium | Prompt engineering, user feedback loop, manual review workflow |
| Slow document processing | Medium | Async processing, progress indicators, batch optimization |
| Database performance at scale | Medium | Indexing strategy, query optimization, caching |
| User adoption challenges | High | Training materials, intuitive UI, gradual rollout |
| Security vulnerabilities | High | Regular security audits, penetration testing, dependency updates |

---

## Next Steps

1. **Review and Approve** this phased plan
2. **Setup Development Environment**
   - Azure subscription
   - Azure DevOps organization
   - Development team access
3. **Kickoff Phase 1**
   - Sprint planning
   - Bicep template development
   - Database schema design
   - API project setup

---

## Estimated Timeline

- **Phase 1**: Weeks 1-6
- **Phase 2**: Weeks 7-10
- **Phase 3**: Weeks 11-15
- **Phase 4**: Weeks 16-19
- **Phase 5**: Weeks 20-23

**Total Project Duration**: ~23 weeks (5.75 months)

---

## Azure Cost Summary by Phase

| Phase | Monthly Cost | Cumulative |
|-------|--------------|------------|
| Phase 1 | $86-141 | $86-141 |
| Phase 2 | +$31-65 | $117-206 |
| Phase 3 | +$80-86 | $197-292 |
| Phase 4 | +$85-90 | $282-382 |
| Phase 5 | +$29-39 | $311-421 |

**Target**: <$400/month  
**Projected**: $311-421/month (within or slightly above target, optimizable)

---

## Technology Stack Summary

**Frontend:**
- Blazor Server or WebAssembly
- Bootstrap or Tailwind CSS
- SignalR (for real-time updates)

**Backend:**
- .NET Core 8 Web API
- Entity Framework Core 8
- Azure SDK for .NET
- Hangfire or Azure Durable Functions (background jobs)

**AI/ML:**
- Azure OpenAI (GPT-4, text-embedding-ada-002)
- Azure Document Intelligence
- Azure AI Search

**Data:**
- Azure SQL Database
- Azure Blob Storage
- Azure Cache for Redis

**Integration:**
- Bot Framework SDK (Teams)
- Slack Bolt SDK
- MCP Protocol Implementation

**DevOps:**
- Azure DevOps (repos, pipelines, boards)
- Bicep (Infrastructure as Code)
- Azure Application Insights (monitoring)

**Security:**
- Azure AD / Azure AD B2C
- Azure Key Vault
- Managed Identities

---

**Document Version**: 1.0  
**Last Updated**: January 29, 2026  
**Author**: Claude & Project Team

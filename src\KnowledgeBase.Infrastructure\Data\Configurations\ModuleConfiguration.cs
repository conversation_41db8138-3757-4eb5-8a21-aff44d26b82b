using KnowledgeBase.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace KnowledgeBase.Infrastructure.Data.Configurations;

public class ModuleConfiguration : IEntityTypeConfiguration<Module>
{
    public void Configure(EntityTypeBuilder<Module> builder)
    {
        builder.ToTable("Modules");

        builder.HasKey(m => m.ModuleId);

        builder.Property(m => m.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(m => m.Description)
            .HasMaxLength(500);

        builder.Property(m => m.IsActive)
            .HasDefaultValue(true);

        builder.Property(m => m.DisplayOrder)
            .HasDefaultValue(0);

        builder.Property(m => m.CreatedDate)
            .HasDefaultValueSql("GETUTCDATE()");

        // Self-referencing relationship for parent/child modules
        builder.HasOne(m => m.ParentModule)
            .WithMany(m => m.ChildModules)
            .HasForeignKey(m => m.ParentModuleId)
            .OnDelete(DeleteBehavior.Restrict);

        // SME relationship
        builder.HasOne(m => m.SME)
            .WithMany(u => u.SMEModules)
            .HasForeignKey(m => m.SMEUserId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}

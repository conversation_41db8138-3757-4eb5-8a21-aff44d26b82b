-- Knowledge Base Platform Seed Data
-- Version: 1.0.0
-- Created: 2026-01-29

-- =============================================
-- Seed Users
-- =============================================
INSERT INTO Users (UserId, Email, Name, Role, IsActive, CreatedDate)
VALUES
    ('********-0000-0000-0000-************', '<EMAIL>', 'System Administrator', 'admin', 1, GETUTCDATE()),
    ('********-0000-0000-0000-********0002', '<EMAIL>', '<PERSON>', 'power_user', 1, GETUTCDATE()),
    ('********-0000-0000-0000-********0003', '<EMAIL>', '<PERSON>', 'power_user', 1, GETUTCDATE()),
    ('********-0000-0000-0000-********0004', '<EMAIL>', '<PERSON>', 'user', 1, GETUTCDATE()),
    ('********-0000-0000-0000-********0005', '<EMAIL>', 'Alice Johnson', 'user', 1, GETUTCDATE());

-- =============================================
-- Seed Modules
-- =============================================
INSERT INTO Modules (ModuleId, Name, Description, SMEUserId, IsActive, DisplayOrder, CreatedDate)
VALUES
    ('********-0000-0000-0000-************', 'Authentication', 'User authentication, SSO, and security features', '********-0000-0000-0000-********0002', 1, 1, GETUTCDATE()),
    ('********-0000-0000-0000-********0002', 'File Management', 'File upload, storage, and document handling', '********-0000-0000-0000-********0003', 1, 2, GETUTCDATE()),
    ('********-0000-0000-0000-********0003', 'User Management', 'User accounts, roles, and permissions', '********-0000-0000-0000-********0002', 1, 3, GETUTCDATE()),
    ('********-0000-0000-0000-********0004', 'Reporting', 'Analytics, dashboards, and data exports', '********-0000-0000-0000-********0003', 1, 4, GETUTCDATE()),
    ('********-0000-0000-0000-********0005', 'API Integration', 'REST API endpoints and webhook configuration', '********-0000-0000-0000-********0002', 1, 5, GETUTCDATE()),
    ('********-0000-0000-0000-********0006', 'Platform', 'General platform features and settings', '********-0000-0000-0000-************', 1, 6, GETUTCDATE());

-- =============================================
-- Seed Sample Articles
-- =============================================

-- How-To Article: Configure SSO
INSERT INTO Articles (
    ArticleId, ArticleType, Title, Summary, Content, ModuleId, Category, Tags,
    EstimatedTimeMinutes, Difficulty, Status, CreatedByUserId, LastModifiedByUserId, CreatedDate, LastModifiedDate
)
VALUES (
    '*************-0000-0000-************',
    'how_to',
    'How to Configure Single Sign-On (SSO) in Admin Portal',
    'Enable SSO authentication for your organization using Azure AD, Okta, or Google Workspace in under 10 minutes.',
    '{
        "prerequisites": [
            {"item": "Admin-level access to the platform", "type": "permission"},
            {"item": "Identity provider credentials (Tenant ID and Client Secret for Azure AD)", "type": "credential"},
            {"item": "Access to your identity provider admin console", "type": "access"}
        ],
        "steps": [
            {"step_number": 1, "title": "Access the Admin Portal", "action": "Navigate to admin.example.com and log in with your admin credentials", "expected_outcome": "You should see the admin dashboard", "screenshot_recommended": true},
            {"step_number": 2, "title": "Navigate to Security Settings", "action": "Click on Settings in the left sidebar, then select the Security tab", "expected_outcome": "Security configuration options are displayed", "screenshot_recommended": true},
            {"step_number": 3, "title": "Open SSO Configuration", "action": "Click on SSO Configuration button", "expected_outcome": "SSO setup wizard opens", "screenshot_recommended": true},
            {"step_number": 4, "title": "Select Identity Provider", "action": "Choose your identity provider from the dropdown: Azure AD, Okta, or Google Workspace", "expected_outcome": "Provider-specific configuration fields appear", "screenshot_recommended": true},
            {"step_number": 5, "title": "Enter Provider Credentials", "action": "For Azure AD: Enter your Tenant ID in the first field and Client Secret in the second field", "expected_outcome": "Credential fields are populated", "screenshot_recommended": true, "warning": "Keep your Client Secret secure - do not share it"},
            {"step_number": 6, "title": "Test the Connection", "action": "Click Test Connection button to verify the configuration", "expected_outcome": "Green checkmark appears with message Connection successful", "screenshot_recommended": true, "warning": "If test fails, double-check your credentials and ensure your IP is whitelisted in Azure AD"},
            {"step_number": 7, "title": "Enable SSO", "action": "Toggle the Enable SSO for Organization switch to ON, then click Save Changes", "expected_outcome": "Confirmation message: SSO has been enabled", "screenshot_recommended": true, "warning": "All users will be required to use SSO on their next login"}
        ],
        "validation": {"title": "Verify SSO is Working", "steps": ["Log out of your current session", "Navigate to the login page", "You should be automatically redirected to your identity provider", "After authenticating, you should be logged into the platform"]},
        "next_steps": ["Configure user provisioning (SCIM)", "Set up multi-factor authentication (MFA)", "Create SSO troubleshooting guide for end users"]
    }',
    '********-0000-0000-0000-************',
    'configuration',
    '["sso", "authentication", "azure-ad", "okta", "security", "admin"]',
    10,
    'intermediate',
    'published',
    '********-0000-0000-0000-********0002',
    '********-0000-0000-0000-********0002',
    GETUTCDATE(),
    GETUTCDATE()
);

-- Troubleshooting Article: File Upload Error
INSERT INTO Articles (
    ArticleId, ArticleType, Title, Summary, Content, ModuleId, Category, Tags,
    EstimatedTimeMinutes, Difficulty, Status, CreatedByUserId, LastModifiedByUserId, CreatedDate, LastModifiedDate
)
VALUES (
    '*************-0000-0000-********0002',
    'troubleshooting',
    'File Upload Fails - "Upload Failed - File Too Large" Error',
    'Users unable to upload files larger than 5MB after recent deployment due to server upload limit misconfiguration.',
    '{
        "symptoms": [
            "Error message: Upload failed - file too large",
            "Affects files larger than 4-5MB",
            "Occurs across all browsers (Chrome, Firefox, Safari)",
            "Started immediately after version 3.1.5 deployment",
            "Smaller files upload successfully"
        ],
        "affected_versions": ["3.1.5"],
        "severity": "high",
        "impact": "Users cannot upload files >5MB, blocking critical workflows",
        "root_causes": [
            {
                "cause": "IIS upload size limit not updated during deployment",
                "likelihood": "confirmed",
                "technical_details": "web.config maxRequestLength (4MB) and maxAllowedContentLength (4MB) not increased to match application requirements (50MB)",
                "solution": {
                    "type": "configuration_change",
                    "steps": [
                        {"step": 1, "action": "Open web.config file on the application server"},
                        {"step": 2, "action": "Update maxRequestLength to 51200", "code": "<httpRuntime maxRequestLength=\"51200\" executionTimeout=\"3600\" />"},
                        {"step": 3, "action": "Update maxAllowedContentLength to 52428800", "code": "<requestLimits maxAllowedContentLength=\"52428800\" />"},
                        {"step": 4, "action": "Restart IIS application pool"},
                        {"step": 5, "action": "Test upload with 10MB file to verify fix"}
                    ],
                    "time_to_resolve": "15 minutes"
                }
            }
        ],
        "workarounds": [
            {"description": "Compress files before uploading", "effectiveness": "temporary"},
            {"description": "Split large files into smaller chunks", "effectiveness": "temporary"}
        ],
        "prevention": ["Add upload limit configuration to deployment checklist", "Include upload size validation in integration tests"]
    }',
    '********-0000-0000-0000-********0002',
    'error_resolution',
    '["file-upload", "error", "deployment", "iis", "configuration"]',
    15,
    'intermediate',
    'published',
    '********-0000-0000-0000-********0003',
    '********-0000-0000-0000-********0003',
    GETUTCDATE(),
    GETUTCDATE()
);

-- FAQ Article: Pricing
INSERT INTO Articles (
    ArticleId, ArticleType, Title, Summary, Content, ModuleId, Category, Tags,
    Status, CreatedByUserId, LastModifiedByUserId, CreatedDate, LastModifiedDate
)
VALUES (
    '*************-0000-0000-********0003',
    'faq',
    'FAQ - Pricing and Billing',
    'Answers to frequently asked questions about pricing plans, payment methods, upgrades, and refunds.',
    '{
        "categories": [
            {
                "category_name": "Payment Methods",
                "questions": [
                    {"question": "What payment methods do you accept?", "answer": "We accept all major credit cards (Visa, Mastercard, American Express), PayPal, and wire transfers for enterprise customers.", "tags": ["payment", "billing"]}
                ]
            },
            {
                "category_name": "Plan Changes",
                "questions": [
                    {"question": "Can I change my plan anytime?", "answer": "Yes, you can upgrade or downgrade your plan at any time from your account settings. Changes take effect immediately.", "tags": ["upgrade", "downgrade"]},
                    {"question": "What happens if I exceed my user limit?", "answer": "We will send you a notification when you reach 90% of your user limit. You have 7 days to upgrade or remove users.", "tags": ["user-limit"]}
                ]
            },
            {
                "category_name": "Refunds & Trials",
                "questions": [
                    {"question": "Do you offer refunds?", "answer": "Yes, we offer a 30-day money-back guarantee for all plans, no questions asked.", "tags": ["refund"]},
                    {"question": "Is there a free trial?", "answer": "Yes! All plans include a 14-day free trial with full access to all features. No credit card required.", "tags": ["trial", "free"]}
                ]
            }
        ]
    }',
    '********-0000-0000-0000-********0006',
    'faq',
    '["faq", "pricing", "billing", "payment", "refund", "trial"]',
    'published',
    '********-0000-0000-0000-********0002',
    '********-0000-0000-0000-********0002',
    GETUTCDATE(),
    GETUTCDATE()
);

-- Update view counts for sample data
UPDATE Articles SET ViewCount = 156, HelpfulCount = 42, NotHelpfulCount = 3 WHERE ArticleId = '*************-0000-0000-************';
UPDATE Articles SET ViewCount = 89, HelpfulCount = 28, NotHelpfulCount = 2 WHERE ArticleId = '*************-0000-0000-********0002';
UPDATE Articles SET ViewCount = 234, HelpfulCount = 67, NotHelpfulCount = 5 WHERE ArticleId = '*************-0000-0000-********0003';

-- Add related articles
INSERT INTO RelatedArticles (ArticleId, RelatedArticleId, RelationshipType, RelevanceScore, CreatedDate)
VALUES
    ('*************-0000-0000-************', '*************-0000-0000-********0002', 'related', 0.65, GETUTCDATE());

GO

PRINT 'Seed data inserted successfully.';
GO

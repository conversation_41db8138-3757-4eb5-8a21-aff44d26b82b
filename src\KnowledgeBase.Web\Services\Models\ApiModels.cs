namespace KnowledgeBase.Web.Services.Models;

/// <summary>
/// Standard API response wrapper.
/// </summary>
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public T? Data { get; set; }
    public string? Message { get; set; }
    public List<string>? Errors { get; set; }
}

/// <summary>
/// Paginated response wrapper.
/// </summary>
public class PagedResponse<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasPreviousPage { get; set; }
    public bool HasNextPage { get; set; }
}

/// <summary>
/// Module data transfer object.
/// </summary>
public class ModuleDto
{
    public Guid ModuleId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? SMEName { get; set; }
    public Guid? SMEUserId { get; set; }
    public Guid? ParentModuleId { get; set; }
    public string? ParentModuleName { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public int ArticleCount { get; set; }
}

/// <summary>
/// Article data transfer object.
/// </summary>
public class ArticleDto
{
    public Guid ArticleId { get; set; }
    public string ArticleType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public object? Content { get; set; }
    public Guid ModuleId { get; set; }
    public string ModuleName { get; set; } = string.Empty;
    public string? Category { get; set; }
    public List<string> Tags { get; set; } = new();
    public int? EstimatedTimeMinutes { get; set; }
    public string? Difficulty { get; set; }
    public List<string> AppliesTo { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public string CreatedByName { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public string LastModifiedByName { get; set; } = string.Empty;
    public DateTime LastModifiedDate { get; set; }
    public DateTime? LastReviewedDate { get; set; }
    public int ViewCount { get; set; }
    public int HelpfulCount { get; set; }
    public int NotHelpfulCount { get; set; }
    public decimal? Rating { get; set; }
    public int Version { get; set; }
    public List<RelatedArticleDto> RelatedArticles { get; set; } = new();
    public List<TicketReferenceDto> TicketReferences { get; set; } = new();
}

/// <summary>
/// Lightweight article DTO for list views.
/// </summary>
public class ArticleSummaryDto
{
    public Guid ArticleId { get; set; }
    public string ArticleType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public string ModuleName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public DateTime LastModifiedDate { get; set; }
    public int ViewCount { get; set; }
    public decimal? Rating { get; set; }
}

public class RelatedArticleDto
{
    public Guid ArticleId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string RelationshipType { get; set; } = string.Empty;
    public decimal? RelevanceScore { get; set; }
}

public class TicketReferenceDto
{
    public string TicketId { get; set; } = string.Empty;
    public string TicketSystem { get; set; } = string.Empty;
    public string? TicketUrl { get; set; }
}

/// <summary>
/// Search result item.
/// </summary>
public class SearchResultDto
{
    public Guid ArticleId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Summary { get; set; }
    public string ArticleType { get; set; } = string.Empty;
    public string? ModuleName { get; set; }
    public string? Category { get; set; }
    public List<string>? Tags { get; set; }
    public int ViewCount { get; set; }
    public decimal? Rating { get; set; }
    public double Score { get; set; }
    public DateTime LastModifiedDate { get; set; }
}

/// <summary>
/// Dashboard statistics.
/// </summary>
public class DashboardStats
{
    public int TotalArticles { get; set; }
    public int PublishedArticles { get; set; }
    public int DraftArticles { get; set; }
    public int PendingReview { get; set; }
    public int TotalModules { get; set; }
    public int TotalViews { get; set; }
}

/// <summary>
/// Article version history entry.
/// </summary>
public class ArticleVersionDto
{
    public Guid VersionId { get; set; }
    public int VersionNumber { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? ChangeDescription { get; set; }
    public string ModifiedByName { get; set; } = string.Empty;
    public DateTime ModifiedDate { get; set; }
}

/// <summary>
/// Response from reindex operation.
/// </summary>
public class ReindexResponse
{
    public int ArticlesIndexed { get; set; }
    public string Message { get; set; } = string.Empty;
}

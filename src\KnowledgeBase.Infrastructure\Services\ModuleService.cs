using KnowledgeBase.Core.Entities;
using KnowledgeBase.Core.Interfaces;
using KnowledgeBase.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace KnowledgeBase.Infrastructure.Services;

/// <summary>
/// Service implementation for module management operations.
/// </summary>
public class ModuleService : IModuleService
{
    private readonly KnowledgeBaseContext _context;
    private readonly ILogger<ModuleService> _logger;

    public ModuleService(KnowledgeBaseContext context, ILogger<ModuleService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<List<Module>> GetAllAsync(bool includeInactive = false, CancellationToken cancellationToken = default)
    {
        var query = _context.Modules
            .Include(m => m.SME)
            .Include(m => m.ParentModule)
            .AsQueryable();

        if (!includeInactive)
        {
            query = query.Where(m => m.IsActive);
        }

        return await query
            .OrderBy(m => m.DisplayOrder)
            .ThenBy(m => m.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<Module?> GetByIdAsync(Guid moduleId, CancellationToken cancellationToken = default)
    {
        return await _context.Modules
            .Include(m => m.SME)
            .Include(m => m.ParentModule)
            .Include(m => m.ChildModules)
            .FirstOrDefaultAsync(m => m.ModuleId == moduleId, cancellationToken);
    }

    public async Task<Module> CreateAsync(Module module, CancellationToken cancellationToken = default)
    {
        module.ModuleId = Guid.NewGuid();
        module.CreatedDate = DateTime.UtcNow;
        module.IsActive = true;

        _context.Modules.Add(module);
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Created module {ModuleId}: {Name}", module.ModuleId, module.Name);

        return module;
    }

    public async Task<Module> UpdateAsync(Module module, CancellationToken cancellationToken = default)
    {
        var existing = await _context.Modules.FindAsync(new object[] { module.ModuleId }, cancellationToken);
        if (existing == null)
        {
            throw new InvalidOperationException($"Module {module.ModuleId} not found.");
        }

        existing.Name = module.Name;
        existing.Description = module.Description;
        existing.SMEUserId = module.SMEUserId;
        existing.ParentModuleId = module.ParentModuleId;
        existing.DisplayOrder = module.DisplayOrder;
        existing.IsActive = module.IsActive;

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Updated module {ModuleId}: {Name}", existing.ModuleId, existing.Name);

        return existing;
    }

    public async Task<bool> SetActiveStatusAsync(Guid moduleId, bool isActive, CancellationToken cancellationToken = default)
    {
        var module = await _context.Modules.FindAsync(new object[] { moduleId }, cancellationToken);
        if (module == null)
        {
            return false;
        }

        module.IsActive = isActive;
        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Set module {ModuleId} active status to {IsActive}", moduleId, isActive);

        return true;
    }

    public async Task<int> GetArticleCountAsync(Guid moduleId, CancellationToken cancellationToken = default)
    {
        return await _context.Articles
            .Where(a => a.ModuleId == moduleId && !a.IsDeleted)
            .CountAsync(cancellationToken);
    }
}

# Azure Pipeline for API Deployment
# Builds, tests, and deploys the KnowledgeBase.Api project
#
# PREREQUISITES:
# 1. Create a Service Connection named 'Azure-ServiceConnection' (Azure Resource Manager)
# 2. Run the infrastructure pipeline first to create Azure resources

trigger:
  branches:
    include:
      - master
  paths:
    include:
      - src/KnowledgeBase.Api/**
      - src/KnowledgeBase.Core/**
      - src/KnowledgeBase.Infrastructure/**

pr:
  branches:
    include:
      - main
  paths:
    include:
      - src/KnowledgeBase.Api/**
      - src/KnowledgeBase.Core/**
      - src/KnowledgeBase.Infrastructure/**

parameters:
  - name: environment
    displayName: 'Environment'
    type: string
    default: 'dev'
    values:
      - dev
      - staging
      - prod

  - name: runTests
    displayName: 'Run Unit Tests'
    type: boolean
    default: false

variables:
  - name: buildConfiguration
    value: 'Release'
  - name: dotnetVersion
    value: '8.0.x'
  - name: azureServiceConnection
    value: 'Azure-ServiceConnection'
  - name: resourceGroupName
    value: 'rg-meshworkskb-${{ parameters.environment }}'
  - name: apiAppName
    value: 'api-meshworkskb-${{ parameters.environment }}'
  - name: projectPath
    value: 'src/KnowledgeBase.Api/KnowledgeBase.Api.csproj'
  - name: testProjectPath
    value: 'tests/**/*.csproj'
  - name: aspnetEnvironment
    ${{ if eq(parameters.environment, 'prod') }}:
      value: 'Production'
    ${{ else }}:
      value: 'Development'

stages:
  - stage: Build
    displayName: 'Build and Test'
    jobs:
      - job: BuildAndTest
        displayName: 'Build and Test API'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - checkout: self

          - task: UseDotNet@2
            displayName: 'Install .NET SDK'
            inputs:
              version: $(dotnetVersion)
              includePreviewVersions: false

          - task: DotNetCoreCLI@2
            displayName: 'Restore NuGet Packages'
            inputs:
              command: 'restore'
              projects: '**/*.csproj'
              feedsToUse: 'select'

          - task: DotNetCoreCLI@2
            displayName: 'Build Solution'
            inputs:
              command: 'build'
              projects: '**/*.csproj'
              arguments: '--configuration $(buildConfiguration) --no-restore'

          - ${{ if eq(parameters.runTests, true) }}:
            - task: DotNetCoreCLI@2
              displayName: 'Run Unit Tests'
              inputs:
                command: 'test'
                projects: 'tests/**/*.csproj'
                arguments: '--configuration $(buildConfiguration) --collect:"XPlat Code Coverage" --results-directory $(Agent.TempDirectory)/TestResults'
              continueOnError: true

            - task: PublishCodeCoverageResults@2
              displayName: 'Publish Code Coverage'
              inputs:
                summaryFileLocation: '$(Agent.TempDirectory)/TestResults/**/coverage.cobertura.xml'
              condition: succeededOrFailed()

          - task: DotNetCoreCLI@2
            displayName: 'Publish API Project'
            inputs:
              command: 'publish'
              publishWebProjects: false
              projects: '$(projectPath)'
              arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/api --no-build'
              zipAfterPublish: true

          - task: DotNetCoreCLI@2
            displayName: 'Restore EF Core Tools'
            inputs:
              command: 'custom'
              custom: 'tool'
              arguments: 'restore'

          - task: DotNetCoreCLI@2
            displayName: 'Create Migration Bundle'
            inputs:
              command: 'custom'
              custom: 'dotnet-ef'
              arguments: 'migrations bundle --project src/KnowledgeBase.Infrastructure/KnowledgeBase.Infrastructure.csproj --startup-project $(projectPath) --configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/migrations/efbundle --self-contained --force'

          - task: PublishPipelineArtifact@1
            displayName: 'Publish API Artifact'
            inputs:
              targetPath: '$(Build.ArtifactStagingDirectory)/api'
              artifact: 'api-drop'
              publishLocation: 'pipeline'

          - task: PublishPipelineArtifact@1
            displayName: 'Publish Migration Bundle'
            inputs:
              targetPath: '$(Build.ArtifactStagingDirectory)/migrations'
              artifact: 'migrations-drop'
              publishLocation: 'pipeline'

  - stage: Deploy
    displayName: 'Deploy API'
    dependsOn: Build
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
    jobs:
      - job: DeployApi
        displayName: 'Deploy to Azure App Service'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - download: current
            artifact: api-drop
            displayName: 'Download API Artifact'

          - download: current
            artifact: migrations-drop
            displayName: 'Download Migration Bundle'

          - task: Bash@3
            displayName: 'Run Database Migrations'
            continueOnError: true
            inputs:
              targetType: 'inline'
              script: |
                if [ -n "$SQL_CONNECTION_STRING" ]; then
                  echo "Running database migrations..."
                  chmod +x $(Pipeline.Workspace)/migrations-drop/efbundle
                  $(Pipeline.Workspace)/migrations-drop/efbundle --connection "$SQL_CONNECTION_STRING"
                  echo "Database migrations completed successfully"
                else
                  echo "⚠ SqlConnectionString variable not set - skipping migrations"
                  echo "To run migrations, add SqlConnectionString as a secret pipeline variable"
                fi
            env:
              SQL_CONNECTION_STRING: $(SqlConnectionString)

          - task: AzureWebApp@1
            displayName: 'Deploy to Azure App Service'
            inputs:
              azureSubscription: $(azureServiceConnection)
              appType: 'webAppLinux'
              appName: $(apiAppName)
              package: '$(Pipeline.Workspace)/api-drop/**/*.zip'
              runtimeStack: 'DOTNETCORE|8.0'

          - task: AzureCLI@2
            displayName: 'Configure App Settings'
            inputs:
              azureSubscription: $(azureServiceConnection)
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az webapp config appsettings set \
                  --resource-group $(resourceGroupName) \
                  --name $(apiAppName) \
                  --settings \
                    "ASPNETCORE_ENVIRONMENT=$(aspnetEnvironment)"

                echo "✓ App settings configured"
                echo "NOTE: Configure ConnectionStrings__DefaultConnection in Azure Portal > App Service > Configuration"

          - task: AzureCLI@2
            displayName: 'Configure CORS'
            inputs:
              azureSubscription: $(azureServiceConnection)
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                WEB_APP_URL="https://web-meshworkskb-${{ parameters.environment }}.azurewebsites.net"

                az webapp cors add \
                  --resource-group $(resourceGroupName) \
                  --name $(apiAppName) \
                  --allowed-origins "$WEB_APP_URL" "https://localhost:7002"

  - stage: Verify
    displayName: 'Verify Deployment'
    dependsOn: Deploy
    condition: succeeded()
    jobs:
      - job: HealthCheck
        displayName: 'API Health Check'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: Bash@3
            displayName: 'Verify API Health'
            continueOnError: true
            inputs:
              targetType: 'inline'
              script: |
                API_URL="https://$(apiAppName).azurewebsites.net"

                echo "Checking API health at $API_URL..."
                sleep 30

                for i in {1..3}; do
                  HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/health" 2>/dev/null || echo "000")

                  if [ "$HTTP_STATUS" -eq "200" ]; then
                    echo "✓ API health check passed (attempt $i)"
                    exit 0
                  fi

                  echo "Attempt $i: Status $HTTP_STATUS, retrying..."
                  sleep 10
                done

                echo "⚠ API health check failed - this may be expected if infrastructure is not yet deployed"
                echo "Run the infrastructure pipeline first, then redeploy the API"

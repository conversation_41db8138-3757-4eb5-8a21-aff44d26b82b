using System.ComponentModel.DataAnnotations;
using KnowledgeBase.Core.Enums;

namespace KnowledgeBase.Core.Entities;

/// <summary>
/// Represents a relationship between two articles.
/// </summary>
public class RelatedArticle
{
    [Key]
    public Guid Id { get; set; }

    public Guid ArticleId { get; set; }

    public Guid RelatedArticleId { get; set; }

    public RelationshipType RelationshipType { get; set; } = RelationshipType.Related;

    /// <summary>
    /// AI-generated relevance score (0.00 - 1.00)
    /// </summary>
    public decimal? RelevanceScore { get; set; }

    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    public Guid? CreatedByUserId { get; set; }

    // Navigation properties
    public virtual Article Article { get; set; } = null!;
    public virtual Article Related { get; set; } = null!;
    public virtual User? CreatedBy { get; set; }
}
